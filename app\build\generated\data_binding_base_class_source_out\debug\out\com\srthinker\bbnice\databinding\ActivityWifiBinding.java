// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWifiBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final View divider;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvWifiList;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final SwitchCompat switchWifi;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAvailableNetworks;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvWifiStatusLabel;

  @NonNull
  public final ConstraintLayout wifiStatusContainer;

  private ActivityWifiBinding(@NonNull ConstraintLayout rootView, @NonNull View divider,
      @NonNull ConstraintLayout main, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView rvWifiList, @NonNull SwipeRefreshLayout swipeRefresh,
      @NonNull SwitchCompat switchWifi, @NonNull Toolbar toolbar,
      @NonNull TextView tvAvailableNetworks, @NonNull TextView tvEmptyState,
      @NonNull TextView tvWifiStatusLabel, @NonNull ConstraintLayout wifiStatusContainer) {
    this.rootView = rootView;
    this.divider = divider;
    this.main = main;
    this.progressBar = progressBar;
    this.rvWifiList = rvWifiList;
    this.swipeRefresh = swipeRefresh;
    this.switchWifi = switchWifi;
    this.toolbar = toolbar;
    this.tvAvailableNetworks = tvAvailableNetworks;
    this.tvEmptyState = tvEmptyState;
    this.tvWifiStatusLabel = tvWifiStatusLabel;
    this.wifiStatusContainer = wifiStatusContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWifiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWifiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_wifi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWifiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_wifi_list;
      RecyclerView rvWifiList = ViewBindings.findChildViewById(rootView, id);
      if (rvWifiList == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.switch_wifi;
      SwitchCompat switchWifi = ViewBindings.findChildViewById(rootView, id);
      if (switchWifi == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_available_networks;
      TextView tvAvailableNetworks = ViewBindings.findChildViewById(rootView, id);
      if (tvAvailableNetworks == null) {
        break missingId;
      }

      id = R.id.tv_empty_state;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_wifi_status_label;
      TextView tvWifiStatusLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvWifiStatusLabel == null) {
        break missingId;
      }

      id = R.id.wifi_status_container;
      ConstraintLayout wifiStatusContainer = ViewBindings.findChildViewById(rootView, id);
      if (wifiStatusContainer == null) {
        break missingId;
      }

      return new ActivityWifiBinding((ConstraintLayout) rootView, divider, main, progressBar,
          rvWifiList, swipeRefresh, switchWifi, toolbar, tvAvailableNetworks, tvEmptyState,
          tvWifiStatusLabel, wifiStatusContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
