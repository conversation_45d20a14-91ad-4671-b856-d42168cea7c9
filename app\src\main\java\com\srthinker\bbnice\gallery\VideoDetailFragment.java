package com.srthinker.bbnice.gallery;

import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.VideoView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.srthinker.bbnice.R;

/**
 * 视频详情Fragment，用于播放视频
 */
public class VideoDetailFragment extends Fragment {
    private static final String ARG_MEDIA_ITEM = "media_item";

    private MediaItem mediaItem;
    private VideoView videoView;
    private ProgressBar progressBar;
    private ImageButton btnBack;
    private ImageButton btnPlayPause;
    private SeekBar seekBar;
    private TextView tvDuration;
    private Handler handler;
    private Runnable updateSeekBarRunnable;
    private boolean isPlaying = false;

    /**
     * 创建VideoDetailFragment实例
     * @param mediaItem 媒体项
     * @return Fragment实例
     */
    public static VideoDetailFragment newInstance(MediaItem mediaItem) {
        VideoDetailFragment fragment = new VideoDetailFragment();
        Bundle args = new Bundle();
        args.putParcelable(ARG_MEDIA_ITEM, mediaItem);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mediaItem = getArguments().getParcelable(ARG_MEDIA_ITEM);
        }
        handler = new Handler();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_video_detail, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        videoView = view.findViewById(R.id.video_view);
        progressBar = view.findViewById(R.id.progress_bar);
        btnBack = view.findViewById(R.id.btn_back);
        btnPlayPause = view.findViewById(R.id.btn_play_pause);
        seekBar = view.findViewById(R.id.seek_bar);
        tvDuration = view.findViewById(R.id.tv_duration);

        // 设置返回按钮点击事件
        btnBack.setOnClickListener(v -> {
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });

        // 设置播放/暂停按钮点击事件
        btnPlayPause.setOnClickListener(v -> {
            if (isPlaying) {
                pauseVideo();
            } else {
                playVideo();
            }
        });

        // 设置进度条变化监听器
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    videoView.seekTo(progress);
                    updateDurationText();
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                pauseVideo();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                playVideo();
            }
        });

        // 加载视频
        loadVideo();
    }

    /**
     * 加载视频
     */
    private void loadVideo() {
        if (mediaItem == null) return;

        progressBar.setVisibility(View.VISIBLE);

        // 设置视频路径
        videoView.setVideoURI(mediaItem.getUri());

        // 设置准备完成监听器
        videoView.setOnPreparedListener(mp -> {
            progressBar.setVisibility(View.GONE);
            seekBar.setMax(videoView.getDuration());
            updateDurationText();
            
            // 创建更新进度条的Runnable
            updateSeekBarRunnable = new Runnable() {
                @Override
                public void run() {
                    if (videoView.isPlaying()) {
                        seekBar.setProgress(videoView.getCurrentPosition());
                        updateDurationText();
                        handler.postDelayed(this, 1000);
                    }
                }
            };
        });

        // 设置完成监听器
        videoView.setOnCompletionListener(mp -> {
            btnPlayPause.setImageResource(android.R.drawable.ic_media_play);
            isPlaying = false;
            seekBar.setProgress(0);
            videoView.seekTo(0);
            updateDurationText();
        });

        // 设置错误监听器
        videoView.setOnErrorListener((mp, what, extra) -> {
            progressBar.setVisibility(View.GONE);
            return false;
        });
    }

    /**
     * 播放视频
     */
    private void playVideo() {
        videoView.start();
        btnPlayPause.setImageResource(android.R.drawable.ic_media_pause);
        isPlaying = true;
        handler.post(updateSeekBarRunnable);
    }

    /**
     * 暂停视频
     */
    private void pauseVideo() {
        videoView.pause();
        btnPlayPause.setImageResource(android.R.drawable.ic_media_play);
        isPlaying = false;
        handler.removeCallbacks(updateSeekBarRunnable);
    }

    /**
     * 更新时长文本
     */
    private void updateDurationText() {
        int currentPosition = videoView.getCurrentPosition() / 1000;
        int duration = videoView.getDuration() / 1000;
        
        int currentMinutes = currentPosition / 60;
        int currentSeconds = currentPosition % 60;
        
        int totalMinutes = duration / 60;
        int totalSeconds = duration % 60;
        
        String timeText = String.format("%02d:%02d / %02d:%02d",
                currentMinutes, currentSeconds, totalMinutes, totalSeconds);
        
        tvDuration.setText(timeText);
    }

    @Override
    public void onPause() {
        super.onPause();
        pauseVideo();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacks(updateSeekBarRunnable);
    }
}
