package com.srthinker.bbnice.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * 聊天消息DAO接口，定义数据库操作
 */
@Dao
public interface ChatMessageDao {
    
    /**
     * 插入聊天消息
     * @param message 聊天消息实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(ChatMessageEntity message);
    
    /**
     * 更新聊天消息
     * @param message 聊天消息实体
     * @return 更新的行数
     */
    @Update
    int update(ChatMessageEntity message);
    
    /**
     * 根据消息ID更新消息内容
     * @param messageId 消息ID
     * @param content 新内容
     * @return 更新的行数
     */
    @Query("UPDATE chat_messages SET content = :content WHERE messageId = :messageId")
    int updateContent(String messageId, String content);
    
    /**
     * 标记消息为已上传
     * @param messageId 消息ID
     * @return 更新的行数
     */
    @Query("UPDATE chat_messages SET isUploaded = 1 WHERE messageId = :messageId")
    int markAsUploaded(String messageId);
    
    /**
     * 获取指定房间类型的最新消息
     * @param roomType 房间类型
     * @param limit 限制数量
     * @return 消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE roomType = :roomType ORDER BY timestamp ASC LIMIT :limit")
    List<ChatMessageEntity> getLatestMessages(String roomType, int limit);
    
    /**
     * 分页获取消息
     * @param roomType 房间类型
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE roomType = :roomType ORDER BY timestamp ASC LIMIT :limit OFFSET :offset")
    List<ChatMessageEntity> getMessagesByPage(String roomType, int limit, int offset);
    
    /**
     * 获取未上传的消息
     * @param limit 限制数量
     * @return 未上传的消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE isUploaded = 0 ORDER BY timestamp ASC LIMIT :limit")
    List<ChatMessageEntity> getNotUploadedMessages(int limit);
    
    /**
     * 获取指定时间范围内的消息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE timestamp >= :startTime AND timestamp < :endTime ORDER BY timestamp ASC")
    List<ChatMessageEntity> getMessagesByTimeRange(long startTime, long endTime);
}
