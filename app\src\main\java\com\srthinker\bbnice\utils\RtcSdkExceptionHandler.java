package com.srthinker.bbnice.utils;

import android.util.Log;

/**
 * RTC SDK异常处理工具类
 * 用于处理字节跳动RTC SDK的已知问题
 */
public class RtcSdkExceptionHandler {
    private static final String TAG = "RtcSdkExceptionHandler";

    /**
     * 安全执行可能抛出RTC SDK异常的代码
     * @param runnable 要执行的代码
     * @param description 操作描述，用于日志
     */
    public static void safeExecute(Runnable runnable, String description) {
        try {
            runnable.run();
        } catch (RuntimeException e) {
            if (isRtcSdkKnownIssue(e)) {
                Log.w(TAG, "Caught known RTC SDK issue during " + description + ": " + e.getMessage());
                // 忽略已知的RTC SDK问题
            } else {
                // 重新抛出其他异常
                throw e;
            }
        } catch (Exception e) {
            if (isRtcSdkKnownIssue(e)) {
                Log.w(TAG, "Caught known RTC SDK issue during " + description + ": " + e.getMessage());
                // 忽略已知的RTC SDK问题
            } else {
                // 包装为RuntimeException重新抛出
                throw new RuntimeException("Unexpected exception during " + description, e);
            }
        }
    }

    /**
     * 检查是否是RTC SDK的已知问题
     * @param throwable 异常对象
     * @return 是否是已知问题
     */
    public static boolean isRtcSdkKnownIssue(Throwable throwable) {
        if (throwable == null) return false;

        String message = throwable.getMessage();
        String stackTrace = Log.getStackTraceString(throwable);

        // 检查SmartModeStatus异常
        if (throwable instanceof android.provider.Settings.SettingNotFoundException) {
            return message != null && message.contains("SmartModeStatus");
        }

        // 检查是否来自RTC SDK的电源监控模块
        if (stackTrace.contains("RXPowerMonitorAndroid.getBatterySaveStatus") ||
            stackTrace.contains("com.bytedance.realx.base.RXPowerMonitorAndroid") ||
            stackTrace.contains("com.ss.bytertc")) {
            return true;
        }

        return false;
    }

    /**
     * 包装RTC SDK调用，自动处理已知异常
     * @param rtcOperation RTC操作
     * @param operationName 操作名称
     * @param <T> 返回类型
     * @return 操作结果，如果发生已知异常则返回null
     */
    public static <T> T safeRtcCall(RtcOperation<T> rtcOperation, String operationName) {
        try {
            return rtcOperation.execute();
        } catch (RuntimeException e) {
            if (isRtcSdkKnownIssue(e)) {
                Log.w(TAG, "Caught known RTC SDK issue during " + operationName + ": " + e.getMessage());
                return null;
            } else {
                throw e;
            }
        } catch (Exception e) {
            if (isRtcSdkKnownIssue(e)) {
                Log.w(TAG, "Caught known RTC SDK issue during " + operationName + ": " + e.getMessage());
                return null;
            } else {
                throw new RuntimeException("Unexpected exception during " + operationName, e);
            }
        }
    }

    /**
     * RTC操作接口
     * @param <T> 返回类型
     */
    public interface RtcOperation<T> {
        T execute() throws Exception;
    }
}
