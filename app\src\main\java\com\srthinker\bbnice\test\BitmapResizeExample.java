package com.srthinker.bbnice.test;

import android.graphics.Bitmap;
import android.util.Log;

import com.srthinker.bbnice.utils.BitmapUtils;

/**
 * BitmapUtils.resize方法使用示例
 */
public class BitmapResizeExample {
    private static final String TAG = "BitmapResizeExample";

    /**
     * 演示resize方法的各种用法
     */
    public static void demonstrateResize() {
        Log.d(TAG, "=== BitmapUtils.resize() 使用示例 ===");

        // 创建一个测试图片 (1200x800, 3:2比例)
        Bitmap originalBitmap = BitmapCompressionTest.createTestBitmap(1200, 800);
        Log.d(TAG, "原始图片: " + originalBitmap.getWidth() + "x" + originalBitmap.getHeight());

        // 1. 基本用法：缩放到指定尺寸，保持比例（默认）
        Bitmap resized1 = BitmapUtils.resize(originalBitmap, 600, 400);
        if (resized1 != null) {
            Log.d(TAG, "1. 保持比例缩放到600x400: " + resized1.getWidth() + "x" + resized1.getHeight());
            // 结果应该是600x400，因为比例相同
        }

        // 2. 缩放到不同比例的目标尺寸，保持原图比例
        Bitmap resized2 = BitmapUtils.resize(originalBitmap, 500, 500);
        if (resized2 != null) {
            Log.d(TAG, "2. 保持比例缩放到500x500: " + resized2.getWidth() + "x" + resized2.getHeight());
            // 结果应该是500x333，保持3:2比例，适应500x500的框
        }

        // 3. 强制缩放到指定尺寸，不保持比例
        Bitmap resized3 = BitmapUtils.resize(originalBitmap, 500, 500, false);
        if (resized3 != null) {
            Log.d(TAG, "3. 强制缩放到500x500: " + resized3.getWidth() + "x" + resized3.getHeight());
            // 结果应该是500x500，图片可能会变形
        }

        // 4. 按比例缩放
        Bitmap resized4 = BitmapUtils.resize(originalBitmap, 0.5f);
        if (resized4 != null) {
            Log.d(TAG, "4. 按0.5倍缩放: " + resized4.getWidth() + "x" + resized4.getHeight());
            // 结果应该是600x400
        }

        // 5. 放大图片
        Bitmap resized5 = BitmapUtils.resize(originalBitmap, 1.5f);
        if (resized5 != null) {
            Log.d(TAG, "5. 按1.5倍放大: " + resized5.getWidth() + "x" + resized5.getHeight());
            // 结果应该是1800x1200
        }

        // 6. 缩放到更宽的目标尺寸
        Bitmap resized6 = BitmapUtils.resize(originalBitmap, 800, 300);
        if (resized6 != null) {
            Log.d(TAG, "6. 保持比例缩放到800x300: " + resized6.getWidth() + "x" + resized6.getHeight());
            // 结果应该是450x300，以高度为准保持比例
        }

        // 7. 缩放到更高的目标尺寸
        Bitmap resized7 = BitmapUtils.resize(originalBitmap, 300, 800);
        if (resized7 != null) {
            Log.d(TAG, "7. 保持比例缩放到300x800: " + resized7.getWidth() + "x" + resized7.getHeight());
            // 结果应该是300x200，以宽度为准保持比例
        }

        // 清理资源
        cleanupBitmaps(originalBitmap, resized1, resized2, resized3, resized4, resized5, resized6, resized7);

        Log.d(TAG, "=== 示例演示完成 ===");
    }

    /**
     * 演示实际应用场景
     */
    public static void demonstrateRealWorldUsage() {
        Log.d(TAG, "=== 实际应用场景示例 ===");

        // 模拟从相机获取的高分辨率图片
        Bitmap cameraPhoto = BitmapCompressionTest.createTestBitmap(4096, 3072); // 4:3比例
        Log.d(TAG, "相机照片: " + cameraPhoto.getWidth() + "x" + cameraPhoto.getHeight());

        // 场景1：生成缩略图
        Bitmap thumbnail = BitmapUtils.resize(cameraPhoto, 150, 150);
        Log.d(TAG, "缩略图: " + (thumbnail != null ? thumbnail.getWidth() + "x" + thumbnail.getHeight() : "null"));

        // 场景2：适应屏幕显示 (假设屏幕是1080x1920)
        Bitmap screenFit = BitmapUtils.resize(cameraPhoto, 1080, 1920);
        Log.d(TAG, "屏幕适配: " + (screenFit != null ? screenFit.getWidth() + "x" + screenFit.getHeight() : "null"));

        // 场景3：社交媒体分享 (正方形)
        Bitmap socialMedia = BitmapUtils.resize(cameraPhoto, 1080, 1080);
        Log.d(TAG, "社交媒体: " + (socialMedia != null ? socialMedia.getWidth() + "x" + socialMedia.getHeight() : "null"));

        // 场景4：头像裁剪 (小正方形，不保持比例)
        Bitmap avatar = BitmapUtils.resize(cameraPhoto, 200, 200, false);
        Log.d(TAG, "头像: " + (avatar != null ? avatar.getWidth() + "x" + avatar.getHeight() : "null"));

        // 场景5：网页显示优化 (减少50%大小)
        Bitmap webOptimized = BitmapUtils.resize(cameraPhoto, 0.5f);
        Log.d(TAG, "网页优化: " + (webOptimized != null ? webOptimized.getWidth() + "x" + webOptimized.getHeight() : "null"));

        // 清理资源
        cleanupBitmaps(cameraPhoto, thumbnail, screenFit, socialMedia, avatar, webOptimized);

        Log.d(TAG, "=== 实际应用场景示例完成 ===");
    }

    /**
     * 清理bitmap资源
     */
    private static void cleanupBitmaps(Bitmap... bitmaps) {
        for (Bitmap bitmap : bitmaps) {
            if (bitmap != null && !bitmap.isRecycled()) {
                bitmap.recycle();
            }
        }
    }

    /**
     * 运行所有示例
     */
    public static void runAllExamples() {
        demonstrateResize();
        demonstrateRealWorldUsage();
    }
}
