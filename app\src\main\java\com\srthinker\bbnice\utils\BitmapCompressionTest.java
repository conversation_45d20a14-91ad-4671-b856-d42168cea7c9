package com.srthinker.bbnice.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;

/**
 * Bitmap压缩测试工具类
 */
public class BitmapCompressionTest {
    private static final String TAG = "BitmapCompressionTest";

    /**
     * 创建测试用的Bitmap
     */
    public static Bitmap createTestBitmap(int width, int height) {
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);

        // 绘制彩色背景
        canvas.drawColor(Color.BLUE);

        // 绘制一些图案
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        // 绘制红色圆圈
        paint.setColor(Color.RED);
        canvas.drawCircle(width / 4f, height / 4f, Math.min(width, height) / 8f, paint);

        // 绘制绿色矩形
        paint.setColor(Color.GREEN);
        canvas.drawRect(width / 2f, height / 2f, width * 3f / 4f, height * 3f / 4f, paint);

        // 绘制黄色文字
        paint.setColor(Color.YELLOW);
        paint.setTextSize(Math.min(width, height) / 10f);
        canvas.drawText("TEST", width / 2f, height / 2f, paint);

        return bitmap;
    }

    /**
     * 测试压缩功能
     */
    public static void testCompression() {
        Log.d(TAG, "=== Starting Bitmap Compression Test ===");

        // 创建不同尺寸的测试图片
        int[] sizes = {512, 1024, 2048, 4096};
        int[] targetSizes = {50, 100, 200, 500}; // KB

        for (int size : sizes) {
            Bitmap testBitmap = createTestBitmap(size, size);
            Log.d(TAG, "\n--- Testing " + size + "x" + size + " bitmap ---");

            for (int targetSize : targetSizes) {
                Log.d(TAG, "\nTarget size: " + targetSize + "KB");

                long startTime = System.currentTimeMillis();
                byte[] compressed = BitmapUtils.compressBitmap(testBitmap, targetSize);
                long endTime = System.currentTimeMillis();

                int actualSizeKB = compressed.length / 1024;
                boolean success = actualSizeKB <= targetSize;

                Log.d(TAG, "Result: " + actualSizeKB + "KB (target: " + targetSize + "KB)");
                Log.d(TAG, "Success: " + success);
                Log.d(TAG, "Time: " + (endTime - startTime) + "ms");

                if (!success) {
                    Log.w(TAG, "⚠️ Compression failed to meet target size!");
                } else {
                    Log.i(TAG, "✅ Compression successful!");
                }
            }

            // 回收测试bitmap
            if (!testBitmap.isRecycled()) {
                testBitmap.recycle();
            }
        }

        Log.d(TAG, "=== Bitmap Compression Test Complete ===");
    }

    /**
     * 测试边界情况
     */
    public static void testEdgeCases() {
        Log.d(TAG, "=== Testing Edge Cases ===");

        // 测试null bitmap
        byte[] result1 = BitmapUtils.compressBitmap(null, 100);
        Log.d(TAG, "Null bitmap result length: " + result1.length);

        // 测试无效目标大小
        Bitmap testBitmap = createTestBitmap(100, 100);
        byte[] result2 = BitmapUtils.compressBitmap(testBitmap, 0);
        Log.d(TAG, "Invalid target size result length: " + result2.length);

        byte[] result3 = BitmapUtils.compressBitmap(testBitmap, -1);
        Log.d(TAG, "Negative target size result length: " + result3.length);

        // 测试已回收的bitmap
        testBitmap.recycle();
        byte[] result4 = BitmapUtils.compressBitmap(testBitmap, 100);
        Log.d(TAG, "Recycled bitmap result length: " + result4.length);

        Log.d(TAG, "=== Edge Cases Test Complete ===");
    }

    /**
     * 测试resize功能
     */
    public static void testResize() {
        Log.d(TAG, "=== Testing Resize Functionality ===");

        // 创建测试图片
        Bitmap testBitmap = createTestBitmap(1000, 800); // 5:4 比例
        Log.d(TAG, "Original bitmap: " + testBitmap.getWidth() + "x" + testBitmap.getHeight());

        // 测试保持比例缩放到不同目标尺寸
        int[][] targetSizes = {
            {500, 400},   // 相同比例
            {600, 300},   // 更宽的目标
            {300, 600},   // 更高的目标
            {200, 200},   // 正方形目标
            {1200, 1000}, // 放大
        };

        for (int[] size : targetSizes) {
            int targetW = size[0];
            int targetH = size[1];

            Log.d(TAG, "\n--- Testing resize to " + targetW + "x" + targetH + " ---");

            // 保持比例缩放
            Bitmap resized1 = BitmapUtils.resize(testBitmap, targetW, targetH, true);
            if (resized1 != null) {
                Log.d(TAG, "Keep aspect ratio: " + resized1.getWidth() + "x" + resized1.getHeight());

                // 验证比例是否保持
                float originalRatio = (float) testBitmap.getWidth() / testBitmap.getHeight();
                float resizedRatio = (float) resized1.getWidth() / resized1.getHeight();
                boolean ratioKept = Math.abs(originalRatio - resizedRatio) < 0.01f;
                Log.d(TAG, "Aspect ratio preserved: " + ratioKept +
                      " (original: " + String.format("%.3f", originalRatio) +
                      ", resized: " + String.format("%.3f", resizedRatio) + ")");

                if (!resized1.isRecycled()) {
                    resized1.recycle();
                }
            }

            // 不保持比例缩放
            Bitmap resized2 = BitmapUtils.resize(testBitmap, targetW, targetH, false);
            if (resized2 != null) {
                Log.d(TAG, "Ignore aspect ratio: " + resized2.getWidth() + "x" + resized2.getHeight());

                if (!resized2.isRecycled()) {
                    resized2.recycle();
                }
            }
        }

        // 测试按比例缩放
        Log.d(TAG, "\n--- Testing scale resize ---");
        float[] scales = {0.5f, 0.8f, 1.0f, 1.5f, 2.0f};

        for (float scale : scales) {
            Bitmap scaled = BitmapUtils.resize(testBitmap, scale);
            if (scaled != null) {
                Log.d(TAG, "Scale " + scale + ": " + scaled.getWidth() + "x" + scaled.getHeight());

                if (!scaled.isRecycled()) {
                    scaled.recycle();
                }
            }
        }

        // 回收测试bitmap
        if (!testBitmap.isRecycled()) {
            testBitmap.recycle();
        }

        Log.d(TAG, "=== Resize Test Complete ===");
    }

    /**
     * 测试resize边界情况
     */
    public static void testResizeEdgeCases() {
        Log.d(TAG, "=== Testing Resize Edge Cases ===");

        // 测试null bitmap
        Bitmap result1 = BitmapUtils.resize(null, 100, 100);
        Log.d(TAG, "Null bitmap result: " + (result1 == null ? "null" : "not null"));

        // 测试无效尺寸
        Bitmap testBitmap = createTestBitmap(100, 100);
        Bitmap result2 = BitmapUtils.resize(testBitmap, 0, 100);
        Log.d(TAG, "Invalid width result: " + (result2 == null ? "null" : "not null"));

        Bitmap result3 = BitmapUtils.resize(testBitmap, 100, -1);
        Log.d(TAG, "Invalid height result: " + (result3 == null ? "null" : "not null"));

        // 测试无效缩放比例
        Bitmap result4 = BitmapUtils.resize(testBitmap, 0.0f);
        Log.d(TAG, "Zero scale result: " + (result4 == null ? "null" : "not null"));

        Bitmap result5 = BitmapUtils.resize(testBitmap, -0.5f);
        Log.d(TAG, "Negative scale result: " + (result5 == null ? "null" : "not null"));

        // 测试相同尺寸
        Bitmap result6 = BitmapUtils.resize(testBitmap, 100, 100);
        Log.d(TAG, "Same size result: " + (result6 == testBitmap ? "same instance" : "different instance"));

        // 测试1.0缩放
        Bitmap result7 = BitmapUtils.resize(testBitmap, 1.0f);
        Log.d(TAG, "1.0 scale result: " + (result7 == testBitmap ? "same instance" : "different instance"));

        // 测试已回收的bitmap
        testBitmap.recycle();
        Bitmap result8 = BitmapUtils.resize(testBitmap, 200, 200);
        Log.d(TAG, "Recycled bitmap result: " + (result8 == null ? "null" : "not null"));

        Log.d(TAG, "=== Resize Edge Cases Test Complete ===");
    }

    /**
     * 运行所有测试
     */
    public static void runAllTests() {
        testCompression();
        testEdgeCases();
        testResize();
        testResizeEdgeCases();
    }
}
