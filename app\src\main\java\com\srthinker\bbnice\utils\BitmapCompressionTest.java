package com.srthinker.bbnice.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;

/**
 * Bitmap压缩测试工具类
 */
public class BitmapCompressionTest {
    private static final String TAG = "BitmapCompressionTest";

    /**
     * 创建测试用的Bitmap
     */
    public static Bitmap createTestBitmap(int width, int height) {
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        
        // 绘制彩色背景
        canvas.drawColor(Color.BLUE);
        
        // 绘制一些图案
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        
        // 绘制红色圆圈
        paint.setColor(Color.RED);
        canvas.drawCircle(width / 4f, height / 4f, Math.min(width, height) / 8f, paint);
        
        // 绘制绿色矩形
        paint.setColor(Color.GREEN);
        canvas.drawRect(width / 2f, height / 2f, width * 3f / 4f, height * 3f / 4f, paint);
        
        // 绘制黄色文字
        paint.setColor(Color.YELLOW);
        paint.setTextSize(Math.min(width, height) / 10f);
        canvas.drawText("TEST", width / 2f, height / 2f, paint);
        
        return bitmap;
    }

    /**
     * 测试压缩功能
     */
    public static void testCompression() {
        Log.d(TAG, "=== Starting Bitmap Compression Test ===");
        
        // 创建不同尺寸的测试图片
        int[] sizes = {512, 1024, 2048, 4096};
        int[] targetSizes = {50, 100, 200, 500}; // KB
        
        for (int size : sizes) {
            Bitmap testBitmap = createTestBitmap(size, size);
            Log.d(TAG, "\n--- Testing " + size + "x" + size + " bitmap ---");
            
            for (int targetSize : targetSizes) {
                Log.d(TAG, "\nTarget size: " + targetSize + "KB");
                
                long startTime = System.currentTimeMillis();
                byte[] compressed = BitmapUtils.compressBitmap(testBitmap, targetSize);
                long endTime = System.currentTimeMillis();
                
                int actualSizeKB = compressed.length / 1024;
                boolean success = actualSizeKB <= targetSize;
                
                Log.d(TAG, "Result: " + actualSizeKB + "KB (target: " + targetSize + "KB)");
                Log.d(TAG, "Success: " + success);
                Log.d(TAG, "Time: " + (endTime - startTime) + "ms");
                
                if (!success) {
                    Log.w(TAG, "⚠️ Compression failed to meet target size!");
                } else {
                    Log.i(TAG, "✅ Compression successful!");
                }
            }
            
            // 回收测试bitmap
            if (!testBitmap.isRecycled()) {
                testBitmap.recycle();
            }
        }
        
        Log.d(TAG, "=== Bitmap Compression Test Complete ===");
    }

    /**
     * 测试边界情况
     */
    public static void testEdgeCases() {
        Log.d(TAG, "=== Testing Edge Cases ===");
        
        // 测试null bitmap
        byte[] result1 = BitmapUtils.compressBitmap(null, 100);
        Log.d(TAG, "Null bitmap result length: " + result1.length);
        
        // 测试无效目标大小
        Bitmap testBitmap = createTestBitmap(100, 100);
        byte[] result2 = BitmapUtils.compressBitmap(testBitmap, 0);
        Log.d(TAG, "Invalid target size result length: " + result2.length);
        
        byte[] result3 = BitmapUtils.compressBitmap(testBitmap, -1);
        Log.d(TAG, "Negative target size result length: " + result3.length);
        
        // 测试已回收的bitmap
        testBitmap.recycle();
        byte[] result4 = BitmapUtils.compressBitmap(testBitmap, 100);
        Log.d(TAG, "Recycled bitmap result length: " + result4.length);
        
        Log.d(TAG, "=== Edge Cases Test Complete ===");
    }

    /**
     * 运行所有测试
     */
    public static void runAllTests() {
        testCompression();
        testEdgeCases();
    }
}
