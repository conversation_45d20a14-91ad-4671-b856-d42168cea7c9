<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_location" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_location.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_location_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="52" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="45"/></Target><Target id="@+id/loc_status_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/tv_loc_status_label" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/switch_location" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="55"/></Target></Targets></Layout>