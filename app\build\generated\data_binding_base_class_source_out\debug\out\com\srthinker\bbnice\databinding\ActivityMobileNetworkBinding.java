// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMobileNetworkBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnRefresh;

  @NonNull
  public final CardView cardNetworkInfo;

  @NonNull
  public final View divider2;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final ConstraintLayout mobileDataContainer;

  @NonNull
  public final SwitchCompat switchMobileData;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvMobileDataLabel;

  @NonNull
  public final TextView tvNetworkInfoLabel;

  @NonNull
  public final TextView tvNetworkOperator;

  @NonNull
  public final TextView tvNetworkStatus;

  @NonNull
  public final TextView tvNetworkType;

  @NonNull
  public final TextView tvPermissionWarning;

  @NonNull
  public final TextView tvRoaming;

  @NonNull
  public final TextView tvSignalStrength;

  private ActivityMobileNetworkBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnRefresh, @NonNull CardView cardNetworkInfo, @NonNull View divider2,
      @NonNull ConstraintLayout main, @NonNull ConstraintLayout mobileDataContainer,
      @NonNull SwitchCompat switchMobileData, @NonNull Toolbar toolbar,
      @NonNull TextView tvMobileDataLabel, @NonNull TextView tvNetworkInfoLabel,
      @NonNull TextView tvNetworkOperator, @NonNull TextView tvNetworkStatus,
      @NonNull TextView tvNetworkType, @NonNull TextView tvPermissionWarning,
      @NonNull TextView tvRoaming, @NonNull TextView tvSignalStrength) {
    this.rootView = rootView;
    this.btnRefresh = btnRefresh;
    this.cardNetworkInfo = cardNetworkInfo;
    this.divider2 = divider2;
    this.main = main;
    this.mobileDataContainer = mobileDataContainer;
    this.switchMobileData = switchMobileData;
    this.toolbar = toolbar;
    this.tvMobileDataLabel = tvMobileDataLabel;
    this.tvNetworkInfoLabel = tvNetworkInfoLabel;
    this.tvNetworkOperator = tvNetworkOperator;
    this.tvNetworkStatus = tvNetworkStatus;
    this.tvNetworkType = tvNetworkType;
    this.tvPermissionWarning = tvPermissionWarning;
    this.tvRoaming = tvRoaming;
    this.tvSignalStrength = tvSignalStrength;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMobileNetworkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMobileNetworkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_mobile_network, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMobileNetworkBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_refresh;
      Button btnRefresh = ViewBindings.findChildViewById(rootView, id);
      if (btnRefresh == null) {
        break missingId;
      }

      id = R.id.card_network_info;
      CardView cardNetworkInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardNetworkInfo == null) {
        break missingId;
      }

      id = R.id.divider2;
      View divider2 = ViewBindings.findChildViewById(rootView, id);
      if (divider2 == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.mobile_data_container;
      ConstraintLayout mobileDataContainer = ViewBindings.findChildViewById(rootView, id);
      if (mobileDataContainer == null) {
        break missingId;
      }

      id = R.id.switch_mobile_data;
      SwitchCompat switchMobileData = ViewBindings.findChildViewById(rootView, id);
      if (switchMobileData == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_mobile_data_label;
      TextView tvMobileDataLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvMobileDataLabel == null) {
        break missingId;
      }

      id = R.id.tv_network_info_label;
      TextView tvNetworkInfoLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvNetworkInfoLabel == null) {
        break missingId;
      }

      id = R.id.tv_network_operator;
      TextView tvNetworkOperator = ViewBindings.findChildViewById(rootView, id);
      if (tvNetworkOperator == null) {
        break missingId;
      }

      id = R.id.tv_network_status;
      TextView tvNetworkStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvNetworkStatus == null) {
        break missingId;
      }

      id = R.id.tv_network_type;
      TextView tvNetworkType = ViewBindings.findChildViewById(rootView, id);
      if (tvNetworkType == null) {
        break missingId;
      }

      id = R.id.tv_permission_warning;
      TextView tvPermissionWarning = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionWarning == null) {
        break missingId;
      }

      id = R.id.tv_roaming;
      TextView tvRoaming = ViewBindings.findChildViewById(rootView, id);
      if (tvRoaming == null) {
        break missingId;
      }

      id = R.id.tv_signal_strength;
      TextView tvSignalStrength = ViewBindings.findChildViewById(rootView, id);
      if (tvSignalStrength == null) {
        break missingId;
      }

      return new ActivityMobileNetworkBinding((ConstraintLayout) rootView, btnRefresh,
          cardNetworkInfo, divider2, main, mobileDataContainer, switchMobileData, toolbar,
          tvMobileDataLabel, tvNetworkInfoLabel, tvNetworkOperator, tvNetworkStatus, tvNetworkType,
          tvPermissionWarning, tvRoaming, tvSignalStrength);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
