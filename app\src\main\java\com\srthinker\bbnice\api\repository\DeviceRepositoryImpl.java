package com.srthinker.bbnice.api.repository;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.DeviceListResponse;
import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.bean.MediaResponse;
import com.srthinker.bbnice.api.bean.PetNameResponse;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.bean.WhitelistResponse;
import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.chat.ChatReportMessage;
import com.srthinker.bbnice.core.ErrorHandler;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.db.RoomType;
import com.srthinker.bbnice.utils.HttpUtils;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DeviceRepository接口的实现类
 */
public class DeviceRepositoryImpl implements DeviceRepository {

    private final BBNiceApi api;
    private final Context context;
    // 认证Token
    private String authToken;

    // 认证Token

    /**
     * 构造函数
     * @param context 上下文
     */
    public DeviceRepositoryImpl(Context context) {
        this.api = BBNiceApi.getInstance(context);
        this.context = context;
    }

    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        api.setAuthToken(token);
        this.authToken = token;
    }

    @Override
    public LiveData<Result<LoginResponse>> login() {
        MutableLiveData<Result<LoginResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.deviceLogin(new ApiCallback<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse response) {
                // 保存Token
                if (response.getData() != null) {
                    String token = response.getData().getLoginInfo().getAccessToken();
                    ApiRepositoryProvider.getInstance(context).setAuthToken(token);
                    Log.d("deviceLogin", "onSuccess: " + token);
                }
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<QrCodeResponse>> getDeviceQrCode() {
        MutableLiveData<Result<QrCodeResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getDeviceQrCode(new ApiCallback<QrCodeResponse>() {
            @Override
            public void onSuccess(QrCodeResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> unbindDevice() {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.unbindDevice(new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<DeviceListResponse>> getDeviceList() {
        MutableLiveData<Result<DeviceListResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getDeviceList(new ApiCallback<DeviceListResponse>() {
            @Override
            public void onSuccess(DeviceListResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<PetNameResponse>> updatePetName(String deviceId, String petName) {
        MutableLiveData<Result<PetNameResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.updatePetName(deviceId, petName, new ApiCallback<PetNameResponse>() {
            @Override
            public void onSuccess(PetNameResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> addPhoneWhitelist(String deviceId, String phone, String name) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.addPhoneWhitelist(deviceId, phone, name, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> deletePhoneWhitelist(String deviceId, String phone) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.deletePhoneWhitelist(deviceId, phone, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<WhitelistResponse>> getPhoneWhitelist(String deviceId) {
        MutableLiveData<Result<WhitelistResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getPhoneWhitelist(deviceId, new ApiCallback<WhitelistResponse>() {
            @Override
            public void onSuccess(WhitelistResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public void reportChatHistory(List<ChatMessage> chatMessages, @NonNull HttpUtils.HttpCallback callback) {
        new Thread(() -> {
            for (RoomType roomType : RoomType.values()) {
                long[] minTime = new long[1];
                long[] maxTime = new long[1];
                minTime[0] = Integer.MAX_VALUE;
                maxTime[0] = Integer.MIN_VALUE;
                String chatTypeValue = roomType.getValue();
                List<ChatReportMessage.ContentData> contentData = chatMessages.stream()
                        .filter(chatMessage ->
                                chatMessage.getRoomType().equals(chatTypeValue)
                        ).map(chatMessage -> {
                            long timestamp = chatMessage.getTimestamp();
                            if (minTime[0] > timestamp) minTime[0] = timestamp;
                            if (maxTime[0] < timestamp) maxTime[0] = timestamp;
                            return new ChatReportMessage.ContentData(chatTypeValue, chatMessage.getContent(), timestamp);
                        }).collect(Collectors.toList());
                ChatReportMessage reportMessage = new ChatReportMessage(chatTypeValue, contentData);
                reportMessage.start_time = minTime[0];
                reportMessage.end_time = maxTime[0];
                reportMessage.duration = maxTime[0] - minTime[0];

                if (!api.reportChatHistory(reportMessage)) {
                    callback.onFailure("");
                    return;
                }
            }
            callback.onSuccess("");
        }).start();
    }

    @Override
    public LiveData<Result<BaseResponse>> reportDeviceStatus(int batteryLevel, String networkStatus,
                                                             int signalStrength,
                                                             double latitude, double longitude,
                                                             String firmware_version) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.reportDeviceStatus(batteryLevel, networkStatus, signalStrength,latitude, longitude,firmware_version, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<MediaResponse>> uploadMedia(String type, byte[] file) {
        MutableLiveData<Result<MediaResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.uploadMedia(type, file, new ApiCallback<MediaResponse>() {
            @Override
            public void onSuccess(MediaResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<MediaResponse>> uploadMedia(String type, File[] files) {
        MutableLiveData<Result<MediaResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.uploadMedia(type, files, new ApiCallback<MediaResponse>() {
            @Override
            public void onSuccess(MediaResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }
}
