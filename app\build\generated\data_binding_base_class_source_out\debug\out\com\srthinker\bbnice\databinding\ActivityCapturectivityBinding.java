// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCapturectivityBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnBack;

  @NonNull
  public final ImageView btnCap;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final ImageView resultBg;

  @NonNull
  public final View resultBgMask;

  @NonNull
  public final TextureView surface;

  @NonNull
  public final TextView tvCnResult;

  @NonNull
  public final TextView tvEnResult;

  private ActivityCapturectivityBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnBack,
      @NonNull ImageView btnCap, @NonNull ConstraintLayout main, @NonNull ImageView resultBg,
      @NonNull View resultBgMask, @NonNull TextureView surface, @NonNull TextView tvCnResult,
      @NonNull TextView tvEnResult) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCap = btnCap;
    this.main = main;
    this.resultBg = resultBg;
    this.resultBgMask = resultBgMask;
    this.surface = surface;
    this.tvCnResult = tvCnResult;
    this.tvEnResult = tvEnResult;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCapturectivityBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCapturectivityBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_capturectivity, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCapturectivityBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      Button btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_cap;
      ImageView btnCap = ViewBindings.findChildViewById(rootView, id);
      if (btnCap == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.result_bg;
      ImageView resultBg = ViewBindings.findChildViewById(rootView, id);
      if (resultBg == null) {
        break missingId;
      }

      id = R.id.result_bg_mask;
      View resultBgMask = ViewBindings.findChildViewById(rootView, id);
      if (resultBgMask == null) {
        break missingId;
      }

      id = R.id.surface;
      TextureView surface = ViewBindings.findChildViewById(rootView, id);
      if (surface == null) {
        break missingId;
      }

      id = R.id.tv_cn_result;
      TextView tvCnResult = ViewBindings.findChildViewById(rootView, id);
      if (tvCnResult == null) {
        break missingId;
      }

      id = R.id.tv_en_result;
      TextView tvEnResult = ViewBindings.findChildViewById(rootView, id);
      if (tvEnResult == null) {
        break missingId;
      }

      return new ActivityCapturectivityBinding((ConstraintLayout) rootView, btnBack, btnCap, main,
          resultBg, resultBgMask, surface, tvCnResult, tvEnResult);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
