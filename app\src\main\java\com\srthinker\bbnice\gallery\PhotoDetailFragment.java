package com.srthinker.bbnice.gallery;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.github.chrisbanes.photoview.PhotoView;
import com.srthinker.bbnice.R;

/**
 * 照片详情Fragment，用于查看照片详情并支持缩放
 */
public class PhotoDetailFragment extends Fragment {
    private static final String ARG_MEDIA_ITEM = "media_item";

    private MediaItem mediaItem;
    private PhotoView photoView;
    private ProgressBar progressBar;
    private ImageButton btnBack;

    /**
     * 创建PhotoDetailFragment实例
     * @param mediaItem 媒体项
     * @return Fragment实例
     */
    public static PhotoDetailFragment newInstance(MediaItem mediaItem) {
        PhotoDetailFragment fragment = new PhotoDetailFragment();
        Bundle args = new Bundle();
        args.putParcelable(ARG_MEDIA_ITEM, mediaItem);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mediaItem = getArguments().getParcelable(ARG_MEDIA_ITEM);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_photo_detail, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        photoView = view.findViewById(R.id.photo_view);
        progressBar = view.findViewById(R.id.progress_bar);
        btnBack = view.findViewById(R.id.btn_back);

        // 设置返回按钮点击事件
        btnBack.setOnClickListener(v -> {
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });

        // 加载图片
        loadImage();
    }

    /**
     * 加载图片
     */
    private void loadImage() {
        if (mediaItem == null || getContext() == null) return;

        progressBar.setVisibility(View.VISIBLE);

        // 使用Glide加载高清图片
        Glide.with(this)
                .load(mediaItem.getUri())
                .apply(new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(photoView);

        // 隐藏进度条
        progressBar.setVisibility(View.GONE);
    }
}
