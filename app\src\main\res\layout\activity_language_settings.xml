<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/language_settings"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <RadioGroup
        android:id="@+id/language_radio_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/radio_system"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/system_language"
            android:padding="12dp"/>

        <RadioButton
            android:id="@+id/radio_english"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/english"
            android:padding="12dp"/>

        <RadioButton
            android:id="@+id/radio_chinese"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/chinese"
            android:padding="12dp"/>

    </RadioGroup>

    <Button
        android:id="@+id/btn_apply"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/apply"
        android:layout_marginTop="24dp"/>

</LinearLayout>
