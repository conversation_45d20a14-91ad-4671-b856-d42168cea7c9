// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDisplayBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ConstraintLayout autoBrightnessContainer;

  @NonNull
  public final ImageButton btnLuminanceDown;

  @NonNull
  public final ImageButton btnLuminanceUp;

  @NonNull
  public final View divider;

  @NonNull
  public final View divider2;

  @NonNull
  public final LinearLayout main;

  @NonNull
  public final ConstraintLayout mediaLuminanceContainer;

  @NonNull
  public final ConstraintLayout muteContainer;

  @NonNull
  public final RecyclerView rvDurationList;

  @NonNull
  public final SeekBar seekbarScreenLuminance;

  @NonNull
  public final SwitchCompat switchAutoBrightness;

  @NonNull
  public final SwitchCompat switchMute;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAutoBrightnessLabel;

  @NonNull
  public final TextView tvBrightnessLevel;

  @NonNull
  public final TextView tvMediaLuminanceLabel;

  @NonNull
  public final TextView tvMuteLabel;

  @NonNull
  public final TextView tvPermissionWarning;

  @NonNull
  public final TextView tvWakeUpDurationLabel;

  private ActivityDisplayBinding(@NonNull LinearLayout rootView,
      @NonNull ConstraintLayout autoBrightnessContainer, @NonNull ImageButton btnLuminanceDown,
      @NonNull ImageButton btnLuminanceUp, @NonNull View divider, @NonNull View divider2,
      @NonNull LinearLayout main, @NonNull ConstraintLayout mediaLuminanceContainer,
      @NonNull ConstraintLayout muteContainer, @NonNull RecyclerView rvDurationList,
      @NonNull SeekBar seekbarScreenLuminance, @NonNull SwitchCompat switchAutoBrightness,
      @NonNull SwitchCompat switchMute, @NonNull Toolbar toolbar,
      @NonNull TextView tvAutoBrightnessLabel, @NonNull TextView tvBrightnessLevel,
      @NonNull TextView tvMediaLuminanceLabel, @NonNull TextView tvMuteLabel,
      @NonNull TextView tvPermissionWarning, @NonNull TextView tvWakeUpDurationLabel) {
    this.rootView = rootView;
    this.autoBrightnessContainer = autoBrightnessContainer;
    this.btnLuminanceDown = btnLuminanceDown;
    this.btnLuminanceUp = btnLuminanceUp;
    this.divider = divider;
    this.divider2 = divider2;
    this.main = main;
    this.mediaLuminanceContainer = mediaLuminanceContainer;
    this.muteContainer = muteContainer;
    this.rvDurationList = rvDurationList;
    this.seekbarScreenLuminance = seekbarScreenLuminance;
    this.switchAutoBrightness = switchAutoBrightness;
    this.switchMute = switchMute;
    this.toolbar = toolbar;
    this.tvAutoBrightnessLabel = tvAutoBrightnessLabel;
    this.tvBrightnessLevel = tvBrightnessLevel;
    this.tvMediaLuminanceLabel = tvMediaLuminanceLabel;
    this.tvMuteLabel = tvMuteLabel;
    this.tvPermissionWarning = tvPermissionWarning;
    this.tvWakeUpDurationLabel = tvWakeUpDurationLabel;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDisplayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDisplayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_display, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDisplayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.auto_brightness_container;
      ConstraintLayout autoBrightnessContainer = ViewBindings.findChildViewById(rootView, id);
      if (autoBrightnessContainer == null) {
        break missingId;
      }

      id = R.id.btn_luminance_down;
      ImageButton btnLuminanceDown = ViewBindings.findChildViewById(rootView, id);
      if (btnLuminanceDown == null) {
        break missingId;
      }

      id = R.id.btn_luminance_up;
      ImageButton btnLuminanceUp = ViewBindings.findChildViewById(rootView, id);
      if (btnLuminanceUp == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.divider2;
      View divider2 = ViewBindings.findChildViewById(rootView, id);
      if (divider2 == null) {
        break missingId;
      }

      LinearLayout main = (LinearLayout) rootView;

      id = R.id.media_luminance_container;
      ConstraintLayout mediaLuminanceContainer = ViewBindings.findChildViewById(rootView, id);
      if (mediaLuminanceContainer == null) {
        break missingId;
      }

      id = R.id.mute_container;
      ConstraintLayout muteContainer = ViewBindings.findChildViewById(rootView, id);
      if (muteContainer == null) {
        break missingId;
      }

      id = R.id.rv_duration_list;
      RecyclerView rvDurationList = ViewBindings.findChildViewById(rootView, id);
      if (rvDurationList == null) {
        break missingId;
      }

      id = R.id.seekbar_screen_luminance;
      SeekBar seekbarScreenLuminance = ViewBindings.findChildViewById(rootView, id);
      if (seekbarScreenLuminance == null) {
        break missingId;
      }

      id = R.id.switch_auto_brightness;
      SwitchCompat switchAutoBrightness = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoBrightness == null) {
        break missingId;
      }

      id = R.id.switch_mute;
      SwitchCompat switchMute = ViewBindings.findChildViewById(rootView, id);
      if (switchMute == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_auto_brightness_label;
      TextView tvAutoBrightnessLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvAutoBrightnessLabel == null) {
        break missingId;
      }

      id = R.id.tv_brightness_level;
      TextView tvBrightnessLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvBrightnessLevel == null) {
        break missingId;
      }

      id = R.id.tv_media_luminance_label;
      TextView tvMediaLuminanceLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvMediaLuminanceLabel == null) {
        break missingId;
      }

      id = R.id.tv_mute_label;
      TextView tvMuteLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvMuteLabel == null) {
        break missingId;
      }

      id = R.id.tv_permission_warning;
      TextView tvPermissionWarning = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionWarning == null) {
        break missingId;
      }

      id = R.id.tv_wake_up_duration_label;
      TextView tvWakeUpDurationLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvWakeUpDurationLabel == null) {
        break missingId;
      }

      return new ActivityDisplayBinding((LinearLayout) rootView, autoBrightnessContainer,
          btnLuminanceDown, btnLuminanceUp, divider, divider2, main, mediaLuminanceContainer,
          muteContainer, rvDurationList, seekbarScreenLuminance, switchAutoBrightness, switchMute,
          toolbar, tvAutoBrightnessLabel, tvBrightnessLevel, tvMediaLuminanceLabel, tvMuteLabel,
          tvPermissionWarning, tvWakeUpDurationLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
