package com.srthinker.bbnice.api;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;

import com.srthinker.bbnice.api.bean.CustomizationResponse;
import com.srthinker.bbnice.api.bean.DeviceInfo;
import com.srthinker.bbnice.api.bean.DeviceListResponse;
import com.srthinker.bbnice.api.bean.ImageRecognizeResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.bean.MediaResponse;
import com.srthinker.bbnice.api.bean.PetNameResponse;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCStopResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.bean.WhitelistItem;
import com.srthinker.bbnice.api.bean.WhitelistResponse;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.chat.ChatReportMessage;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.HttpUtils;
import com.srthinker.bbnice.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BB Nice API接口实现类
 * 实现与服务器的所有API交互
 */
public class BBNiceApi {
    private static final String TAG = "BBNiceApi";

    // 上下文
    private final Context context;

    // 认证Token
    private String authToken;

    private static BBNiceApi instance;

    public static BBNiceApi getInstance(Context context) {
        if (instance == null) {
            instance = new BBNiceApi(context);
        }
        return instance;
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    private BBNiceApi(Context context) {
        this.context = context;
    }


    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        this.authToken = token;
    }

    /**
     * 获取认证Token
     * @return 认证Token
     */
    public String getAuthToken() {
        return authToken;
    }

    /**
     * 创建带认证的请求头
     * @return 请求头Map
     */
    private Map<String, String> createAuthHeaders() {
        Map<String, String> headers = new HashMap<>();
        if (authToken != null && !authToken.isEmpty()) {
            headers.put("Authorization", "Bearer " + authToken);
        }
        return headers;
    }

    /**
     * 设备登录
     * @param callback 回调接口
     */
    public void deviceLogin(ApiCallback<LoginResponse> callback) {

        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/public/login");
        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    Log.d(TAG, "onSuccess: response====>" + response);
                    // 解析响应
                    ApiResponse<LoginResponse.LoginResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(LoginResponse.LoginResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 保存Token
                        if (apiResponse.getData() != null) {
                            String token = apiResponse.getData().getLoginInfo().getAccessToken();
                            ApiRepositoryProvider.getInstance(context).setAuthToken(token);
                            setAuthToken(token);
                            Log.d(TAG, "onSuccess: " + apiResponse.getData());
                            // 返回成功
                            LoginResponse loginResponse = new LoginResponse(apiResponse);
                            callback.onSuccess(loginResponse);
                        } else {
                            // 返回业务错误
                            callback.onError(ApiError.createBusinessError(
                                    ApiConstants.BusinessCode.INVALID_TOKEN,
                                    ApiConstants.BusinessCode.getDescription(ApiConstants.BusinessCode.INVALID_TOKEN))
                            );
                        }


                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 获取设备二维码
     * @param callback 回调接口
     */
    public void getDeviceQrCode(ApiCallback<QrCodeResponse> callback) {
        String deviceId = DeviceIdUtils.getDeviceId(context);
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/public/qrcode");
        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<QrCodeResponse.QrCodeResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(QrCodeResponse.QrCodeResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        QrCodeResponse qrCodeResponse = new QrCodeResponse(apiResponse);
                        callback.onSuccess(qrCodeResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 获取RtcToken
     * @param roomId 房间ID
     * @param callback 回调接口
     */
    public void rtcToken(String roomId, ApiCallback<RTCTokenResponse> callback) {

        String url = ApiConstants.getApiUrl("ai/tokens");
        Map<String, String> headers = createAuthHeaders();
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("room_id", roomId);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }
        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<RTCTokenResponse.RTCStartResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(RTCTokenResponse.RTCStartResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        RTCTokenResponse rtcTokenResponse = new RTCTokenResponse(apiResponse);
                        callback.onSuccess(rtcTokenResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 开启rtc智能体
     * @param requestData 请求数据
     * @param callback 回调接口
     */
    public void rtcStart(RTCStartRequestData requestData, ApiCallback<RTCStartResponse> callback) {

        String url = ApiConstants.getApiUrl("device/ai/agent/start");
        Map<String, String> headers = createAuthHeaders();
        HttpUtils.getInstance().asyncPostJson(url, JsonUtils.toJson(requestData), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<RTCStartResponse.RTCStartResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(RTCStartResponse.RTCStartResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        RTCStartResponse rtcStartResponse = new RTCStartResponse(apiResponse);
                        callback.onSuccess(rtcStartResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 关闭rtc智能体
     * @param taskId 启动AI代理时返回的任务ID
     * @param roomId 房间ID
     * @param callback 回调接口
     */
    public void rtcStop(String taskId, String roomId, ApiCallback<RTCStopResponse> callback) {

        String url = ApiConstants.getApiUrl("device/ai/agent/stop");
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("task_id", taskId);
            requestBody.put("room_id", roomId);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        Map<String, String> headers = createAuthHeaders();
        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<RTCStopResponse.RTCStopResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(RTCStopResponse.RTCStopResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        RTCStopResponse rtcStartResponse = new RTCStopResponse(apiResponse);
                        callback.onSuccess(rtcStartResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }


    /**
     * 上传图片进行识别
     * @param callback 回调接口
     */
    public void imageRecognize(Bitmap img, ApiCallback<ImageRecognizeResponse> callback) {
        String url = ApiConstants.getApiUrl("device/ai/image/recognition");
        // 发送请求
        Map<String, String> headers = createAuthHeaders();
        Log.d(TAG, "imageRecognize: img size:" + img.getWidth() + " " + img.getHeight());
        HttpUtils.getInstance().uploadBitmap(url, img, headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                Log.d(TAG, "onSuccess: " + response);
                try {

                    // 解析响应
                    ApiResponse<ImageRecognizeResponse.ImageRecognizeResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(ImageRecognizeResponse.ImageRecognizeResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        ImageRecognizeResponse imageRecognizeResponse = new ImageRecognizeResponse(apiResponse);
                        callback.onSuccess(imageRecognizeResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }

                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                Log.d(TAG, "onFailure: " + error);
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 解绑设备
     * @param callback 回调接口
     */
    public void unbindDevice(ApiCallback<BaseResponse> callback) {
        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/unbind");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 获取设备列表
     * @param callback 回调接口
     */
    public void getDeviceList(ApiCallback<DeviceListResponse> callback) {
        // 发送请求
        String url = ApiConstants.getApiUrl("device/list");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncGet(url, null, headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<List<DeviceInfo>> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getListType(DeviceInfo.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        DeviceListResponse deviceListResponse = new DeviceListResponse(apiResponse);
                        callback.onSuccess(deviceListResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 更新宠物名称
     * @param deviceId 设备ID
     * @param petName 宠物名称
     * @param callback 回调接口
     */
    public void updatePetName(String deviceId, String petName, ApiCallback<PetNameResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
            requestBody.put("pet_name", petName);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/pet/name/update");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<PetNameResponse.PetNameResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(PetNameResponse.PetNameResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        PetNameResponse petNameResponse = new PetNameResponse(apiResponse);
                        callback.onSuccess(petNameResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 添加白名单
     * @param deviceId 设备ID
     * @param phone 电话号码
     * @param name 姓名
     * @param callback 回调接口
     */
    public void addPhoneWhitelist(String deviceId, String phone, String name, ApiCallback<BaseResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
            requestBody.put("phone", phone);
            requestBody.put("name", name);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/phone/whitelist/add");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 删除白名单
     * @param deviceId 设备ID
     * @param phone 电话号码
     * @param callback 回调接口
     */
    public void deletePhoneWhitelist(String deviceId, String phone, ApiCallback<BaseResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("device_id", deviceId);
            requestBody.put("phone", phone);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/phone/whitelist/del");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 获取白名单列表
     * @param deviceId 设备ID
     * @param callback 回调接口
     */
    public void getPhoneWhitelist(String deviceId, ApiCallback<WhitelistResponse> callback) {
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("device_id", deviceId);

        // 发送请求
        String url = ApiConstants.getApiUrl("device/phone/whitelist/list");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncGet(url, params, headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<List<WhitelistItem>> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getListType(WhitelistItem.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        WhitelistResponse whitelistResponse = new WhitelistResponse(apiResponse);
                        callback.onSuccess(whitelistResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 上报聊天记录
     * @param reportMessage 聊天记录
     */
    public boolean reportChatHistory(ChatReportMessage reportMessage) {
        // 构建请求参数
        String requestJson = JsonUtils.toJson(reportMessage);

        // 发送请求
        String url = ApiConstants.getApiUrl("device/chat/record");
        Map<String, String> headers = createAuthHeaders();
        try {
            String response = HttpUtils.getInstance().syncPostJson(url, requestJson, headers);
            ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));
            return apiResponse.isSuccess();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 上报设备状态
     * @param batteryLevel 电量
     * @param networkStatus 网络状态
     * @param signalStrength 信号强度
     * @param callback 回调接口
     */
    public void reportDeviceStatus(int batteryLevel, String networkStatus,
                                   int signalStrength,
                                   double latitude, double longitude,
                                   String firmware_version, ApiCallback<BaseResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("battery_level", batteryLevel);
            requestBody.put("network_status", networkStatus);
            requestBody.put("signal_strength", signalStrength);
            requestBody.put("latitude", latitude);
            requestBody.put("longitude", longitude);
            requestBody.put("firmware_version", firmware_version);
            requestBody.put("timestamp", System.currentTimeMillis());
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/status");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 上报指令执行状态
     * @param commandId 指令ID
     * @param status 状态
     * @param message 消息
     * @param timestamp 时间戳
     * @param callback 回调接口
     */
    public void reportCommandStatus(String commandId, String status, String message, long timestamp, ApiCallback<BaseResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("command_id", commandId);
            requestBody.put("status", status);
            requestBody.put("message", message);
            requestBody.put("timestamp", timestamp);
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("device/commands/status");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<Object> apiResponse = JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 上报位置
     * @param locationData 位置数据
     * @param callback 回调接口
     */
    public void reportLocation(LocationData locationData, ApiCallback<BaseResponse> callback) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("latitude", locationData.getLatitude());
            requestBody.put("longitude", locationData.getLongitude());

            // 添加可选参数
            if (locationData.getAltitude() != 0) {
                requestBody.put("altitude", locationData.getAltitude());
            }
            if (locationData.getAccuracy() != 0) {
                requestBody.put("accuracy", locationData.getAccuracy());
            }
            if (locationData.getSpeed() != 0) {
                requestBody.put("speed", locationData.getSpeed());
            }
            if (locationData.getBearing() != 0) {
                requestBody.put("bearing", locationData.getBearing());
            }
            if (locationData.getTime() != 0) {
                requestBody.put("timestamp", locationData.getTime() / 1000); // 转换为秒
            }
            if (locationData.getAddress() != null) {
                requestBody.put("address", locationData.getAddress());
            }
        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("locations");
        Map<String, String> headers = createAuthHeaders();


        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    Log.d(TAG, "位置上报响应: " + response);

                    // 解析响应
                    ApiResponse<Object> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }

                } catch (Exception e) {
                    // 返回解析错误
                    Log.e(TAG, "解析位置上报响应失败: " + e.getMessage(), e);
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                Log.e(TAG, "位置上报失败: " + error);
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 上报多个位置
     * @param locationDataList 位置数据列表
     * @param callback 回调接口
     */
    public void reportLocations(List<LocationData> locationDataList, ApiCallback<BaseResponse> callback) {
        if (locationDataList == null || locationDataList.isEmpty()) {
            callback.onError(ApiError.createParamError("位置数据列表为空"));
            return;
        }

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        try {
            // 创建位置数组
            JSONArray locationsArray = new JSONArray();

            // 添加每个位置数据
            for (LocationData locationData : locationDataList) {
                JSONObject locationObj = new JSONObject();
                locationObj.put("latitude", locationData.getLatitude());
                locationObj.put("longitude", locationData.getLongitude());

                // 添加可选参数
                if (locationData.getAltitude() != 0) {
                    locationObj.put("altitude", locationData.getAltitude());
                }
                if (locationData.getAccuracy() != 0) {
                    locationObj.put("accuracy", locationData.getAccuracy());
                }
                if (locationData.getSpeed() != 0) {
                    locationObj.put("speed", locationData.getSpeed());
                }
                if (locationData.getBearing() != 0) {
                    locationObj.put("bearing", locationData.getBearing());
                }
                if (locationData.getTime() != 0) {
                    locationObj.put("timestamp", locationData.getTime() / 1000); // 转换为秒
                }
                if (locationData.getAddress() != null) {
                    locationObj.put("address", locationData.getAddress());
                }

                // 添加到数组
                locationsArray.put(locationObj);
            }

            // 添加位置数组到请求体
            requestBody.put("locations", locationsArray);

        } catch (JSONException e) {
            callback.onError(ApiError.createParamError("构建请求参数失败: " + e.getMessage()));
            return;
        }

        // 发送请求
        String url = ApiConstants.getApiUrl("locations/batch");
        Map<String, String> headers = createAuthHeaders();

        Log.d(TAG, "批量上报位置: " + requestBody.toString());

        HttpUtils.getInstance().asyncPostJson(url, requestBody.toString(), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    Log.d(TAG, "批量位置上报响应: " + response);

                    // 解析响应
                    ApiResponse<Object> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(Object.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        BaseResponse baseResponse = new BaseResponse(apiResponse);
                        callback.onSuccess(baseResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    Log.e(TAG, "解析批量位置上报响应失败: " + e.getMessage(), e);
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                Log.e(TAG, "批量位置上报失败: " + error);
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 上传媒体文件
     * @param type 类型
     * @param files 文件
     * @param callback 回调接口
     */
    public void uploadMedia(String type, File[] files, ApiCallback<MediaResponse> callback) {

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("device_id", DeviceIdUtils.getDeviceId(context));
        params.put("type", type);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 发送请求
        String url = ApiConstants.getApiUrl("device/media");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncUploadFiles(url, params, "file", Arrays.asList(files), headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {

                    // 解析响应
                    ApiResponse<MediaResponse.MediaResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(MediaResponse.MediaResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        MediaResponse mediaResponse = new MediaResponse(apiResponse);

                        callback.onSuccess(mediaResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }

                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }


    /**
     * 上传媒体文件
     * @param type 类型
     * @param file 文件
     * @param callback 回调接口
     */
    public void uploadMedia(String type, byte[] file, ApiCallback<MediaResponse> callback) {

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("device_id", DeviceIdUtils.getDeviceId(context));
        params.put("type", type);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 发送请求
        String url = ApiConstants.getApiUrl("device/media");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncUploadFile(url, params, "file", file, headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {

                    // 解析响应
                    ApiResponse<MediaResponse.MediaResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(MediaResponse.MediaResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        MediaResponse mediaResponse = new MediaResponse(apiResponse);

                        callback.onSuccess(mediaResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }

                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }

    /**
     * 获取设备装扮数据
     * @param callback 回调接口
     */
    public void getDeviceCustomizations(ApiCallback<CustomizationResponse> callback) {
        // 发送请求
        String url = ApiConstants.getApiUrl("device/customizations");
        Map<String, String> headers = createAuthHeaders();

        HttpUtils.getInstance().asyncGet(url, null, headers, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // 解析响应
                    ApiResponse<CustomizationResponse.CustomizationResponseData> apiResponse =
                            JsonUtils.fromJson(response, ApiResponse.getType(CustomizationResponse.CustomizationResponseData.class));

                    if (apiResponse.isSuccess()) {
                        // 返回成功
                        CustomizationResponse customizationResponse = new CustomizationResponse(apiResponse);
                        callback.onSuccess(customizationResponse);
                    } else {
                        // 返回业务错误
                        callback.onError(ApiError.createBusinessError(
                                apiResponse.getCode(),
                                apiResponse.getMessage()));
                    }
                } catch (Exception e) {
                    // 返回解析错误
                    callback.onError(ApiError.createJsonParseError(e.getMessage()));
                }
            }

            @Override
            public void onFailure(String error) {
                // 返回网络错误
                callback.onError(ApiError.createNetworkError(error));
            }
        });
    }
}
