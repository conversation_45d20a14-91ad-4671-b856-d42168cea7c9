package com.srthinker.bbnice.core;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

/**
 * 所有ViewModel的基类，提供通用功能
 */
public abstract class BaseViewModel extends ViewModel {
    
    // 加载状态
    private final MutableLiveData<Boolean> _isLoading = new MutableLiveData<>(false);
    public final LiveData<Boolean> isLoading = _isLoading;
    
    // 错误状态
    private final MutableLiveData<Event<Throwable>> _error = new MutableLiveData<>();
    public final LiveData<Event<Throwable>> error = _error;
    
    /**
     * 设置加载状态
     * @param loading 是否正在加载
     */
    protected void setLoading(boolean loading) {
        _isLoading.setValue(loading);
    }
    
    /**
     * 设置错误
     * @param throwable 错误
     */
    protected void setError(Throwable throwable) {
        _error.setValue(new Event<>(throwable));
    }
    
    /**
     * 执行异步操作的辅助方法
     * @param action 要执行的操作
     */
    protected void executeAction(Runnable action) {
        try {
            setLoading(true);
            action.run();
        } catch (Exception e) {
            setError(e);
        } finally {
            setLoading(false);
        }
    }
}
