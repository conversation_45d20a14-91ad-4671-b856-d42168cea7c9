package com.srthinker.bbnice.gallery;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.srthinker.bbnice.R;

import java.io.File;

import java.util.List;

/**
 * 媒体文件适配器，用于在RecyclerView中显示媒体项
 */
public class MediaAdapter extends RecyclerView.Adapter<MediaAdapter.MediaViewHolder> {
    private static final String TAG = "MediaAdapter";
    private final Context context;
    private final List<MediaItem> mediaItems;
    private OnMediaItemClickListener listener;
    private boolean selectMode = false;

    public MediaAdapter(Context context, List<MediaItem> mediaItems) {
        this.context = context;
        this.mediaItems = mediaItems;
    }

    @NonNull
    @Override
    public MediaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_media, parent, false);
        return new MediaViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MediaViewHolder holder, int position) {
        MediaItem item = mediaItems.get(position);

        // 检查文件是否存在
        boolean fileExists = checkFileExists(item);

        // 使用Glide加载缩略图
        if (item.isVideo()) {
            // 视频缩略图加载
            loadVideoThumbnail(holder, item, fileExists);
        } else {
            // 图片加载
            loadImageThumbnail(holder, item, fileExists);
        }

        // 设置视频指示器和时长
        if (item.isVideo()) {
            holder.imgVideoIndicator.setVisibility(View.VISIBLE);
            holder.tvDuration.setVisibility(View.VISIBLE);
            holder.tvDuration.setText(item.getFormattedDuration());
        } else {
            holder.imgVideoIndicator.setVisibility(View.GONE);
            holder.tvDuration.setVisibility(View.GONE);
        }

        // 设置选择框状态
        if (selectMode) {
            holder.checkbox.setVisibility(View.VISIBLE);
            holder.checkbox.setChecked(item.isSelected());
        } else {
            holder.checkbox.setVisibility(View.GONE);
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMediaItemClick(item, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mediaItems.size();
    }

    /**
     * 设置选择模式
     * @param selectMode 是否为选择模式
     */
    public void setSelectMode(boolean selectMode) {
        this.selectMode = selectMode;
        notifyDataSetChanged();
    }

    /**
     * 设置媒体项点击监听器
     * @param listener 监听器
     */
    public void setOnMediaItemClickListener(OnMediaItemClickListener listener) {
        this.listener = listener;
    }

    /**
     * 媒体项点击监听器接口
     */
    public interface OnMediaItemClickListener {
        void onMediaItemClick(MediaItem item, int position);
    }

    /**
     * 检查媒体文件是否存在
     * @param item 媒体项
     * @return 文件是否存在
     */
    private boolean checkFileExists(MediaItem item) {
        try {
            File file = new File(item.getPath());
            boolean exists = file.exists() && file.canRead() && file.length() > 0;
            if (!exists) {
                Log.w(TAG, "File does not exist or cannot be read: " + item.getPath());
            }
            return exists;
        } catch (Exception e) {
            Log.e(TAG, "Error checking file: " + e.getMessage());
            return false;
        }
    }

    /**
     * 加载视频缩略图
     * @param holder ViewHolder
     * @param item 媒体项
     * @param fileExists 文件是否存在
     */
    private void loadVideoThumbnail(MediaViewHolder holder, MediaItem item, boolean fileExists) {
        if (!fileExists) {
            // 如果文件不存在，显示错误占位图
            holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
            return;
        }

        try {
            // 使用Glide加载视频缩略图
            RequestBuilder<Drawable> thumbnailRequest = Glide.with(context)
                    .load(item.getUri())
                    .apply(new RequestOptions()
                            .frame(1000000) // 尝试获取视频的第1秒作为缩略图
                            .centerCrop()
                            .diskCacheStrategy(DiskCacheStrategy.ALL));

            Glide.with(context)
                    .load(item.getUri())
                    .thumbnail(thumbnailRequest)
                    .apply(new RequestOptions()
                            .placeholder(R.drawable.ic_video_placeholder)
                            .error(R.drawable.ic_broken_image)
                            .centerCrop()
                            .diskCacheStrategy(DiskCacheStrategy.ALL))
                    .listener(new RequestListener<Drawable>() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                            Log.e(TAG, "Failed to load video thumbnail: " + item.getDisplayName(), e);
                            // 尝试使用MediaMetadataRetriever手动获取缩略图
                            tryLoadWithMediaMetadataRetriever(holder, item);
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                            return false;
                        }
                    })
                    .into(holder.imgThumbnail);
        } catch (Exception e) {
            Log.e(TAG, "Error loading video thumbnail: " + e.getMessage());
            holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
        }
    }

    /**
     * 尝试使用MediaMetadataRetriever手动获取视频缩略图
     * @param holder ViewHolder
     * @param item 媒体项
     */
    private void tryLoadWithMediaMetadataRetriever(MediaViewHolder holder, MediaItem item) {
        try {
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(context, item.getUri());

            // 获取第一帧作为缩略图
            Bitmap bitmap = retriever.getFrameAtTime(0);
            if (bitmap != null) {
                BitmapDrawable drawable = new BitmapDrawable(context.getResources(), bitmap);
                holder.imgThumbnail.setImageDrawable(drawable);
            } else {
                holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
            }

            retriever.release();
        } catch (Exception e) {
            Log.e(TAG, "Error with MediaMetadataRetriever: " + e.getMessage());
            holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
        }
    }

    /**
     * 加载图片缩略图
     * @param holder ViewHolder
     * @param item 媒体项
     * @param fileExists 文件是否存在
     */
    private void loadImageThumbnail(MediaViewHolder holder, MediaItem item, boolean fileExists) {
        if (!fileExists) {
            // 如果文件不存在，显示错误占位图
            holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
            return;
        }

        try {
            Glide.with(context)
                    .load(item.getUri())
                    .apply(new RequestOptions()
                            .placeholder(R.drawable.ic_image_placeholder)
                            .error(R.drawable.ic_broken_image)
                            .centerCrop()
                            .diskCacheStrategy(DiskCacheStrategy.ALL))
                    .listener(new RequestListener<Drawable>() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                            Log.e(TAG, "Failed to load image: " + item.getDisplayName(), e);
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                            return false;
                        }
                    })
                    .into(holder.imgThumbnail);
        } catch (Exception e) {
            Log.e(TAG, "Error loading image: " + e.getMessage());
            holder.imgThumbnail.setImageResource(R.drawable.ic_broken_image);
        }
    }

    /**
     * 媒体项ViewHolder
     */
    static class MediaViewHolder extends RecyclerView.ViewHolder {
        ImageView imgThumbnail;
        ImageView imgVideoIndicator;
        TextView tvDuration;
        CheckBox checkbox;

        public MediaViewHolder(@NonNull View itemView) {
            super(itemView);
            imgThumbnail = itemView.findViewById(R.id.img_thumbnail);
            imgVideoIndicator = itemView.findViewById(R.id.img_video_indicator);
            tvDuration = itemView.findViewById(R.id.tv_duration);
            checkbox = itemView.findViewById(R.id.checkbox);
        }
    }
}
