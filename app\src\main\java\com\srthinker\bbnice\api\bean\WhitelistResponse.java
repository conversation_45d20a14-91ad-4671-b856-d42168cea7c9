package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

import java.util.List;

/**
 * 白名单响应类
 * 用于表示获取白名单API的响应
 */
public class WhitelistResponse extends BaseResponse {
    // 响应数据
    private final List<WhitelistItem> data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public WhitelistResponse(ApiResponse<List<WhitelistItem>> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public List<WhitelistItem> getData() {
        return data;
    }
}
