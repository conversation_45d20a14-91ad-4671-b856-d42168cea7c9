// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAboutBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnCopyAppVersion;

  @NonNull
  public final ImageButton btnCopyDeviceModel;

  @NonNull
  public final ImageButton btnCopyDeviceName;

  @NonNull
  public final ImageButton btnCopyDeviceSerial;

  @NonNull
  public final ImageButton btnCopyDeviceVersion;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAppBuild;

  @NonNull
  public final TextView tvAppVersion;

  @NonNull
  public final TextView tvDeviceModel;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvDeviceSerial;

  @NonNull
  public final TextView tvDeviceVersion;

  @NonNull
  public final TextView tvPermissionWarning;

  private ActivityAboutBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton btnCopyAppVersion, @NonNull ImageButton btnCopyDeviceModel,
      @NonNull ImageButton btnCopyDeviceName, @NonNull ImageButton btnCopyDeviceSerial,
      @NonNull ImageButton btnCopyDeviceVersion, @NonNull ConstraintLayout main,
      @NonNull Toolbar toolbar, @NonNull TextView tvAppBuild, @NonNull TextView tvAppVersion,
      @NonNull TextView tvDeviceModel, @NonNull TextView tvDeviceName,
      @NonNull TextView tvDeviceSerial, @NonNull TextView tvDeviceVersion,
      @NonNull TextView tvPermissionWarning) {
    this.rootView = rootView;
    this.btnCopyAppVersion = btnCopyAppVersion;
    this.btnCopyDeviceModel = btnCopyDeviceModel;
    this.btnCopyDeviceName = btnCopyDeviceName;
    this.btnCopyDeviceSerial = btnCopyDeviceSerial;
    this.btnCopyDeviceVersion = btnCopyDeviceVersion;
    this.main = main;
    this.toolbar = toolbar;
    this.tvAppBuild = tvAppBuild;
    this.tvAppVersion = tvAppVersion;
    this.tvDeviceModel = tvDeviceModel;
    this.tvDeviceName = tvDeviceName;
    this.tvDeviceSerial = tvDeviceSerial;
    this.tvDeviceVersion = tvDeviceVersion;
    this.tvPermissionWarning = tvPermissionWarning;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_about, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAboutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_copy_app_version;
      ImageButton btnCopyAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyAppVersion == null) {
        break missingId;
      }

      id = R.id.btn_copy_device_model;
      ImageButton btnCopyDeviceModel = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyDeviceModel == null) {
        break missingId;
      }

      id = R.id.btn_copy_device_name;
      ImageButton btnCopyDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyDeviceName == null) {
        break missingId;
      }

      id = R.id.btn_copy_device_serial;
      ImageButton btnCopyDeviceSerial = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyDeviceSerial == null) {
        break missingId;
      }

      id = R.id.btn_copy_device_version;
      ImageButton btnCopyDeviceVersion = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyDeviceVersion == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_app_build;
      TextView tvAppBuild = ViewBindings.findChildViewById(rootView, id);
      if (tvAppBuild == null) {
        break missingId;
      }

      id = R.id.tv_app_version;
      TextView tvAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAppVersion == null) {
        break missingId;
      }

      id = R.id.tv_device_model;
      TextView tvDeviceModel = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceModel == null) {
        break missingId;
      }

      id = R.id.tv_device_name;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tv_device_serial;
      TextView tvDeviceSerial = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceSerial == null) {
        break missingId;
      }

      id = R.id.tv_device_version;
      TextView tvDeviceVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceVersion == null) {
        break missingId;
      }

      id = R.id.tv_permission_warning;
      TextView tvPermissionWarning = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionWarning == null) {
        break missingId;
      }

      return new ActivityAboutBinding((ConstraintLayout) rootView, btnCopyAppVersion,
          btnCopyDeviceModel, btnCopyDeviceName, btnCopyDeviceSerial, btnCopyDeviceVersion, main,
          toolbar, tvAppBuild, tvAppVersion, tvDeviceModel, tvDeviceName, tvDeviceSerial,
          tvDeviceVersion, tvPermissionWarning);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
