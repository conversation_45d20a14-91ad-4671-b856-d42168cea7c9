// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogBluetoothPinBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etPinCode;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvPinInfo;

  private DialogBluetoothPinBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etPinCode, @NonNull TextView tvDeviceName,
      @NonNull TextView tvPinInfo) {
    this.rootView = rootView;
    this.etPinCode = etPinCode;
    this.tvDeviceName = tvDeviceName;
    this.tvPinInfo = tvPinInfo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogBluetoothPinBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogBluetoothPinBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_bluetooth_pin, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogBluetoothPinBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_pin_code;
      TextInputEditText etPinCode = ViewBindings.findChildViewById(rootView, id);
      if (etPinCode == null) {
        break missingId;
      }

      id = R.id.tv_device_name;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tv_pin_info;
      TextView tvPinInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvPinInfo == null) {
        break missingId;
      }

      return new DialogBluetoothPinBinding((LinearLayout) rootView, etPinCode, tvDeviceName,
          tvPinInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
