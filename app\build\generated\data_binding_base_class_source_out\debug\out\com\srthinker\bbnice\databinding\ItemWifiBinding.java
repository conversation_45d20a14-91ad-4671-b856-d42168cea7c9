// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWifiBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivWifiLock;

  @NonNull
  public final ImageView ivWifiSignal;

  @NonNull
  public final TextView tvWifiName;

  @NonNull
  public final TextView tvWifiStatus;

  private ItemWifiBinding(@NonNull CardView rootView, @NonNull ImageView ivWifiLock,
      @NonNull ImageView ivWifiSignal, @NonNull TextView tvWifiName,
      @NonNull TextView tvWifiStatus) {
    this.rootView = rootView;
    this.ivWifiLock = ivWifiLock;
    this.ivWifiSignal = ivWifiSignal;
    this.tvWifiName = tvWifiName;
    this.tvWifiStatus = tvWifiStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWifiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWifiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_wifi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWifiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_wifi_lock;
      ImageView ivWifiLock = ViewBindings.findChildViewById(rootView, id);
      if (ivWifiLock == null) {
        break missingId;
      }

      id = R.id.iv_wifi_signal;
      ImageView ivWifiSignal = ViewBindings.findChildViewById(rootView, id);
      if (ivWifiSignal == null) {
        break missingId;
      }

      id = R.id.tv_wifi_name;
      TextView tvWifiName = ViewBindings.findChildViewById(rootView, id);
      if (tvWifiName == null) {
        break missingId;
      }

      id = R.id.tv_wifi_status;
      TextView tvWifiStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvWifiStatus == null) {
        break missingId;
      }

      return new ItemWifiBinding((CardView) rootView, ivWifiLock, ivWifiSignal, tvWifiName,
          tvWifiStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
