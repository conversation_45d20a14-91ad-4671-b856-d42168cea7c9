package com.srthinker.bbnice.mqtt;

import android.content.Context;
import android.os.Build;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;

/**
 * 安全的MQTT Android客户端包装类
 * 用于处理Android 14的广播接收器注册问题
 */
public class SafeMqttAndroidClient extends MqttAndroidClient {
    
    public SafeMqttAndroidClient(Context context, String serverURI, String clientId) {
        super(context, serverURI, clientId);
    }
    
    /**
     * 安全的连接方法，处理Android 14的兼容性问题
     */
    public void connectSafely(MqttConnectOptions options, Object userContext, IMqttActionListener callback) throws MqttException {
        try {
            // 在Android 14及以上版本，尝试禁用某些可能导致问题的功能
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // 设置更保守的连接选项
                if (options != null) {
                    // 禁用自动重连，我们手动处理重连
                    options.setAutomaticReconnect(false);
                    // 设置较短的Keep Alive间隔
                    if (options.getKeepAliveInterval() > 60) {
                        options.setKeepAliveInterval(60);
                    }
                }
            }
            
            // 调用父类的连接方法
            connect(options, userContext, callback);
        } catch (SecurityException e) {
            // 如果遇到安全异常，尝试使用更简单的连接选项
            if (options != null) {
                MqttConnectOptions simpleOptions = new MqttConnectOptions();
                simpleOptions.setCleanSession(true);
                simpleOptions.setAutomaticReconnect(false);
                simpleOptions.setKeepAliveInterval(30);
                simpleOptions.setConnectionTimeout(10);
                
                if (options.getUserName() != null) {
                    simpleOptions.setUserName(options.getUserName());
                }
                if (options.getPassword() != null) {
                    simpleOptions.setPassword(options.getPassword());
                }
                
                connect(simpleOptions, userContext, callback);
            } else {
                throw e;
            }
        }
    }
}
