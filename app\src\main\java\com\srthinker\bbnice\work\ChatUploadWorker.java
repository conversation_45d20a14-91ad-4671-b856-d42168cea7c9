package com.srthinker.bbnice.work;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.db.ChatRepository;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.HttpUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 聊天记录上传工作器，负责将聊天记录上传到服务器
 */
public class ChatUploadWorker extends Worker {
    private static final String TAG = "ChatUploadWorker";
    private static final int BATCH_SIZE = 50; // 每批上传的消息数量
    private DeviceRepository deviceRepository;
    public ChatUploadWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        deviceRepository = ApiRepositoryProvider.getInstance(context).getDeviceRepository();
    }

    @NonNull
    @Override
    public Result doWork() {
        Log.d(TAG, "Starting chat history upload work");

        Context context = getApplicationContext();
        ChatRepository repository = ChatRepository.getInstance(context);
        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 获取未上传的消息
        List<ChatMessage> messages = repository.getNotUploadedMessages(BATCH_SIZE);

        if (messages.isEmpty()) {
            Log.d(TAG, "No messages to upload");
            return Result.success();
        }

        Log.d(TAG, "Found " + messages.size() + " messages to upload");

//        // 使用CountDownLatch等待上传完成
//        final CountDownLatch latch = new CountDownLatch(1);
//        final AtomicBoolean uploadSuccess = new AtomicBoolean(false);
//
//        // 上传消息
//        deviceRepository.reportChatHistory(messages, new HttpUtils.HttpCallback() {
//            @Override
//            public void onSuccess(String response) {
//                Log.d(TAG, "Yesterday's chat history upload successful: ");
//                uploadSuccess.set(true);
//
//                // 标记消息为已上传
//                for (ChatMessage message : messages) {
//                    repository.markMessageAsUploadedAsync(message.getMessageId());
//                }
//                latch.countDown();
//            }
//
//            @Override
//            public void onFailure(String error) {
//                Log.e(TAG, "Yesterday's chat history upload failed: " + error);
//                latch.countDown();
//            }
//        });
//
//        try {
//            // 等待上传完成，最多等待30秒
//            boolean completed = latch.await(30, TimeUnit.SECONDS);
//
//            if (!completed) {
//                Log.e(TAG, "Upload timed out");
//                return Result.retry();
//            }
//
//            if (uploadSuccess.get()) {
//                return Result.success();
//            } else {
//                return Result.retry();
//            }
//        } catch (InterruptedException e) {
//            Log.e(TAG, "Upload interrupted: " + e.getMessage());
//            Thread.currentThread().interrupt();
//            return Result.retry();
//        }
        return Result.success();
    }
}
