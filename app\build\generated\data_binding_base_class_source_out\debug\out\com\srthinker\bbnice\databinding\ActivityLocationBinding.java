// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLocationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout locStatusContainer;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final SwitchCompat switchLocation;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvLocStatusLabel;

  private ActivityLocationBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout locStatusContainer, @NonNull ConstraintLayout main,
      @NonNull SwitchCompat switchLocation, @NonNull Toolbar toolbar,
      @NonNull TextView tvLocStatusLabel) {
    this.rootView = rootView;
    this.locStatusContainer = locStatusContainer;
    this.main = main;
    this.switchLocation = switchLocation;
    this.toolbar = toolbar;
    this.tvLocStatusLabel = tvLocStatusLabel;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLocationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLocationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_location, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLocationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.loc_status_container;
      ConstraintLayout locStatusContainer = ViewBindings.findChildViewById(rootView, id);
      if (locStatusContainer == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.switch_location;
      SwitchCompat switchLocation = ViewBindings.findChildViewById(rootView, id);
      if (switchLocation == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_loc_status_label;
      TextView tvLocStatusLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvLocStatusLabel == null) {
        break missingId;
      }

      return new ActivityLocationBinding((ConstraintLayout) rootView, locStatusContainer, main,
          switchLocation, toolbar, tvLocStatusLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
