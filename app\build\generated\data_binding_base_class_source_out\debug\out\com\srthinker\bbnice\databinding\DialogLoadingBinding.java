// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogLoadingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout dialogLoadingView;

  @NonNull
  public final ProgressBar progressBar1;

  @NonNull
  public final TextView tipTextView;

  private DialogLoadingBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout dialogLoadingView, @NonNull ProgressBar progressBar1,
      @NonNull TextView tipTextView) {
    this.rootView = rootView;
    this.dialogLoadingView = dialogLoadingView;
    this.progressBar1 = progressBar1;
    this.tipTextView = tipTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogLoadingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogLoadingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_loading, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogLoadingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout dialogLoadingView = (LinearLayout) rootView;

      id = R.id.progressBar1;
      ProgressBar progressBar1 = ViewBindings.findChildViewById(rootView, id);
      if (progressBar1 == null) {
        break missingId;
      }

      id = R.id.tipTextView;
      TextView tipTextView = ViewBindings.findChildViewById(rootView, id);
      if (tipTextView == null) {
        break missingId;
      }

      return new DialogLoadingBinding((LinearLayout) rootView, dialogLoadingView, progressBar1,
          tipTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
