package com.srthinker.bbnice.setting;

import android.net.wifi.ScanResult;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;

import java.util.ArrayList;
import java.util.List;

public class WifiAdapter extends RecyclerView.Adapter<WifiAdapter.WifiViewHolder> {

    private List<ScanResult> wifiList = new ArrayList<>();
    private WifiInfo currentWifi;
    private OnWifiItemClickListener listener;

    public interface OnWifiItemClickListener {
        void onWifiItemClick(ScanResult scanResult);
    }

    public WifiAdapter(OnWifiItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public WifiViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wifi, parent, false);
        return new WifiViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull WifiViewHolder holder, int position) {
        ScanResult scanResult = wifiList.get(position);
        holder.bind(scanResult);
    }

    @Override
    public int getItemCount() {
        return wifiList.size();
    }

    public void updateWifiList(List<ScanResult> wifiList, WifiInfo currentWifi) {
        this.wifiList = wifiList;
        this.currentWifi = currentWifi;
        notifyDataSetChanged();
    }

    class WifiViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivWifiSignal;
        private final TextView tvWifiName;
        private final TextView tvWifiStatus;
        private final ImageView ivWifiLock;

        public WifiViewHolder(@NonNull View itemView) {
            super(itemView);
            ivWifiSignal = itemView.findViewById(R.id.iv_wifi_signal);
            tvWifiName = itemView.findViewById(R.id.tv_wifi_name);
            tvWifiStatus = itemView.findViewById(R.id.tv_wifi_status);
            ivWifiLock = itemView.findViewById(R.id.iv_wifi_lock);

            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onWifiItemClick(wifiList.get(position));
                }
            });
        }

        public void bind(ScanResult scanResult) {
            // 设置WiFi名称
            String ssid = scanResult.SSID;
            if (ssid.isEmpty()) {
                ssid = "Hidden Network";
            }
            tvWifiName.setText(ssid);

            // 设置WiFi状态
            boolean isConnected = false;
            if (currentWifi != null && currentWifi.getSSID() != null) {
                String currentSsid = currentWifi.getSSID().replace("\"", "");
                isConnected = currentSsid.equals(scanResult.SSID);
            }

            if (isConnected) {
                tvWifiStatus.setText(itemView.getContext().getString(R.string.wifi_connected));
            } else {
                tvWifiStatus.setText(itemView.getContext().getString(R.string.wifi_disconnected));
            }

            // 设置WiFi信号强度图标
//            int signalLevel = WifiManager.calculateSignalLevel(scanResult.level, 4);
//            switch (signalLevel) {
//                case 0:
//                    ivWifiSignal.setImageResource(android.R.mipmap.stat_sys_wifi_signal_1);
//                    break;
//                case 1:
//                    ivWifiSignal.setImageResource(android.R.drawable.stat_sys_wifi_signal_2);
//                    break;
//                case 2:
//                    ivWifiSignal.setImageResource(android.R.drawable.stat_sys_wifi_signal_3);
//                    break;
//                case 3:
//                    ivWifiSignal.setImageResource(android.R.drawable.stat_sys_wifi_signal_4);
//                    break;
//                default:
//                    ivWifiSignal.setImageResource(android.R.drawable.stat_sys_wifi);
//                    break;
//            }

            ivWifiSignal.setImageResource(R.mipmap.iocn_wifi);
            // 设置WiFi加密状态
            boolean isSecure = scanResult.capabilities != null &&
                    (scanResult.capabilities.contains("WEP") ||
                     scanResult.capabilities.contains("PSK") ||
                     scanResult.capabilities.contains("EAP"));

            ivWifiLock.setVisibility(isSecure ? View.VISIBLE : View.GONE);
        }
    }
}
