package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * 宠物名称响应类
 * 用于表示更新宠物名称API的响应
 */
public class PetNameResponse extends BaseResponse {
    // 响应数据
    private final PetNameResponseData data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public PetNameResponse(ApiResponse<PetNameResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public PetNameResponseData getData() {
        return data;
    }
    
    /**
     * 宠物名称响应数据类
     */
    public static class PetNameResponseData {
        // 设备ID
        private String device_id;
        
        // 宠物名称
        private String pet_name;
        
        /**
         * 获取设备ID
         * @return 设备ID
         */
        public String getDeviceId() {
            return device_id;
        }
        
        /**
         * 设置设备ID
         * @param device_id 设备ID
         */
        public void setDeviceId(String device_id) {
            this.device_id = device_id;
        }
        
        /**
         * 获取宠物名称
         * @return 宠物名称
         */
        public String getPetName() {
            return pet_name;
        }
        
        /**
         * 设置宠物名称
         * @param pet_name 宠物名称
         */
        public void setPetName(String pet_name) {
            this.pet_name = pet_name;
        }
    }
}
