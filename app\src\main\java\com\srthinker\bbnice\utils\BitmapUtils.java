package com.srthinker.bbnice.utils;

import android.graphics.Bitmap;
import android.util.Log;

import java.io.ByteArrayOutputStream;

public class BitmapUtils {
    private static final String TAG = "BitmapUtils";

    /**
     * 压缩Bitmap到指定大小
     * @param bitmap 原始bitmap
     * @param maxSizeKB 最大大小（KB）
     * @return 压缩后的字节数组
     */
    public static byte[] compressBitmap(Bitmap bitmap, int maxSizeKB) {
        // 检查输入参数
        if (bitmap == null) {
            Log.w(TAG, "Bitmap is null");
            return new byte[0];
        }

        if (bitmap.isRecycled()) {
            Log.w(TAG, "Bitmap is recycled");
            return new byte[0];
        }

        if (maxSizeKB <= 0) {
            Log.w(TAG, "Invalid maxSizeKB: " + maxSizeKB);
            return new byte[0];
        }

        Log.d(TAG, "Original bitmap size: " + bitmap.getWidth() + "x" + bitmap.getHeight());
        Log.d(TAG, "Target size: " + maxSizeKB + "KB");

        // 首先尝试质量压缩
        byte[] result = compressByQuality(bitmap, maxSizeKB);

        // 如果质量压缩不够，尝试尺寸+质量压缩
        if (result.length / 1024 > maxSizeKB) {
            Log.d(TAG, "Quality compression not enough, trying size compression");
            result = compressBySizeAndQuality(bitmap, maxSizeKB);
        }

        Log.d(TAG, "Final compressed size: " + (result.length / 1024) + "KB");
        return result;
    }

    /**
     * 通过质量压缩
     */
    private static byte[] compressByQuality(Bitmap bitmap, int maxSizeKB) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int quality = 100;

        // 初始压缩
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);
        int currentSizeKB = outputStream.toByteArray().length / 1024;
        Log.d(TAG, "Initial size at quality " + quality + ": " + currentSizeKB + "KB");

        // 如果初始大小就符合要求，直接返回
        if (currentSizeKB <= maxSizeKB) {
            return outputStream.toByteArray();
        }

        // 二分查找最优质量值
        int minQuality = 1;
        int maxQuality = 100;
        byte[] bestResult = outputStream.toByteArray();

        while (minQuality <= maxQuality) {
            quality = (minQuality + maxQuality) / 2;
            outputStream.reset();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);

            byte[] currentResult = outputStream.toByteArray();
            currentSizeKB = currentResult.length / 1024;

            Log.d(TAG, "Quality " + quality + " -> " + currentSizeKB + "KB");

            if (currentSizeKB <= maxSizeKB) {
                bestResult = currentResult;
                minQuality = quality + 1; // 尝试更高质量
            } else {
                maxQuality = quality - 1; // 降低质量
            }
        }

        return bestResult;
    }

    /**
     * 通过尺寸和质量双重压缩
     */
    private static byte[] compressBySizeAndQuality(Bitmap bitmap, int maxSizeKB) {
        // 计算需要缩放的比例
        float scale = calculateScaleRatio(bitmap, maxSizeKB);

        if (scale >= 1.0f) {
            // 不需要缩放，直接质量压缩
            return compressByQuality(bitmap, maxSizeKB);
        }

        Log.d(TAG, "Scaling bitmap by factor: " + scale);

        // 缩放bitmap
        int newWidth = (int) (bitmap.getWidth() * scale);
        int newHeight = (int) (bitmap.getHeight() * scale);

        Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);
        Log.d(TAG, "Scaled bitmap size: " + newWidth + "x" + newHeight);

        // 对缩放后的bitmap进行质量压缩
        byte[] result = compressByQuality(scaledBitmap, maxSizeKB);

        // 回收缩放后的bitmap（如果不是原bitmap）
        if (scaledBitmap != bitmap && !scaledBitmap.isRecycled()) {
            scaledBitmap.recycle();
        }

        return result;
    }

    /**
     * 计算缩放比例
     */
    private static float calculateScaleRatio(Bitmap bitmap, int maxSizeKB) {
        // 估算当前bitmap的大小（粗略估算）
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        // 假设每个像素平均占用2字节（JPEG压缩后）
        int estimatedSizeKB = (width * height * 2) / 1024;

        if (estimatedSizeKB <= maxSizeKB) {
            return 1.0f; // 不需要缩放
        }

        // 计算需要的缩放比例
        float ratio = (float) Math.sqrt((double) maxSizeKB / estimatedSizeKB);

        // 确保缩放比例在合理范围内
        return Math.max(0.1f, Math.min(1.0f, ratio));
    }
}
