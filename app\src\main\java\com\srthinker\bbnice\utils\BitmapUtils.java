package com.srthinker.bbnice.utils;

import android.graphics.Bitmap;
import android.util.Log;

import java.io.ByteArrayOutputStream;

public class BitmapUtils {
    private static final String TAG = "BitmapUtils";

    /**
     * 压缩Bitmap到指定大小
     * @param bitmap 原始bitmap
     * @param maxSizeKB 最大大小（KB）
     * @return 压缩后的字节数组
     */
    public static byte[] compressBitmap(Bitmap bitmap, int maxSizeKB) {
        // 检查输入参数
        if (bitmap == null) {
            Log.w(TAG, "Bitmap is null");
            return new byte[0];
        }

        if (bitmap.isRecycled()) {
            Log.w(TAG, "Bitmap is recycled");
            return new byte[0];
        }

        if (maxSizeKB <= 0) {
            Log.w(TAG, "Invalid maxSizeKB: " + maxSizeKB);
            return new byte[0];
        }

        Log.d(TAG, "Original bitmap size: " + bitmap.getWidth() + "x" + bitmap.getHeight());
        Log.d(TAG, "Target size: " + maxSizeKB + "KB");

        // 首先尝试质量压缩
        byte[] result = compressByQuality(bitmap, maxSizeKB);

        // 如果质量压缩不够，尝试尺寸+质量压缩
        if (result.length / 1024 > maxSizeKB) {
            Log.d(TAG, "Quality compression not enough, trying size compression");
            result = compressBySizeAndQuality(bitmap, maxSizeKB);
        }

        Log.d(TAG, "Final compressed size: " + (result.length / 1024) + "KB");
        return result;
    }

    /**
     * 通过质量压缩
     */
    private static byte[] compressByQuality(Bitmap bitmap, int maxSizeKB) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int quality = 100;

        // 初始压缩
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);
        int currentSizeKB = outputStream.toByteArray().length / 1024;
        Log.d(TAG, "Initial size at quality " + quality + ": " + currentSizeKB + "KB");

        // 如果初始大小就符合要求，直接返回
        if (currentSizeKB <= maxSizeKB) {
            return outputStream.toByteArray();
        }

        // 二分查找最优质量值
        int minQuality = 1;
        int maxQuality = 100;
        byte[] bestResult = outputStream.toByteArray();

        while (minQuality <= maxQuality) {
            quality = (minQuality + maxQuality) / 2;
            outputStream.reset();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);

            byte[] currentResult = outputStream.toByteArray();
            currentSizeKB = currentResult.length / 1024;

            Log.d(TAG, "Quality " + quality + " -> " + currentSizeKB + "KB");

            if (currentSizeKB <= maxSizeKB) {
                bestResult = currentResult;
                minQuality = quality + 1; // 尝试更高质量
            } else {
                maxQuality = quality - 1; // 降低质量
            }
        }

        return bestResult;
    }

    /**
     * 通过尺寸和质量双重压缩
     */
    private static byte[] compressBySizeAndQuality(Bitmap bitmap, int maxSizeKB) {
        // 计算需要缩放的比例
        float scale = calculateScaleRatio(bitmap, maxSizeKB);

        if (scale >= 1.0f) {
            // 不需要缩放，直接质量压缩
            return compressByQuality(bitmap, maxSizeKB);
        }

        Log.d(TAG, "Scaling bitmap by factor: " + scale);

        // 缩放bitmap
        int newWidth = (int) (bitmap.getWidth() * scale);
        int newHeight = (int) (bitmap.getHeight() * scale);

        Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);
        Log.d(TAG, "Scaled bitmap size: " + newWidth + "x" + newHeight);

        // 对缩放后的bitmap进行质量压缩
        byte[] result = compressByQuality(scaledBitmap, maxSizeKB);

        // 回收缩放后的bitmap（如果不是原bitmap）
        if (scaledBitmap != bitmap && !scaledBitmap.isRecycled()) {
            scaledBitmap.recycle();
        }

        return result;
    }

    /**
     * 计算缩放比例
     */
    private static float calculateScaleRatio(Bitmap bitmap, int maxSizeKB) {
        // 估算当前bitmap的大小（粗略估算）
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        // 假设每个像素平均占用2字节（JPEG压缩后）
        int estimatedSizeKB = (width * height * 2) / 1024;

        if (estimatedSizeKB <= maxSizeKB) {
            return 1.0f; // 不需要缩放
        }

        // 计算需要的缩放比例
        float ratio = (float) Math.sqrt((double) maxSizeKB / estimatedSizeKB);

        // 确保缩放比例在合理范围内
        return Math.max(0.1f, Math.min(1.0f, ratio));
    }

    /**
     * 缩放Bitmap到指定尺寸，保持原图比例
     * @param bitmap 原始bitmap
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 缩放后的bitmap，如果输入无效则返回null
     */
    public static Bitmap resize(Bitmap bitmap, int targetWidth, int targetHeight) {
        return resize(bitmap, targetWidth, targetHeight, true);
    }

    /**
     * 缩放Bitmap到指定尺寸
     * @param bitmap 原始bitmap
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @param keepAspectRatio 是否保持原图比例
     * @return 缩放后的bitmap，如果输入无效则返回null
     */
    public static Bitmap resize(Bitmap bitmap, int targetWidth, int targetHeight, boolean keepAspectRatio) {
        // 检查输入参数
        if (bitmap == null) {
            Log.w(TAG, "Bitmap is null");
            return null;
        }

        if (bitmap.isRecycled()) {
            Log.w(TAG, "Bitmap is recycled");
            return null;
        }

        if (targetWidth <= 0 || targetHeight <= 0) {
            Log.w(TAG, "Invalid target size: " + targetWidth + "x" + targetHeight);
            return null;
        }

        int originalWidth = bitmap.getWidth();
        int originalHeight = bitmap.getHeight();

        Log.d(TAG, "Resizing bitmap from " + originalWidth + "x" + originalHeight +
              " to " + targetWidth + "x" + targetHeight +
              " (keepAspectRatio: " + keepAspectRatio + ")");

        // 如果尺寸已经相同，直接返回原图
        if (originalWidth == targetWidth && originalHeight == targetHeight) {
            Log.d(TAG, "Size already matches, returning original bitmap");
            return bitmap;
        }

        int finalWidth, finalHeight;

        if (keepAspectRatio) {
            // 计算保持比例的最终尺寸
            ResizeResult result = calculateAspectRatioSize(originalWidth, originalHeight, targetWidth, targetHeight);
            finalWidth = result.width;
            finalHeight = result.height;

            Log.d(TAG, "Calculated aspect ratio size: " + finalWidth + "x" + finalHeight);
        } else {
            // 直接使用目标尺寸
            finalWidth = targetWidth;
            finalHeight = targetHeight;
        }

        try {
            // 执行缩放
            Bitmap resizedBitmap = Bitmap.createScaledBitmap(bitmap, finalWidth, finalHeight, true);
            Log.d(TAG, "Resize completed successfully");
            return resizedBitmap;
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "Out of memory while resizing bitmap", e);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error resizing bitmap", e);
            return null;
        }
    }

    /**
     * 按比例缩放Bitmap
     * @param bitmap 原始bitmap
     * @param scale 缩放比例（1.0为原始大小，0.5为一半大小，2.0为两倍大小）
     * @return 缩放后的bitmap，如果输入无效则返回null
     */
    public static Bitmap resize(Bitmap bitmap, float scale) {
        if (bitmap == null) {
            Log.w(TAG, "Bitmap is null");
            return null;
        }

        if (bitmap.isRecycled()) {
            Log.w(TAG, "Bitmap is recycled");
            return null;
        }

        if (scale <= 0) {
            Log.w(TAG, "Invalid scale: " + scale);
            return null;
        }

        // 如果缩放比例为1.0，直接返回原图
        if (Math.abs(scale - 1.0f) < 0.001f) {
            Log.d(TAG, "Scale is 1.0, returning original bitmap");
            return bitmap;
        }

        int originalWidth = bitmap.getWidth();
        int originalHeight = bitmap.getHeight();
        int targetWidth = Math.round(originalWidth * scale);
        int targetHeight = Math.round(originalHeight * scale);

        Log.d(TAG, "Scaling bitmap by " + scale + " from " + originalWidth + "x" + originalHeight +
              " to " + targetWidth + "x" + targetHeight);

        return resize(bitmap, targetWidth, targetHeight, false);
    }

    /**
     * 计算保持宽高比的目标尺寸
     */
    private static ResizeResult calculateAspectRatioSize(int originalWidth, int originalHeight,
                                                        int targetWidth, int targetHeight) {
        // 计算宽高比
        float originalRatio = (float) originalWidth / originalHeight;
        float targetRatio = (float) targetWidth / targetHeight;

        int finalWidth, finalHeight;

        if (originalRatio > targetRatio) {
            // 原图更宽，以目标宽度为准
            finalWidth = targetWidth;
            finalHeight = Math.round(targetWidth / originalRatio);
        } else {
            // 原图更高，以目标高度为准
            finalHeight = targetHeight;
            finalWidth = Math.round(targetHeight * originalRatio);
        }

        // 确保尺寸不超过目标尺寸
        if (finalWidth > targetWidth) {
            finalWidth = targetWidth;
            finalHeight = Math.round(targetWidth / originalRatio);
        }
        if (finalHeight > targetHeight) {
            finalHeight = targetHeight;
            finalWidth = Math.round(targetHeight * originalRatio);
        }

        return new ResizeResult(finalWidth, finalHeight);
    }

    /**
     * 缩放结果辅助类
     */
    private static class ResizeResult {
        final int width;
        final int height;

        ResizeResult(int width, int height) {
            this.width = width;
            this.height = height;
        }
    }
}
