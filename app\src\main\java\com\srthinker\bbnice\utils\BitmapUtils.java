package com.srthinker.bbnice.utils;

import android.graphics.Bitmap;

import java.io.ByteArrayOutputStream;

public class BitmapUtils {
    // Bitmap压缩方法
    public static byte[] compressBitmap(Bitmap bitmap, int maxSizeKB) {
        // 检查输入参数
        if (bitmap == null) {
            return new byte[0]; // 返回空字节数组而不是null
        }

        if (bitmap.isRecycled()) {
            return new byte[0]; // 返回空字节数组而不是null
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int quality = 100;
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);

        while (outputStream.toByteArray().length / 1024 > maxSizeKB && quality > 10) {
            outputStream.reset();
            quality -= 15;
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);
        }
        return outputStream.toByteArray();
    }
}
