package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * 二维码响应类
 * 用于表示获取二维码API的响应
 */
public class QrCodeResponse extends BaseResponse {
    // 响应数据
    private final QrCodeResponseData data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public QrCodeResponse(ApiResponse<QrCodeResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public QrCodeResponseData getData() {
        return data;
    }

    @Override
    public String toString() {
        return "QrCodeResponse{" +
                "data=" + data +
                '}';
    }

    /**
     * 二维码响应数据类
     */
    public static class QrCodeResponseData {
        // 二维码URL
        private String qrcode_url;
        
        /**
         * 获取二维码URL
         * @return 二维码URL
         */
        public String getQrcodeUrl() {
            return qrcode_url;
        }
        
        /**
         * 设置二维码URL
         * @param qrcode_url 二维码URL
         */
        public void setQrcodeUrl(String qrcode_url) {
            this.qrcode_url = qrcode_url;
        }

        @Override
        public String toString() {
            return "QrCodeResponseData{" +
                    "qrcode_url='" + qrcode_url + '\'' +
                    '}';
        }
    }
}
