<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>org.opencv.objdetect Class Hierarchy (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="tree: package: org.opencv.objdetect">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.objdetect</h1>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.opencv.core.<a href="../core/Algorithm.html" class="type-name-link" title="class in org.opencv.core">Algorithm</a>
<ul>
<li class="circle">org.opencv.objdetect.<a href="ArucoDetector.html" class="type-name-link" title="class in org.opencv.objdetect">ArucoDetector</a></li>
<li class="circle">org.opencv.objdetect.<a href="BaseCascadeClassifier.html" class="type-name-link" title="class in org.opencv.objdetect">BaseCascadeClassifier</a></li>
<li class="circle">org.opencv.objdetect.<a href="CharucoDetector.html" class="type-name-link" title="class in org.opencv.objdetect">CharucoDetector</a></li>
</ul>
</li>
<li class="circle">org.opencv.objdetect.<a href="Board.html" class="type-name-link" title="class in org.opencv.objdetect">Board</a>
<ul>
<li class="circle">org.opencv.objdetect.<a href="CharucoBoard.html" class="type-name-link" title="class in org.opencv.objdetect">CharucoBoard</a></li>
<li class="circle">org.opencv.objdetect.<a href="GridBoard.html" class="type-name-link" title="class in org.opencv.objdetect">GridBoard</a></li>
</ul>
</li>
<li class="circle">org.opencv.objdetect.<a href="CascadeClassifier.html" class="type-name-link" title="class in org.opencv.objdetect">CascadeClassifier</a></li>
<li class="circle">org.opencv.objdetect.<a href="CharucoParameters.html" class="type-name-link" title="class in org.opencv.objdetect">CharucoParameters</a></li>
<li class="circle">org.opencv.objdetect.<a href="DetectorParameters.html" class="type-name-link" title="class in org.opencv.objdetect">DetectorParameters</a></li>
<li class="circle">org.opencv.objdetect.<a href="Dictionary.html" class="type-name-link" title="class in org.opencv.objdetect">Dictionary</a></li>
<li class="circle">org.opencv.objdetect.<a href="FaceDetectorYN.html" class="type-name-link" title="class in org.opencv.objdetect">FaceDetectorYN</a></li>
<li class="circle">org.opencv.objdetect.<a href="FaceRecognizerSF.html" class="type-name-link" title="class in org.opencv.objdetect">FaceRecognizerSF</a></li>
<li class="circle">org.opencv.objdetect.<a href="GraphicalCodeDetector.html" class="type-name-link" title="class in org.opencv.objdetect">GraphicalCodeDetector</a>
<ul>
<li class="circle">org.opencv.objdetect.<a href="BarcodeDetector.html" class="type-name-link" title="class in org.opencv.objdetect">BarcodeDetector</a></li>
<li class="circle">org.opencv.objdetect.<a href="QRCodeDetector.html" class="type-name-link" title="class in org.opencv.objdetect">QRCodeDetector</a></li>
<li class="circle">org.opencv.objdetect.<a href="QRCodeDetectorAruco.html" class="type-name-link" title="class in org.opencv.objdetect">QRCodeDetectorAruco</a></li>
</ul>
</li>
<li class="circle">org.opencv.objdetect.<a href="HOGDescriptor.html" class="type-name-link" title="class in org.opencv.objdetect">HOGDescriptor</a></li>
<li class="circle">org.opencv.objdetect.<a href="Objdetect.html" class="type-name-link" title="class in org.opencv.objdetect">Objdetect</a></li>
<li class="circle">org.opencv.objdetect.<a href="QRCodeDetectorAruco_Params.html" class="type-name-link" title="class in org.opencv.objdetect">QRCodeDetectorAruco_Params</a></li>
<li class="circle">org.opencv.objdetect.<a href="QRCodeEncoder.html" class="type-name-link" title="class in org.opencv.objdetect">QRCodeEncoder</a></li>
<li class="circle">org.opencv.objdetect.<a href="QRCodeEncoder_Params.html" class="type-name-link" title="class in org.opencv.objdetect">QRCodeEncoder_Params</a></li>
<li class="circle">org.opencv.objdetect.<a href="RefineParameters.html" class="type-name-link" title="class in org.opencv.objdetect">RefineParameters</a></li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
