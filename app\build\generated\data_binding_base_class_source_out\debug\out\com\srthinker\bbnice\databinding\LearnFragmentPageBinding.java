// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LearnFragmentPageBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnFinish;

  @NonNull
  public final ImageView btnTalk;

  @NonNull
  public final TextView hint;

  @NonNull
  public final ImageView imgIcon;

  @NonNull
  public final TextView tvName;

  private LearnFragmentPageBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView btnFinish,
      @NonNull ImageView btnTalk, @NonNull TextView hint, @NonNull ImageView imgIcon,
      @NonNull TextView tvName) {
    this.rootView = rootView;
    this.btnFinish = btnFinish;
    this.btnTalk = btnTalk;
    this.hint = hint;
    this.imgIcon = imgIcon;
    this.tvName = tvName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LearnFragmentPageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LearnFragmentPageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.learn_fragment_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LearnFragmentPageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_finish;
      ImageView btnFinish = ViewBindings.findChildViewById(rootView, id);
      if (btnFinish == null) {
        break missingId;
      }

      id = R.id.btn_talk;
      ImageView btnTalk = ViewBindings.findChildViewById(rootView, id);
      if (btnTalk == null) {
        break missingId;
      }

      id = R.id.hint;
      TextView hint = ViewBindings.findChildViewById(rootView, id);
      if (hint == null) {
        break missingId;
      }

      id = R.id.img_icon;
      ImageView imgIcon = ViewBindings.findChildViewById(rootView, id);
      if (imgIcon == null) {
        break missingId;
      }

      id = R.id.tv_name;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      return new LearnFragmentPageBinding((ConstraintLayout) rootView, btnFinish, btnTalk, hint,
          imgIcon, tvName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
