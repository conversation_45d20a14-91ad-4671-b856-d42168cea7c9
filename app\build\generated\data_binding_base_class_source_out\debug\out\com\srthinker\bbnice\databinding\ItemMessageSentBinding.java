// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMessageSentBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView ivOtherAvatar;

  @NonNull
  public final TextView textViewMessageSent;

  private ItemMessageSentBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView ivOtherAvatar, @NonNull TextView textViewMessageSent) {
    this.rootView = rootView;
    this.ivOtherAvatar = ivOtherAvatar;
    this.textViewMessageSent = textViewMessageSent;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMessageSentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMessageSentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_message_sent, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMessageSentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_other_avatar;
      ImageView ivOtherAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivOtherAvatar == null) {
        break missingId;
      }

      id = R.id.textViewMessageSent;
      TextView textViewMessageSent = ViewBindings.findChildViewById(rootView, id);
      if (textViewMessageSent == null) {
        break missingId;
      }

      return new ItemMessageSentBinding((ConstraintLayout) rootView, ivOtherAvatar,
          textViewMessageSent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
