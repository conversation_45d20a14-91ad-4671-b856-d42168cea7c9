package com.srthinker.bbnice.core;

/**
 * 用于封装操作结果的通用类
 * @param <T> 结果数据类型
 */
public class Result<T> {
    private final Status status;
    private final T data;
    private final Throwable error;
    
    private Result(Status status, T data, Throwable error) {
        this.status = status;
        this.data = data;
        this.error = error;
    }
    
    public enum Status {
        SUCCESS,
        ERROR,
        LOADING
    }
    
    public static <T> Result<T> success(T data) {
        return new Result<>(Status.SUCCESS, data, null);
    }
    
    public static <T> Result<T> error(Throwable error) {
        return new Result<>(Status.ERROR, null, error);
    }
    
    public static <T> Result<T> loading() {
        return new Result<>(Status.LOADING, null, null);
    }
    
    public Status getStatus() {
        return status;
    }
    
    public T getData() {
        return data;
    }
    
    public Throwable getError() {
        return error;
    }
    
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    public boolean isError() {
        return status == Status.ERROR;
    }
    
    public boolean isLoading() {
        return status == Status.LOADING;
    }
}
