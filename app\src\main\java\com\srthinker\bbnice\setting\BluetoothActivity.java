package com.srthinker.bbnice.setting;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.utils.BluetoothHelper;

import java.util.ArrayList;
import java.util.List;
@SuppressLint("MissingPermission")
public class BluetoothActivity extends AppCompatActivity implements BluetoothDeviceAdapter.OnDeviceClickListener, BluetoothHelper.BluetoothCallback {

    private static final String TAG = "BluetoothActivity";
    private static final int REQUEST_ENABLE_BT = 1;

    private Toolbar toolbar;
    private SwitchCompat switchBluetooth;
    private RecyclerView rvPairedDevices;
    private RecyclerView rvAvailableDevices;
    private SwipeRefreshLayout swipeRefresh;
    private ProgressBar progressBar;
    private TextView tvEmptyState;

    private BluetoothDeviceAdapter pairedDevicesAdapter;
    private BluetoothDeviceAdapter availableDevicesAdapter;
    
    // 蓝牙辅助类
    private BluetoothHelper bluetoothHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetooth);

        // 初始化视图
        initViews();
        
        // 初始化蓝牙辅助类
        bluetoothHelper = new BluetoothHelper(this);
        bluetoothHelper.setCallback(this);
        
        // 检查设备是否支持蓝牙
        if (!bluetoothHelper.isBluetoothAvailable()) {
            Toast.makeText(this, R.string.bluetooth_not_supported, Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // 初始化适配器
        pairedDevicesAdapter = new BluetoothDeviceAdapter(this, true);
        rvPairedDevices.setLayoutManager(new LinearLayoutManager(this));
        rvPairedDevices.setAdapter(pairedDevicesAdapter);
        
        availableDevicesAdapter = new BluetoothDeviceAdapter(this, false);
        rvAvailableDevices.setLayoutManager(new LinearLayoutManager(this));
        rvAvailableDevices.setAdapter(availableDevicesAdapter);
        
        // 设置下拉刷新监听器
        swipeRefresh.setOnRefreshListener(this::scanBluetoothDevices);
        
        // 设置蓝牙开关监听器
        switchBluetooth.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                if (isChecked) {
                    enableBluetooth();
                } else {
                    disableBluetooth();
                }
            }
        });
        
        // 更新蓝牙状态
        updateBluetoothState(bluetoothHelper.isBluetoothEnabled() ? 
                BluetoothAdapter.STATE_ON : BluetoothAdapter.STATE_OFF);
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.bluetooth_settings));

        switchBluetooth = findViewById(R.id.switch_bluetooth);
        rvPairedDevices = findViewById(R.id.rv_paired_devices);
        rvAvailableDevices = findViewById(R.id.rv_available_devices);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        progressBar = findViewById(R.id.progress_bar);
        tvEmptyState = findViewById(R.id.tv_empty_state);
    }

    private void updateBluetoothState(int state) {
        switch (state) {
            case BluetoothAdapter.STATE_ON:
                switchBluetooth.setChecked(true);
                updateDeviceLists();
                break;
            case BluetoothAdapter.STATE_OFF:
                switchBluetooth.setChecked(false);
                clearDeviceLists();
                break;
            case BluetoothAdapter.STATE_TURNING_ON:
                switchBluetooth.setChecked(true);
                showLoading();
                break;
            case BluetoothAdapter.STATE_TURNING_OFF:
                switchBluetooth.setChecked(false);
                showLoading();
                break;
        }
    }

    private void enableBluetooth() {
        if (!bluetoothHelper.isBluetoothEnabled()) {
            if (!bluetoothHelper.hasBluetoothPermissions()) {
                bluetoothHelper.requestBluetoothPermissions(this);
            } else {
                bluetoothHelper.requestEnableBluetooth(this);
            }
        }
    }

    private void disableBluetooth() {
        if (bluetoothHelper.isBluetoothEnabled()) {
            if (bluetoothHelper.hasBluetoothPermissions()) {
                bluetoothHelper.disableBluetooth();
                clearDeviceLists();
            } else {
                bluetoothHelper.requestBluetoothPermissions(this);
            }
        }
    }

    private void scanBluetoothDevices() {
        if (!bluetoothHelper.isBluetoothEnabled()) {
            hideLoading();
            return;
        }

        if (!bluetoothHelper.hasBluetoothPermissions()) {
            bluetoothHelper.requestBluetoothPermissions(this);
            hideLoading();
            return;
        }

        showLoading();
        boolean success = bluetoothHelper.startScan();
        if (!success) {
            hideLoading();
            Toast.makeText(this, R.string.bluetooth_scan_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void updateDeviceLists() {
        if (!bluetoothHelper.isBluetoothEnabled()) {
            return;
        }

        if (!bluetoothHelper.hasBluetoothPermissions()) {
            bluetoothHelper.requestBluetoothPermissions(this);
            return;
        }

        // 更新设备列表
        bluetoothHelper.updateDeviceLists();
        
        // 获取设备列表
        List<BluetoothDevice> pairedDevices = bluetoothHelper.getPairedDevices();
        List<BluetoothDevice> availableDevices = bluetoothHelper.getAvailableDevices();
        List<BluetoothDevice> connectedDevices = bluetoothHelper.getConnectedDevices();
        
        // 更新适配器
        pairedDevicesAdapter.updateDeviceList(pairedDevices, connectedDevices);
        availableDevicesAdapter.updateDeviceList(availableDevices, connectedDevices);
        
        // 检查空状态
        if (availableDevices.isEmpty()) {
            showEmptyState();
        } else {
            hideEmptyState();
        }
        
        // 扫描可用设备
        scanBluetoothDevices();
    }

    private void clearDeviceLists() {
        pairedDevicesAdapter.updateDeviceList(new ArrayList<>(), new ArrayList<>());
        availableDevicesAdapter.updateDeviceList(new ArrayList<>(), new ArrayList<>());
        hideLoading();
        showEmptyState();
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        swipeRefresh.setRefreshing(false);
    }

    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        swipeRefresh.setRefreshing(false);
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
    }

    @Override
    public void onDeviceClick(BluetoothDevice device) {
        if (!bluetoothHelper.hasBluetoothPermissions()) {
            bluetoothHelper.requestBluetoothPermissions(this);
            return;
        }

        // 检查设备配对状态
        int bondState = device.getBondState();
        if (bondState == BluetoothDevice.BOND_BONDED) {
            // 已配对设备，显示连接/断开选项
            showPairedDeviceDialog(device);
        } else {
            // 未配对设备，开始配对
            bluetoothHelper.pairDevice(device);
        }
    }

    private void showPairedDeviceDialog(BluetoothDevice device) {
        boolean isConnected = bluetoothHelper.isDeviceConnected(device);
        String[] options;
        if (isConnected) {
            options = new String[]{getString(R.string.bluetooth_disconnect), getString(R.string.bluetooth_unpair)};
        } else {
            options = new String[]{getString(R.string.bluetooth_connect), getString(R.string.bluetooth_unpair)};
        }

        new AlertDialog.Builder(this)
                .setTitle(device.getName())
                .setItems(options, (dialog, which) -> {
                    if (which == 0) {
                        if (isConnected) {
                            bluetoothHelper.disconnectDevice(device);
                        } else {
                            bluetoothHelper.connectDevice(device);
                        }
                    } else if (which == 1) {
                        bluetoothHelper.unpairDevice(device);
                    }
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 注册蓝牙广播接收器
        bluetoothHelper.registerReceiver();
        
        // 更新蓝牙状态
        updateBluetoothState(bluetoothHelper.isBluetoothEnabled() ? 
                BluetoothAdapter.STATE_ON : BluetoothAdapter.STATE_OFF);
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 注销蓝牙广播接收器
        bluetoothHelper.unregisterReceiver();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放资源
        bluetoothHelper.release();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (bluetoothHelper.hasBluetoothPermissions()) {
            updateDeviceLists();
        } else {
            Toast.makeText(this, R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                // 蓝牙已启用
                updateBluetoothState(BluetoothAdapter.STATE_ON);
            } else {
                // 用户拒绝启用蓝牙
                switchBluetooth.setChecked(false);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // BluetoothHelper.BluetoothCallback 实现
    @Override
    public void onBluetoothStateChanged(int state) {
        runOnUiThread(() -> updateBluetoothState(state));
    }

    @Override
    public void onDeviceDiscovered(BluetoothDevice device) {
        runOnUiThread(() -> {
            hideEmptyState();
            updateDeviceLists();
        });
    }

    @Override
    public void onDeviceConnected(BluetoothDevice device) {
        runOnUiThread(() -> {
            Toast.makeText(this, getString(R.string.bluetooth_connected) + ": " + device.getName(), 
                    Toast.LENGTH_SHORT).show();
            updateDeviceLists();
        });
    }

    @Override
    public void onDeviceDisconnected(BluetoothDevice device) {
        runOnUiThread(() -> {
            Toast.makeText(this, getString(R.string.bluetooth_disconnect) + ": " + device.getName(), 
                    Toast.LENGTH_SHORT).show();
            updateDeviceLists();
        });
    }

    @Override
    public void onDevicePaired(BluetoothDevice device) {
        runOnUiThread(() -> {
            Toast.makeText(this, getString(R.string.bluetooth_paired) + ": " + device.getName(), 
                    Toast.LENGTH_SHORT).show();
            updateDeviceLists();
        });
    }

    @Override
    public void onDeviceUnpaired(BluetoothDevice device) {
        runOnUiThread(() -> {
            Toast.makeText(this, getString(R.string.bluetooth_unpair) + ": " + device.getName(), 
                    Toast.LENGTH_SHORT).show();
            updateDeviceLists();
        });
    }

    @Override
    public void onScanStarted() {
        runOnUiThread(this::showLoading);
    }

    @Override
    public void onScanFinished() {
        runOnUiThread(() -> {
            hideLoading();
            if (bluetoothHelper.getAvailableDevices().isEmpty()) {
                showEmptyState();
            }
        });
    }

    @Override
    public void onError(int errorCode, String message) {
        runOnUiThread(() -> {
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            hideLoading();
        });
    }
}
