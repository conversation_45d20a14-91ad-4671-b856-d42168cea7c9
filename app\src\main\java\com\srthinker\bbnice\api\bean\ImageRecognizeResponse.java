package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * 图片识别响应类
 * 用于表示图片识别API的响应
 */
public class ImageRecognizeResponse extends BaseResponse {
    // 响应数据
    private final ImageRecognizeResponseData data;

    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public ImageRecognizeResponse(ApiResponse<ImageRecognizeResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }

    /**
     * 获取响应数据
     * @return 响应数据
     */
    public ImageRecognizeResponseData getData() {
        return data;
    }

    /**
     * 图片识别响应数据类
     */
    public static class ImageRecognizeResponseData {
        // 识别出的物体名称
        private String text;

        // 英文名称
        private String en;

        // 识别物体的置信度
        private float confidence;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getEn() {
            return en;
        }

        public void setEn(String en) {
            this.en = en;
        }

        public float getConfidence() {
            return confidence;
        }

        public void setConfidence(float confidence) {
            this.confidence = confidence;
        }
    }
}
