#include <jni.h>
#include <string>
#include <android/log.h>
#include <android/bitmap.h>
#include <opencv2/opencv.hpp>
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>

#define LOG_TAG "VideoASStabilizer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

using namespace cv;
using namespace std;

// 全局变量
bool isInitialized = false;
bool isTracking = true;
int frameIndex = 0;
float stabilizationStrength = 0.5f;

// 最大支持的视频尺寸
const int MAX_WIDTH = 2048;
const int MAX_HEIGHT = 2048;

// 防抖模式
enum StabilizationMode {
    NONE = 0,       // 不使用防抖
    BASIC = 1,      // 基础防抖（OpenCV实现）
    SDK_STAB = 2,   // SDK视频防抖
    ADVANCED = 3    // 高级防抖（特征点匹配）
};

// 当前防抖模式
StabilizationMode currentMode = BASIC;

// 存储前一帧和当前帧
Mat prevFrame;
Mat currFrame;

// 存储变换矩阵历史
deque<Mat> transformations;
const int SMOOTHING_RADIUS = 5;

/**
 * 初始化视频防抖
 */
bool initVideoStabilizer() {
    if (isInitialized) {
        return true;
    }
    
    try {
        // 初始化OpenCV相关资源
        prevFrame.release();
        transformations.clear();
        frameIndex = 0;
        isTracking = true;
        isInitialized = true;
        LOGI("Video stabilizer initialized");
        return true;
    } catch (cv::Exception& e) {
        LOGE("Failed to initialize video stabilizer: %s", e.what());
        return false;
    }
}

/**
 * 释放视频防抖资源
 */
void releaseVideoStabilizer() {
    if (!isInitialized) {
        return;
    }
    
    try {
        // 释放OpenCV相关资源
        prevFrame.release();
        transformations.clear();
        isInitialized = false;
        LOGI("Video stabilizer released");
    } catch (cv::Exception& e) {
        LOGE("Error releasing video stabilizer: %s", e.what());
    }
}

/**
 * 平滑变换矩阵
 */
Mat smoothTransformation(const Mat& transform) {
    // 添加当前变换到队列
    transformations.push_back(transform.clone());

    // 保持队列大小不超过平滑窗口
    int windowSize = max(3, min(15, static_cast<int>(SMOOTHING_RADIUS * (1.0 + stabilizationStrength))));
    if (transformations.size() > windowSize * 2 + 1) {
        transformations.pop_front();
    }

    // 计算平滑后的变换
    Mat smoothTransform = Mat::eye(3, 3, CV_64F);
    if (transformations.size() > 0) {
        // 使用加权平均平滑，最近的帧权重更高
        double totalWeight = 0.0;
        for (size_t i = 0; i < transformations.size(); i++) {
            // 计算权重，越近的帧权重越大
            double weight = 1.0;
            if (transformations.size() > 1) {
                // 使用高斯权重
                double sigma = transformations.size() / 3.0;
                double x = static_cast<double>(i) - transformations.size() / 2.0;
                weight = exp(-(x * x) / (2 * sigma * sigma));
            }

            smoothTransform += transformations[i] * weight;
            totalWeight += weight;
        }

        if (totalWeight > 0) {
            smoothTransform /= totalWeight;
        }

        // 根据防抖强度调整平滑程度
        if (stabilizationStrength < 1.0) {
            // 计算原始变换和平滑变换的加权平均
            double alpha = stabilizationStrength;
            smoothTransform = smoothTransform * alpha + transform * (1.0 - alpha);
        }
    }

    return smoothTransform;
}

/**
 * 处理I420格式的视频帧
 */
bool stabilizeI420Frame(
        uint8_t* yData, uint8_t* uData, uint8_t* vData,
        int width, int height, int yStride, int uStride, int vStride,
        bool processLumaOnly) {
    
    if (!isInitialized) {
        if (!initVideoStabilizer()) {
            return false;
        }
    }
    
    // 检查视频尺寸是否超过限制
    if (width > MAX_WIDTH || height > MAX_HEIGHT) {
        LOGE("Video dimensions exceed maximum supported size: %dx%d", width, height);
        return false;
    }
    
    // 创建Y通道Mat
    Mat yMat(height, yStride, CV_8UC1, yData);
    
    // 如果是第一帧，保存并返回
    if (prevFrame.empty()) {
        yMat(Rect(0, 0, width, height)).copyTo(prevFrame);
        frameIndex++;
        return false;
    }
    
    // 保存当前帧
    yMat(Rect(0, 0, width, height)).copyTo(currFrame);
    
    // 根据当前模式处理
    if (currentMode == BASIC || currentMode == ADVANCED) {
        // 使用OpenCV实现的防抖
        // 这里可以调用video_stabilizer.cpp中的实现
        // 暂时返回false，表示未处理
        LOGI("Using OpenCV stabilization (mode: %d)", currentMode);
        return false;
    } 
    else if (currentMode == SDK_STAB) {
        // 使用SDK的VideoAS实现
        // 这里需要JNI调用SDK的方法
        LOGI("Using SDK VideoAS stabilization");
        
        // 模拟处理
        if (isTracking) {
            // 跟踪阶段
            LOGI("Tracking frame %d", frameIndex);
        } else {
            // 变形阶段
            LOGI("Deforming frame %d", frameIndex);
            
            // 这里应该调用SDK的frameDeforming方法
            // 暂时使用简单的OpenCV实现
            try {
                // 简单的平移变换示例
                Mat transform = Mat::eye(3, 3, CV_64F);
                transform.at<double>(0, 2) = sin(frameIndex * 0.05) * 10.0 * stabilizationStrength;
                transform.at<double>(1, 2) = cos(frameIndex * 0.05) * 10.0 * stabilizationStrength;
                
                // 平滑变换
                Mat smoothTransform = smoothTransformation(transform);
                
                // 应用变换到Y通道
                Mat stabilizedY;
                warpAffine(yMat, stabilizedY, smoothTransform.rowRange(0, 2), Size(yStride, height));
                
                // 复制回原始数据
                stabilizedY.copyTo(yMat);
                
                // 处理UV通道
                if (!processLumaOnly) {
                    Mat uMat(height/2, uStride, CV_8UC1, uData);
                    Mat vMat(height/2, vStride, CV_8UC1, vData);
                    
                    // 缩小变换矩阵以适应UV通道
                    Mat scaledTransform = smoothTransform.clone();
                    scaledTransform.at<double>(0, 2) /= 2.0;
                    scaledTransform.at<double>(1, 2) /= 2.0;
                    
                    // 应用变换
                    Mat stabilizedU, stabilizedV;
                    warpAffine(uMat, stabilizedU, scaledTransform.rowRange(0, 2), Size(uStride, height/2));
                    warpAffine(vMat, stabilizedV, scaledTransform.rowRange(0, 2), Size(vStride, height/2));
                    
                    // 复制回原始数据
                    stabilizedU.copyTo(uMat);
                    stabilizedV.copyTo(vMat);
                }
            } catch (cv::Exception& e) {
                LOGE("Error in stabilization: %s", e.what());
                return false;
            }
        }
    }
    
    // 更新前一帧
    currFrame.copyTo(prevFrame);
    frameIndex++;
    
    return true;
}

extern "C" {

/**
 * 初始化视频防抖
 */
JNIEXPORT jboolean JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_initVideoASStabilizer(
        JNIEnv *env, jobject thiz) {
    return initVideoStabilizer();
}

/**
 * 释放视频防抖资源
 */
JNIEXPORT void JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_releaseVideoASStabilizer(
        JNIEnv *env, jobject thiz) {
    releaseVideoStabilizer();
}

/**
 * 设置防抖模式
 */
JNIEXPORT void JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_setVideoASStabilizationMode(
        JNIEnv *env, jobject thiz, jint mode) {
    StabilizationMode newMode = static_cast<StabilizationMode>(mode);
    if (currentMode != newMode) {
        LOGI("Changing VideoAS stabilization mode from %d to %d", currentMode, newMode);
        currentMode = newMode;
        
        // 重置状态
        releaseVideoStabilizer();
        initVideoStabilizer();
    }
}

/**
 * 设置是否处于跟踪阶段
 */
JNIEXPORT void JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_setVideoASTrackingState(
        JNIEnv *env, jobject thiz, jboolean tracking) {
    isTracking = tracking;
    LOGI("VideoAS tracking state set to: %d", isTracking);
}

/**
 * 设置防抖强度
 */
JNIEXPORT void JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_setVideoASStabilizationStrength(
        JNIEnv *env, jobject thiz, jfloat strength) {
    stabilizationStrength = std::max(0.0f, std::min(strength, 1.0f));
    LOGI("VideoAS stabilization strength set to: %.2f", stabilizationStrength);
}

/**
 * 处理I420格式的视频帧
 */
JNIEXPORT jboolean JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_stabilizeI420FrameWithVideoAS(
        JNIEnv *env, jobject thiz,
        jbyteArray y_data, jbyteArray u_data, jbyteArray v_data,
        jint width, jint height, jint y_stride, jint u_stride, jint v_stride,
        jboolean process_luma_only) {
    
    // 获取数据指针
    jbyte *yBytes = env->GetByteArrayElements(y_data, NULL);
    jbyte *uBytes = env->GetByteArrayElements(u_data, NULL);
    jbyte *vBytes = env->GetByteArrayElements(v_data, NULL);
    
    // 调用处理函数
    bool result = stabilizeI420Frame(
        reinterpret_cast<uint8_t*>(yBytes),
        reinterpret_cast<uint8_t*>(uBytes),
        reinterpret_cast<uint8_t*>(vBytes),
        width, height, y_stride, u_stride, v_stride,
        process_luma_only
    );
    
    // 释放数据指针
    env->ReleaseByteArrayElements(y_data, yBytes, 0);
    env->ReleaseByteArrayElements(u_data, uBytes, 0);
    env->ReleaseByteArrayElements(v_data, vBytes, 0);
    
    return result;
}

} // extern "C"
