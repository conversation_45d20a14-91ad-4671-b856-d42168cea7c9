<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Board (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: Board">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class Board" class="title">Class Board</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.Board</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></code>, <code><a href="GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Board</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Board of ArUco markers

 A board is a set of markers in the 3D space with a common coordinate system.
 The common form of a board of marker is a planar (2D) board, however any 3D layout can be used.
 A Board object is composed by:
 - The object points of the marker corners, i.e. their coordinates respect to the board system.
 - The dictionary which indicates the type of markers of the board
 - The identifier of all the markers in the board.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.util.List,org.opencv.objdetect.Dictionary,org.opencv.core.Mat)" class="member-name-link">Board</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objPoints,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code></div>
<div class="col-last even-row-color">
<div class="block">Common Board constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Board.html" title="class in org.opencv.objdetect">Board</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateImage(org.opencv.core.Size,org.opencv.core.Mat)" class="member-name-link">generateImage</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draw a planar board</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateImage(org.opencv.core.Size,org.opencv.core.Mat,int)" class="member-name-link">generateImage</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;marginSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draw a planar board</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateImage(org.opencv.core.Size,org.opencv.core.Mat,int,int)" class="member-name-link">generateImage</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;marginSize,
 int&nbsp;borderBits)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Draw a planar board</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDictionary()" class="member-name-link">getDictionary</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">return the Dictionary of markers employed for this board</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIds()" class="member-name-link">getIds</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">vector of the identifiers of the markers in the board (should be the same size as objPoints)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getObjPoints()" class="member-name-link">getObjPoints</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">return array of object points of all the marker corners in the board.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Point3.html" title="class in org.opencv.core">Point3</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRightBottomCorner()" class="member-name-link">getRightBottomCorner</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get coordinate of the bottom right corner of the board, is set when calling the function create()</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#matchImagePoints(java.util.List,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">matchImagePoints</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;objPoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imgPoints)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a board configuration and a set of detected markers, returns the corresponding
 image points and object points, can be used in solvePnP()</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.util.List,org.opencv.objdetect.Dictionary,org.opencv.core.Mat)">
<h3>Board</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Board</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objPoints,
 <a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</span></div>
<div class="block">Common Board constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>objPoints</code> - array of object points of all the marker corners in the board</dd>
<dd><code>dictionary</code> - the dictionary of markers employed for this board</dd>
<dd><code>ids</code> - vector of the identifiers of the markers in the board</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Board.html" title="class in org.opencv.objdetect">Board</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDictionary()">
<h3>getDictionary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></span>&nbsp;<span class="element-name">getDictionary</span>()</div>
<div class="block">return the Dictionary of markers employed for this board</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getObjPoints()">
<h3>getObjPoints</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;</span>&nbsp;<span class="element-name">getObjPoints</span>()</div>
<div class="block">return array of object points of all the marker corners in the board.

 Each marker include its 4 corners in this order:
 -   objPoints[i][0] - left-top point of i-th marker
 -   objPoints[i][1] - right-top point of i-th marker
 -   objPoints[i][2] - right-bottom point of i-th marker
 -   objPoints[i][3] - left-bottom point of i-th marker

 Markers are placed in a certain order - row by row, left to right in every row. For M markers, the size is Mx4.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIds()">
<h3>getIds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></span>&nbsp;<span class="element-name">getIds</span>()</div>
<div class="block">vector of the identifiers of the markers in the board (should be the same size as objPoints)</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>vector of the identifiers of the markers</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRightBottomCorner()">
<h3>getRightBottomCorner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Point3.html" title="class in org.opencv.core">Point3</a></span>&nbsp;<span class="element-name">getRightBottomCorner</span>()</div>
<div class="block">get coordinate of the bottom right corner of the board, is set when calling the function create()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matchImagePoints(java.util.List,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>matchImagePoints</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">matchImagePoints</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;objPoints,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imgPoints)</span></div>
<div class="block">Given a board configuration and a set of detected markers, returns the corresponding
 image points and object points, can be used in solvePnP()</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>detectedCorners</code> - List of detected marker corners of the board.
 For cv::Board and cv::GridBoard the method expects std::vector&lt;std::vector&lt;Point2f&gt;&gt; or std::vector&lt;Mat&gt; with Aruco marker corners.
 For cv::CharucoBoard the method expects std::vector&lt;Point2f&gt; or Mat with ChAruco corners (chess board corners matched with Aruco markers).</dd>
<dd><code>detectedIds</code> - List of identifiers for each marker or charuco corner.
 For any Board class the method expects std::vector&lt;int&gt; or Mat.</dd>
<dd><code>objPoints</code> - Vector of marker points in the board coordinate space.
 For any Board class the method expects std::vector&lt;cv::Point3f&gt; objectPoints or cv::Mat</dd>
<dd><code>imgPoints</code> - Vector of marker points in the image coordinate space.
 For any Board class the method expects std::vector&lt;cv::Point2f&gt; objectPoints or cv::Mat

 SEE: solvePnP</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImage(org.opencv.core.Size,org.opencv.core.Mat,int,int)">
<h3>generateImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImage</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;marginSize,
 int&nbsp;borderBits)</span></div>
<div class="block">Draw a planar board</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.</dd>
<dd><code>marginSize</code> - minimum margins (in pixels) of the board in the output image</dd>
<dd><code>borderBits</code> - width of the marker borders.

 This function return the image of the board, ready to be printed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImage(org.opencv.core.Size,org.opencv.core.Mat,int)">
<h3>generateImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImage</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 int&nbsp;marginSize)</span></div>
<div class="block">Draw a planar board</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.</dd>
<dd><code>marginSize</code> - minimum margins (in pixels) of the board in the output image

 This function return the image of the board, ready to be printed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateImage(org.opencv.core.Size,org.opencv.core.Mat)">
<h3>generateImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateImage</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</span></div>
<div class="block">Draw a planar board</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.

 This function return the image of the board, ready to be printed.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
