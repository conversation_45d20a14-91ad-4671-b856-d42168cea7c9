// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMediaBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CheckBox checkbox;

  @NonNull
  public final ImageView imgThumbnail;

  @NonNull
  public final ImageView imgVideoIndicator;

  @NonNull
  public final TextView tvDuration;

  private ItemMediaBinding(@NonNull CardView rootView, @NonNull CheckBox checkbox,
      @NonNull ImageView imgThumbnail, @NonNull ImageView imgVideoIndicator,
      @NonNull TextView tvDuration) {
    this.rootView = rootView;
    this.checkbox = checkbox;
    this.imgThumbnail = imgThumbnail;
    this.imgVideoIndicator = imgVideoIndicator;
    this.tvDuration = tvDuration;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMediaBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMediaBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_media, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMediaBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkbox;
      CheckBox checkbox = ViewBindings.findChildViewById(rootView, id);
      if (checkbox == null) {
        break missingId;
      }

      id = R.id.img_thumbnail;
      ImageView imgThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (imgThumbnail == null) {
        break missingId;
      }

      id = R.id.img_video_indicator;
      ImageView imgVideoIndicator = ViewBindings.findChildViewById(rootView, id);
      if (imgVideoIndicator == null) {
        break missingId;
      }

      id = R.id.tv_duration;
      TextView tvDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvDuration == null) {
        break missingId;
      }

      return new ItemMediaBinding((CardView) rootView, checkbox, imgThumbnail, imgVideoIndicator,
          tvDuration);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
