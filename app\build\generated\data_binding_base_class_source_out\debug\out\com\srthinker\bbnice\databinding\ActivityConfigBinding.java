// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityConfigBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnLanguageSettings;

  @NonNull
  public final EditText roomIdInput;

  @NonNull
  public final Button saveConfigButton;

  @NonNull
  public final EditText serverHostInput;

  @NonNull
  public final EditText userIdInput;

  private ActivityConfigBinding(@NonNull LinearLayout rootView, @NonNull Button btnLanguageSettings,
      @NonNull EditText roomIdInput, @NonNull Button saveConfigButton,
      @NonNull EditText serverHostInput, @NonNull EditText userIdInput) {
    this.rootView = rootView;
    this.btnLanguageSettings = btnLanguageSettings;
    this.roomIdInput = roomIdInput;
    this.saveConfigButton = saveConfigButton;
    this.serverHostInput = serverHostInput;
    this.userIdInput = userIdInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_language_settings;
      Button btnLanguageSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnLanguageSettings == null) {
        break missingId;
      }

      id = R.id.room_id_input;
      EditText roomIdInput = ViewBindings.findChildViewById(rootView, id);
      if (roomIdInput == null) {
        break missingId;
      }

      id = R.id.save_config_button;
      Button saveConfigButton = ViewBindings.findChildViewById(rootView, id);
      if (saveConfigButton == null) {
        break missingId;
      }

      id = R.id.server_host_input;
      EditText serverHostInput = ViewBindings.findChildViewById(rootView, id);
      if (serverHostInput == null) {
        break missingId;
      }

      id = R.id.user_id_input;
      EditText userIdInput = ViewBindings.findChildViewById(rootView, id);
      if (userIdInput == null) {
        break missingId;
      }

      return new ActivityConfigBinding((LinearLayout) rootView, btnLanguageSettings, roomIdInput,
          saveConfigButton, serverHostInput, userIdInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
