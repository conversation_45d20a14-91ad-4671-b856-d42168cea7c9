package com.srthinker.bbnice.work;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.telephony.CellInfo;
import android.telephony.CellInfoGsm;
import android.telephony.CellInfoLte;
import android.telephony.CellInfoWcdma;
import android.telephony.CellSignalStrengthGsm;
import android.telephony.CellSignalStrengthLte;
import android.telephony.CellSignalStrengthWcdma;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresPermission;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.location.LocationManager;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备状态上报工作器，负责定期上报设备状态信息
 */
public class DeviceStatusWorker extends Worker {
    private static final String TAG = "DeviceStatusWorker";
    private BBNiceApi api;
    private int signalStrength = -1;

    public DeviceStatusWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        api = BBNiceApi.getInstance(context);
    }

    @NonNull
    @Override
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    public Result doWork() {
        Log.d(TAG, "Starting device status report work");

        Context context = getApplicationContext();

        try {
            // 获取电池电量
            int batteryLevel = getBatteryLevel(context);

            // 获取网络状态
            String networkStatus = getNetworkStatus(context);

            // 获取信号强度
            int signalStrength = getSignalStrength(context);

            // 使用CountDownLatch等待上报完成
            final CountDownLatch latch = new CountDownLatch(1);
            final AtomicBoolean reportSuccess = new AtomicBoolean(false);

            // 尝试获取当前位置
            LocationManager locationManager = LocationManager.getInstance(context);
            locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
                @Override
                public void onSuccess(LocationData locationData) {
                    double latitude = locationData.getLatitude();
                    double longitude = locationData.getLongitude();
                    // 获取固件版本
                    String firmwareVersion = Build.VERSION.RELEASE;

                    Log.d(TAG, "Device status: Battery=" + batteryLevel + "%, Network=" + networkStatus +
                            ", Signal=" + signalStrength + ", Location=(" + latitude + "," + longitude +
                            "), Firmware=" + firmwareVersion);

                    // 上报设备状态
                    api.reportDeviceStatus(batteryLevel,
                            networkStatus,
                            signalStrength,
                            latitude,
                            longitude,
                            firmwareVersion, new ApiCallback<BaseResponse>() {
                                @Override
                                public void onSuccess(BaseResponse response) {
                                    Log.d(TAG, "Device status reported successfully");
                                    reportSuccess.set(true);
                                    latch.countDown();
                                }

                                @Override
                                public void onError(ApiError error) {
                                    Log.e(TAG, "Failed to report device status: " + (error != null ? error.getMessage() : "Unknown error"));
                                    latch.countDown();
                                }

                            }
                    );
                }

                @Override
                public void onError(ApiError error) {
                    Log.e(TAG, "onError: 获取位置失败");
                    latch.countDown();
                }
            });

            try {
                // 等待上报完成，最多等待30秒
                boolean completed = latch.await(30, TimeUnit.SECONDS);

                if (!completed) {
                    Log.e(TAG, "Device status report timed out");
                    return Result.retry();
                }

                if (reportSuccess.get()) {
                    return Result.success();
                } else {
                    return Result.retry();
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Device status report interrupted: " + e.getMessage());
                Thread.currentThread().interrupt();
                return Result.retry();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during device status report: " + e.getMessage());
            return Result.retry();
        }
    }

    /**
     * 获取电池电量
     * @param context 上下文
     * @return 电池电量百分比 (0-100)
     */
    private int getBatteryLevel(Context context) {
        IntentFilter iFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = context.registerReceiver(null, iFilter);

        int level = -1;
        int scale = -1;

        if (batteryStatus != null) {
            level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
            scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
        }

        if (level != -1 && scale != -1) {
            return (int) ((level / (float) scale) * 100);
        }

        return 50; // 默认值
    }

    /**
     * 获取网络状态
     * @param context 上下文
     * @return 网络状态 ("wifi", "4G", "5G", "offline" 等)
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    private String getNetworkStatus(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();

        if (activeNetwork == null || !activeNetwork.isConnected()) {
            return "offline";
        }

        if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
            return "wifi";
        }

        if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

            switch (tm.getDataNetworkType()) {
                case TelephonyManager.NETWORK_TYPE_LTE:
                    return "4G";
                case TelephonyManager.NETWORK_TYPE_NR:
                    return "5G";
                default:
                    return "mobile";
            }
        }

        return "unknown";
    }

    /**
     * 获取信号强度
     * @param context 上下文
     * @return 信号强度 (0-5)
     */
    private int getSignalStrength(Context context) {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();

            if (activeNetwork == null || !activeNetwork.isConnected()) {
                return 0; // 无网络连接
            }

            if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                // WiFi信号强度
                return getWifiSignalStrength(context);
            } else if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
                // 移动网络信号强度
                return getMobileSignalStrength(context);
            }

            return 3; // 默认中等信号强度
        } catch (Exception e) {
            Log.e(TAG, "Error getting signal strength: " + e.getMessage());
            return 3; // 默认值
        }
    }

    /**
     * 获取WiFi信号强度
     * @param context 上下文
     * @return 信号强度 (0-5)
     */
    private int getWifiSignalStrength(Context context) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null && wifiManager.isWifiEnabled()) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    int rssi = wifiInfo.getRssi();
                    // 将RSSI值转换为0-5的信号强度
                    // WiFi RSSI范围通常是 -100 到 -30 dBm
                    if (rssi >= -50) {
                        return 5; // 优秀
                    } else if (rssi >= -60) {
                        return 4; // 良好
                    } else if (rssi >= -70) {
                        return 3; // 一般
                    } else if (rssi >= -80) {
                        return 2; // 较差
                    } else if (rssi >= -90) {
                        return 1; // 很差
                    } else {
                        return 0; // 极差
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting WiFi signal strength: " + e.getMessage());
        }
        return 3; // 默认值
    }

    /**
     * 获取移动网络信号强度
     * @param context 上下文
     * @return 信号强度 (0-5)
     */
    private int getMobileSignalStrength(Context context) {
        try {
            // 检查权限
            if (context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "No permission to access cell info");
                return 3; // 默认值
            }

            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                try {
                    java.util.List<CellInfo> cellInfos = telephonyManager.getAllCellInfo();
                    if (cellInfos != null && !cellInfos.isEmpty()) {
                        for (CellInfo cellInfo : cellInfos) {
                            if (cellInfo.isRegistered()) {
                                return getCellSignalStrength(cellInfo);
                            }
                        }
                    }
                } catch (SecurityException e) {
                    Log.w(TAG, "SecurityException when getting cell info: " + e.getMessage());
                }
            }

            // 如果无法获取详细信息，尝试使用传统方法
            return getSignalStrengthFromTelephonyManager(telephonyManager);

        } catch (Exception e) {
            Log.e(TAG, "Error getting mobile signal strength: " + e.getMessage());
        }
        return 3; // 默认值
    }

    /**
     * 从CellInfo获取信号强度
     * @param cellInfo 蜂窝信息
     * @return 信号强度 (0-5)
     */
    private int getCellSignalStrength(CellInfo cellInfo) {
        try {
            if (cellInfo instanceof CellInfoLte) {
                CellSignalStrengthLte signalStrength = ((CellInfoLte) cellInfo).getCellSignalStrength();
                int dbm = signalStrength.getDbm();
                return convertDbmToSignalLevel(dbm, "LTE");
            } else if (cellInfo instanceof CellInfoGsm) {
                CellSignalStrengthGsm signalStrength = ((CellInfoGsm) cellInfo).getCellSignalStrength();
                int dbm = signalStrength.getDbm();
                return convertDbmToSignalLevel(dbm, "GSM");
            } else if (cellInfo instanceof CellInfoWcdma) {
                CellSignalStrengthWcdma signalStrength = ((CellInfoWcdma) cellInfo).getCellSignalStrength();
                int dbm = signalStrength.getDbm();
                return convertDbmToSignalLevel(dbm, "WCDMA");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting cell signal strength: " + e.getMessage());
        }
        return 3; // 默认值
    }

    /**
     * 将dBm值转换为信号强度等级
     * @param dbm dBm值
     * @param networkType 网络类型
     * @return 信号强度 (0-5)
     */
    private int convertDbmToSignalLevel(int dbm, String networkType) {
        // 根据不同网络类型设置不同的阈值
        switch (networkType) {
            case "LTE":
                // LTE信号强度范围通常是 -140 到 -44 dBm
                if (dbm >= -70) {
                    return 5; // 优秀
                } else if (dbm >= -85) {
                    return 4; // 良好
                } else if (dbm >= -100) {
                    return 3; // 一般
                } else if (dbm >= -115) {
                    return 2; // 较差
                } else if (dbm >= -130) {
                    return 1; // 很差
                } else {
                    return 0; // 极差
                }
            case "GSM":
                // GSM信号强度范围通常是 -113 到 -51 dBm
                if (dbm >= -70) {
                    return 5; // 优秀
                } else if (dbm >= -80) {
                    return 4; // 良好
                } else if (dbm >= -90) {
                    return 3; // 一般
                } else if (dbm >= -100) {
                    return 2; // 较差
                } else if (dbm >= -110) {
                    return 1; // 很差
                } else {
                    return 0; // 极差
                }
            case "WCDMA":
                // WCDMA信号强度范围通常是 -120 到 -25 dBm
                if (dbm >= -70) {
                    return 5; // 优秀
                } else if (dbm >= -85) {
                    return 4; // 良好
                } else if (dbm >= -100) {
                    return 3; // 一般
                } else if (dbm >= -110) {
                    return 2; // 较差
                } else if (dbm >= -120) {
                    return 1; // 很差
                } else {
                    return 0; // 极差
                }
            default:
                // 通用转换
                if (dbm >= -70) {
                    return 5;
                } else if (dbm >= -85) {
                    return 4;
                } else if (dbm >= -100) {
                    return 3;
                } else if (dbm >= -115) {
                    return 2;
                } else if (dbm >= -130) {
                    return 1;
                } else {
                    return 0;
                }
        }
    }

    /**
     * 从TelephonyManager获取信号强度（兼容旧版本）
     * @param telephonyManager 电话管理器
     * @return 信号强度 (0-5)
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    private int getSignalStrengthFromTelephonyManager(TelephonyManager telephonyManager) {
        try {
            // 这个方法在新版本Android中可能不太准确，但可以作为备用方案
            // 由于无法直接获取信号强度，我们使用网络类型来估算
            int networkType = telephonyManager.getNetworkType();

            switch (networkType) {
                case TelephonyManager.NETWORK_TYPE_LTE:
                case TelephonyManager.NETWORK_TYPE_NR: // 5G
                    return 4; // LTE/5G通常信号较好
                case TelephonyManager.NETWORK_TYPE_HSPAP:
                case TelephonyManager.NETWORK_TYPE_HSPA:
                case TelephonyManager.NETWORK_TYPE_HSUPA:
                case TelephonyManager.NETWORK_TYPE_HSDPA:
                case TelephonyManager.NETWORK_TYPE_UMTS:
                    return 3; // 3G网络
                case TelephonyManager.NETWORK_TYPE_EDGE:
                case TelephonyManager.NETWORK_TYPE_GPRS:
                    return 2; // 2G网络
                case TelephonyManager.NETWORK_TYPE_UNKNOWN:
                default:
                    return 1; // 未知或无信号
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting signal strength from TelephonyManager: " + e.getMessage());
        }
        return 3; // 默认值
    }
}
