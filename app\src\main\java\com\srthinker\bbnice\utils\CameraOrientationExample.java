package com.srthinker.bbnice.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.view.TextureView;

/**
 * Camera2Helper 图像方向修正使用示例
 */
public class CameraOrientationExample {
    private static final String TAG = "CameraOrientationExample";

    /**
     * 演示图像方向修正功能
     */
    public static void demonstrateOrientationCorrection(Context context, TextureView textureView) {
        Log.d(TAG, "=== Camera2Helper 图像方向修正示例 ===");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);

        // 设置为1K清晰度
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.MEDIUM);

        // 拍照并验证方向修正
        cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
            @Override
            public void onBitmapCaptured(Bitmap bitmap) {
                if (bitmap != null) {
                    int width = bitmap.getWidth();
                    int height = bitmap.getHeight();
                    
                    Log.d(TAG, "=== 拍照结果 ===");
                    Log.d(TAG, "图像尺寸: " + width + "x" + height);
                    Log.d(TAG, "宽高比: " + String.format("%.2f", (float) width / height));
                    
                    // 分析图像方向
                    analyzeImageOrientation(width, height);
                    
                    // 验证是否符合预期
                    boolean isCorrectOrientation = verifyOrientation(width, height);
                    Log.d(TAG, "方向修正结果: " + (isCorrectOrientation ? "✅ 正确" : "❌ 需要检查"));
                    
                } else {
                    Log.e(TAG, "拍照失败，无法验证方向修正");
                }
            }
        });
    }

    /**
     * 分析图像方向
     */
    private static void analyzeImageOrientation(int width, int height) {
        if (width > height) {
            Log.d(TAG, "图像方向: 横向 (Landscape)");
            Log.d(TAG, "说明: 图像宽度大于高度，适合横屏显示");
        } else if (height > width) {
            Log.d(TAG, "图像方向: 纵向 (Portrait)");
            Log.d(TAG, "说明: 图像高度大于宽度，适合竖屏显示");
        } else {
            Log.d(TAG, "图像方向: 正方形 (Square)");
            Log.d(TAG, "说明: 图像宽高相等");
        }
    }

    /**
     * 验证图像方向是否正确
     */
    private static boolean verifyOrientation(int width, int height) {
        // 对于竖屏拍照，期望得到竖向图像（高度 > 宽度）
        // 如果没有方向修正，通常会得到横向图像
        
        if (height > width) {
            Log.d(TAG, "✅ 方向修正成功：得到竖向图像");
            return true;
        } else {
            Log.w(TAG, "⚠️ 可能需要检查方向修正逻辑");
            return false;
        }
    }

    /**
     * 测试不同设备方向下的拍照效果
     */
    public static void testDifferentOrientations(Context context, TextureView textureView) {
        Log.d(TAG, "=== 不同设备方向测试 ===");
        Log.d(TAG, "请在不同设备方向下进行拍照测试：");
        Log.d(TAG, "1. 竖屏拍照");
        Log.d(TAG, "2. 横屏拍照");
        Log.d(TAG, "3. 倒立拍照");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.MEDIUM);

        cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
            @Override
            public void onBitmapCaptured(Bitmap bitmap) {
                if (bitmap != null) {
                    Log.d(TAG, "当前拍照结果: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    
                    // 检查是否与预览方向一致
                    checkConsistencyWithPreview(bitmap);
                } else {
                    Log.e(TAG, "拍照失败");
                }
            }
        });
    }

    /**
     * 检查拍照结果与预览的一致性
     */
    private static void checkConsistencyWithPreview(Bitmap bitmap) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        
        Log.d(TAG, "=== 预览一致性检查 ===");
        Log.d(TAG, "拍照图像: " + width + "x" + height);
        
        // 这里可以添加更多的一致性检查逻辑
        // 比如比较拍照图像的方向与TextureView的方向
        
        if (height > width) {
            Log.d(TAG, "✅ 竖向图像，符合竖屏预览预期");
        } else {
            Log.d(TAG, "📱 横向图像，符合横屏预览预期");
        }
    }

    /**
     * 性能测试：方向修正的耗时
     */
    public static void testOrientationCorrectionPerformance(Context context, TextureView textureView) {
        Log.d(TAG, "=== 方向修正性能测试 ===");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);
        
        // 测试不同质量下的性能
        Camera2Helper.CaptureQuality[] qualities = {
            Camera2Helper.CaptureQuality.LOW,
            Camera2Helper.CaptureQuality.MEDIUM,
            Camera2Helper.CaptureQuality.HIGH
        };

        for (Camera2Helper.CaptureQuality quality : qualities) {
            cameraHelper.setCaptureQuality(quality);
            
            Log.d(TAG, "\n--- 测试 " + quality.name() + " 质量 ---");
            
            long startTime = System.currentTimeMillis();
            
            cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
                @Override
                public void onBitmapCaptured(Bitmap bitmap) {
                    long endTime = System.currentTimeMillis();
                    long totalTime = endTime - startTime;
                    
                    if (bitmap != null) {
                        int pixels = bitmap.getWidth() * bitmap.getHeight();
                        
                        Log.d(TAG, quality.name() + " 质量性能数据:");
                        Log.d(TAG, "  总耗时: " + totalTime + "ms");
                        Log.d(TAG, "  图像尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                        Log.d(TAG, "  像素总数: " + pixels);
                        Log.d(TAG, "  处理效率: " + (pixels / Math.max(totalTime, 1)) + " 像素/ms");
                        
                        // 估算方向修正的额外耗时（通常很小）
                        long estimatedRotationTime = Math.max(1, totalTime / 10);
                        Log.d(TAG, "  估算旋转耗时: ~" + estimatedRotationTime + "ms");
                    } else {
                        Log.e(TAG, quality.name() + " 质量拍照失败");
                    }
                }
            });
            
            // 等待处理完成
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 内存使用测试
     */
    public static void testMemoryUsage(Context context, TextureView textureView) {
        Log.d(TAG, "=== 内存使用测试 ===");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.MEDIUM);

        // 获取拍照前的内存使用
        Runtime runtime = Runtime.getRuntime();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        Log.d(TAG, "拍照前内存使用: " + (memoryBefore / 1024 / 1024) + "MB");

        cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
            @Override
            public void onBitmapCaptured(Bitmap bitmap) {
                if (bitmap != null) {
                    // 获取拍照后的内存使用
                    long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
                    long memoryIncrease = memoryAfter - memoryBefore;
                    
                    Log.d(TAG, "拍照后内存使用: " + (memoryAfter / 1024 / 1024) + "MB");
                    Log.d(TAG, "内存增加: " + (memoryIncrease / 1024 / 1024) + "MB");
                    
                    // 计算bitmap理论内存占用
                    int bitmapMemory = bitmap.getWidth() * bitmap.getHeight() * 4; // ARGB_8888
                    Log.d(TAG, "Bitmap理论占用: " + (bitmapMemory / 1024 / 1024) + "MB");
                    
                    // 检查是否有内存泄漏
                    if (memoryIncrease > bitmapMemory * 2) {
                        Log.w(TAG, "⚠️ 可能存在内存泄漏，增加的内存超过预期");
                    } else {
                        Log.d(TAG, "✅ 内存使用正常");
                    }
                } else {
                    Log.e(TAG, "拍照失败，无法测试内存使用");
                }
            }
        });
    }
}
