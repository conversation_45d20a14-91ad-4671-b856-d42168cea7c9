package com.srthinker.bbnice.mqtt;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.repository.MqttRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.mqtt.handler.AICommandHandler;
import com.srthinker.bbnice.mqtt.handler.ActionCommandHandler;
import com.srthinker.bbnice.mqtt.handler.CommandHandler;
import com.srthinker.bbnice.mqtt.model.MqttCommand;
import com.srthinker.bbnice.mqtt.model.MqttCommandStatus;
import com.srthinker.bbnice.utils.JsonUtils;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.DisconnectedBufferOptions;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MQTT服务
 * 用于连接MQTT服务器、订阅主题、处理消息
 */
public class MqttService extends Service {
    private static final String TAG = "MqttService";

    // 通知相关常量
    private static final String NOTIFICATION_CHANNEL_ID = "mqtt_service_channel";
    private static final int NOTIFICATION_ID = 1001;

    // MQTT客户端
    private MqttAndroidClient mqttClient;

    // MQTT配置
    private MqttConfig mqttConfig;

    // MQTT Repository
    private BBNiceApi api;

    // 指令处理器列表
    private final List<CommandHandler> commandHandlers = new ArrayList<>();

    // 已处理的指令ID集合，用于去重
    private final Set<String> processedCommandIds = new HashSet<>();

    // 线程池，用于处理指令
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    // 重连处理器
    private final Handler reconnectHandler = new Handler(Looper.getMainLooper());

    // 重连任务
    private final Runnable reconnectRunnable = new Runnable() {
        @Override
        public void run() {
            if (mqttClient != null && !mqttClient.isConnected()) {
                Log.d(TAG, "Attempting to reconnect to MQTT broker");
                connectToMqttBroker();
            }
        }
    };

    // 重连间隔（毫秒）
    private static final long RECONNECT_INTERVAL = 30000; // 30秒

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "MQTT service created");

        // 初始化MQTT配置
        mqttConfig = MqttConfig.getInstance(this);

        // 初始化MQTT Repository
        api = BBNiceApi.getInstance(this);

        // 初始化指令处理器
        initCommandHandlers();

        // 创建通知渠道
        createNotificationChannel();

        // 启动前台服务
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // Android 14及以上需要指定服务类型
                startForeground(NOTIFICATION_ID, createNotification(), ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
            } else {
                startForeground(NOTIFICATION_ID, createNotification());
            }
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to start foreground service: " + e.getMessage());
            // 如果权限不足，停止服务
            stopSelf();
            return;
        }

        // 连接到MQTT服务器
        connectToMqttBroker();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "MQTT service started");
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "MQTT service destroyed");

        // 断开MQTT连接
        disconnectFromMqttBroker();

        // 关闭线程池
        executorService.shutdown();

        // 移除重连任务
        reconnectHandler.removeCallbacks(reconnectRunnable);

        super.onDestroy();
    }

    /**
     * 初始化指令处理器
     */
    private void initCommandHandlers() {
        commandHandlers.add(new ActionCommandHandler(this));
        commandHandlers.add(new AICommandHandler(this));
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    NOTIFICATION_CHANNEL_ID,
                    "MQTT Service Channel",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("Channel for MQTT service");
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 创建通知
     * @return 通知
     */
    private Notification createNotification() {
        return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("BB Nice MQTT Service")
                .setContentText("Listening for commands")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
    }

    /**
     * 连接到MQTT服务器
     */
    private void connectToMqttBroker() {
        // 移除重连任务
        reconnectHandler.removeCallbacks(reconnectRunnable);

        // 如果已经有客户端且已连接，则不需要重新连接
        if (mqttClient != null && mqttClient.isConnected()) {
            Log.d(TAG, "Already connected to MQTT broker");
            return;
        }

        // 如果已经有客户端但未连接，则关闭它
        if (mqttClient != null) {
            try {
                mqttClient.close();
            } catch (Exception e) {
                Log.e(TAG, "Error closing MQTT client", e);
            }
        }

        // 创建新的MQTT客户端 - 使用安全包装类
        mqttClient = new SafeMqttAndroidClient(
                getApplicationContext(),
                mqttConfig.getBrokerUrl(),
                mqttConfig.getClientId());

        // 设置回调
        mqttClient.setCallback(new MqttCallbackExtended() {
            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                Log.d(TAG, "Connected to MQTT broker: " + serverURI + ", reconnect: " + reconnect);

                // 订阅主题
                subscribeToTopic();
            }

            @Override
            public void connectionLost(Throwable cause) {
                Log.e(TAG, "Connection to MQTT broker lost", cause);

                // 安排重连任务
                reconnectHandler.postDelayed(reconnectRunnable, RECONNECT_INTERVAL);
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) {
                String payload = new String(message.getPayload());
                Log.d(TAG, "Message arrived on topic: " + topic + ", payload: " + payload);

                // 处理消息
                processMessage(topic, payload);
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                Log.d(TAG, "Message delivery complete");
            }
        });

        // 设置连接选项
        MqttConnectOptions connectOptions = new MqttConnectOptions();
        connectOptions.setAutomaticReconnect(true);
        connectOptions.setCleanSession(false);
        connectOptions.setUserName(mqttConfig.getUsername());
        connectOptions.setPassword(mqttConfig.getPassword().toCharArray());

        // 设置Keep Alive间隔，避免使用AlarmPingSender
        connectOptions.setKeepAliveInterval(60); // 60秒
        connectOptions.setConnectionTimeout(30); // 30秒连接超时
        connectOptions.setMaxInflight(10); // 最大未确认消息数

        try {
            // 使用安全连接方法
            IMqttActionListener actionListener = new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    Log.d(TAG, "Connected to MQTT broker");

                    // 设置断开连接缓冲选项
                    DisconnectedBufferOptions disconnectedBufferOptions = new DisconnectedBufferOptions();
                    disconnectedBufferOptions.setBufferEnabled(true);
                    disconnectedBufferOptions.setBufferSize(100);
                    disconnectedBufferOptions.setPersistBuffer(false);
                    disconnectedBufferOptions.setDeleteOldestMessages(false);
                    mqttClient.setBufferOpts(disconnectedBufferOptions);
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    Log.e(TAG, "Failed to connect to MQTT broker", exception);

                    // 安排重连任务
                    reconnectHandler.postDelayed(reconnectRunnable, RECONNECT_INTERVAL);
                }
            };

            if (mqttClient instanceof SafeMqttAndroidClient) {
                ((SafeMqttAndroidClient) mqttClient).connectSafely(connectOptions, null, actionListener);
            } else {
                mqttClient.connect(connectOptions, null, actionListener);
            }
        } catch (MqttException e) {
            Log.e(TAG, "Error connecting to MQTT broker", e);

            // 安排重连任务
            reconnectHandler.postDelayed(reconnectRunnable, RECONNECT_INTERVAL);
        }
    }

    /**
     * 断开MQTT连接
     */
    private void disconnectFromMqttBroker() {
        if (mqttClient != null && mqttClient.isConnected()) {
            try {
                mqttClient.disconnect();
                mqttClient.close();
                Log.d(TAG, "Disconnected from MQTT broker");
            } catch (MqttException e) {
                Log.e(TAG, "Error disconnecting from MQTT broker", e);
            }
        }
    }

    /**
     * 订阅主题
     */
    private void subscribeToTopic() {
        if (mqttClient == null || !mqttClient.isConnected()) {
            Log.e(TAG, "Cannot subscribe to topic: MQTT client not connected");
            return;
        }

        try {
            mqttClient.subscribe(mqttConfig.getTopic(), mqttConfig.getQos(), null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    Log.d(TAG, "Subscribed to topic: " + mqttConfig.getTopic());
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    Log.e(TAG, "Failed to subscribe to topic: " + mqttConfig.getTopic(), exception);
                }
            });
        } catch (MqttException e) {
            Log.e(TAG, "Error subscribing to topic: " + mqttConfig.getTopic(), e);
        }
    }

    /**
     * 处理消息
     * @param topic 主题
     * @param payload 消息内容
     */
    private void processMessage(String topic, String payload) {
        // 在线程池中处理消息
        executorService.execute(() -> {
            try {
                // 解析消息
                MqttCommand command = JsonUtils.fromJson(payload, MqttCommand.class);
                if (command == null) {
                    Log.e(TAG, "Failed to parse message: " + payload);
                    return;
                }

                // 检查是否已处理过该指令
                if (processedCommandIds.contains(command.getCommandId())) {
                    Log.d(TAG, "Command already processed: " + command.getCommandId());
                    return;
                }

                // 添加到已处理指令集合
                processedCommandIds.add(command.getCommandId());

                // 限制已处理指令集合大小
                if (processedCommandIds.size() > 100) {
                    // 移除最早的50个指令ID
                    List<String> toRemove = new ArrayList<>(processedCommandIds).subList(0, 50);
                    toRemove.forEach(processedCommandIds::remove);
                }

                // 查找合适的处理器
                CommandHandler handler = findHandler(command);
                if (handler == null) {
                    Log.e(TAG, "No handler found for command: " + command);
                    reportCommandStatus(MqttCommandStatus.createFailed(
                            command.getCommandId(),
                            "No handler found for command: " + command.getCommand()));
                    return;
                }

                // 处理指令
                MqttCommandStatus status = handler.handleCommand(command);

                // 上报指令执行状态
                reportCommandStatus(status);
            } catch (Exception e) {
                Log.e(TAG, "Error processing message: " + payload, e);
            }
        });
    }

    /**
     * 查找合适的处理器
     * @param command MQTT指令
     * @return 处理器
     */
    private CommandHandler findHandler(MqttCommand command) {
        for (CommandHandler handler : commandHandlers) {
            if (handler.canHandle(command)) {
                return handler;
            }
        }
        return null;
    }

    /**
     * 上报指令执行状态
     * @param status 指令执行状态
     */
    private void reportCommandStatus(MqttCommandStatus status) {
        Log.d(TAG, "Reporting command status: " + status);
        api.reportCommandStatus(status.getCommandId(),
                status.getStatus(),
                status.getMessage(),
                status.getTimestamp(),
                new ApiCallback<BaseResponse>() {
                    @Override
                    public void onSuccess(BaseResponse response) {
                        Log.d(TAG, "Command status reported successfully: " + status.getCommandId());

                    }

                    @Override
                    public void onError(ApiError error) {
                        Log.e(TAG, "Failed to report command status: " + (error != null ? error.getMessage() : "Unknown error"));

                    }
                }
        );
    }
}
