// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityChatBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnChat;

  @NonNull
  public final RecyclerView recyclerViewChat;

  private ActivityChatBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView btnChat,
      @NonNull RecyclerView recyclerViewChat) {
    this.rootView = rootView;
    this.btnChat = btnChat;
    this.recyclerViewChat = recyclerViewChat;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_chat;
      ImageView btnChat = ViewBindings.findChildViewById(rootView, id);
      if (btnChat == null) {
        break missingId;
      }

      id = R.id.recyclerViewChat;
      RecyclerView recyclerViewChat = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewChat == null) {
        break missingId;
      }

      return new ActivityChatBinding((ConstraintLayout) rootView, btnChat, recyclerViewChat);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
