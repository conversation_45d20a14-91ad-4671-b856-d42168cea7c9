// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogWifiPasswordBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox cbShowPassword;

  @NonNull
  public final TextInputEditText etWifiPassword;

  @NonNull
  public final TextView tvWifiName;

  private DialogWifiPasswordBinding(@NonNull LinearLayout rootView,
      @NonNull CheckBox cbShowPassword, @NonNull TextInputEditText etWifiPassword,
      @NonNull TextView tvWifiName) {
    this.rootView = rootView;
    this.cbShowPassword = cbShowPassword;
    this.etWifiPassword = etWifiPassword;
    this.tvWifiName = tvWifiName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogWifiPasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogWifiPasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_wifi_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogWifiPasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cb_show_password;
      CheckBox cbShowPassword = ViewBindings.findChildViewById(rootView, id);
      if (cbShowPassword == null) {
        break missingId;
      }

      id = R.id.et_wifi_password;
      TextInputEditText etWifiPassword = ViewBindings.findChildViewById(rootView, id);
      if (etWifiPassword == null) {
        break missingId;
      }

      id = R.id.tv_wifi_name;
      TextView tvWifiName = ViewBindings.findChildViewById(rootView, id);
      if (tvWifiName == null) {
        break missingId;
      }

      return new DialogWifiPasswordBinding((LinearLayout) rootView, cbShowPassword, etWifiPassword,
          tvWifiName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
