package com.srthinker.bbnice.setting;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.home.HomeActivity;
import com.srthinker.bbnice.utils.LocaleHelper;

/**
 * 语言设置Activity
 */
public class LanguageSettingsActivity extends AppCompatActivity {

    private RadioGroup languageRadioGroup;
    private RadioButton radioSystem;
    private RadioButton radioEnglish;
    private RadioButton radioChinese;
    private Button btnApply;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_language_settings);

        // 初始化视图
        initViews();

        // 设置当前语言选择
        setCurrentLanguageSelection();

        // 设置按钮点击事件
        btnApply.setOnClickListener(v -> applyLanguageSettings());
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        languageRadioGroup = findViewById(R.id.language_radio_group);
        radioSystem = findViewById(R.id.radio_system);
        radioEnglish = findViewById(R.id.radio_english);
        radioChinese = findViewById(R.id.radio_chinese);
        btnApply = findViewById(R.id.btn_apply);
    }

    /**
     * 设置当前语言选择
     */
    private void setCurrentLanguageSelection() {
        String currentLanguage = LocaleHelper.getLanguage(this);

        if (currentLanguage.equals(LocaleHelper.getSystemLanguage())) {
            radioSystem.setChecked(true);
        } else if (currentLanguage.equals("en")) {
            radioEnglish.setChecked(true);
        } else if (currentLanguage.equals("zh")) {
            radioChinese.setChecked(true);
        } else {
            // 默认选择系统语言
            radioSystem.setChecked(true);
        }
    }

    /**
     * 应用语言设置
     */
    private void applyLanguageSettings() {
        String selectedLanguage;

        int selectedId = languageRadioGroup.getCheckedRadioButtonId();
        if (selectedId == R.id.radio_system) {
            selectedLanguage = LocaleHelper.getSystemLanguage();
        } else if (selectedId == R.id.radio_english) {
            selectedLanguage = "en";
        } else if (selectedId == R.id.radio_chinese) {
            selectedLanguage = "zh";
        } else {
            // 默认使用系统语言
            selectedLanguage = LocaleHelper.getSystemLanguage();
        }

        // 应用语言设置
        LocaleHelper.setLocale(this, selectedLanguage);

        // 显示提示
        Toast.makeText(this, getString(R.string.language_changed), Toast.LENGTH_SHORT).show();
        Toast.makeText(this, getString(R.string.restart_app_message), Toast.LENGTH_LONG).show();

        // 重启应用
        Intent intent = new Intent(this, HomeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
}
