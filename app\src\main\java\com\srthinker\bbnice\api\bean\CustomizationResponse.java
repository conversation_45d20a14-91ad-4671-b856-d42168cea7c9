package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

import java.util.List;

/**
 * 装扮响应类
 * 用于表示获取装扮API的响应
 */
public class CustomizationResponse extends BaseResponse {
    // 响应数据
    private final CustomizationResponseData data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public CustomizationResponse(ApiResponse<CustomizationResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public CustomizationResponseData getData() {
        return data;
    }
    
    /**
     * 装扮响应数据类
     */
    public static class CustomizationResponseData {
        // 所有装扮
        private List<CustomizationItem> all_customizations;
        
        // 已装备的装扮
        private List<CustomizationItem> equipped_customizations;
        
        /**
         * 获取所有装扮
         * @return 所有装扮
         */
        public List<CustomizationItem> getAllCustomizations() {
            return all_customizations;
        }
        
        /**
         * 设置所有装扮
         * @param all_customizations 所有装扮
         */
        public void setAllCustomizations(List<CustomizationItem> all_customizations) {
            this.all_customizations = all_customizations;
        }
        
        /**
         * 获取已装备的装扮
         * @return 已装备的装扮
         */
        public List<CustomizationItem> getEquippedCustomizations() {
            return equipped_customizations;
        }
        
        /**
         * 设置已装备的装扮
         * @param equipped_customizations 已装备的装扮
         */
        public void setEquippedCustomizations(List<CustomizationItem> equipped_customizations) {
            this.equipped_customizations = equipped_customizations;
        }
    }
    
    /**
     * 装扮项类
     */
    public static class CustomizationItem {
        // 装扮ID
        private String customization_id;
        
        // 名称
        private String name;
        
        // 类型
        private String type;
        
        // URL
        private String url;
        
        /**
         * 获取装扮ID
         * @return 装扮ID
         */
        public String getCustomizationId() {
            return customization_id;
        }
        
        /**
         * 设置装扮ID
         * @param customization_id 装扮ID
         */
        public void setCustomizationId(String customization_id) {
            this.customization_id = customization_id;
        }
        
        /**
         * 获取名称
         * @return 名称
         */
        public String getName() {
            return name;
        }
        
        /**
         * 设置名称
         * @param name 名称
         */
        public void setName(String name) {
            this.name = name;
        }
        
        /**
         * 获取类型
         * @return 类型
         */
        public String getType() {
            return type;
        }
        
        /**
         * 设置类型
         * @param type 类型
         */
        public void setType(String type) {
            this.type = type;
        }
        
        /**
         * 获取URL
         * @return URL
         */
        public String getUrl() {
            return url;
        }
        
        /**
         * 设置URL
         * @param url URL
         */
        public void setUrl(String url) {
            this.url = url;
        }
    }
}
