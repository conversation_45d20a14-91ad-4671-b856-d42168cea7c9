// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRepositoryTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAiAgent;

  @NonNull
  public final Button btnClear;

  @NonNull
  public final Button btnDeviceList;

  @NonNull
  public final Button btnGetLocation;

  @NonNull
  public final Button btnLogin;

  @NonNull
  public final Button btnQrCode;

  @NonNull
  public final Button btnReportCommand;

  @NonNull
  public final Button btnReportLocation;

  @NonNull
  public final Button btnReportStatus;

  @NonNull
  public final Button btnUploadMedia;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final TextView tvResult;

  private ActivityRepositoryTestBinding(@NonNull LinearLayout rootView, @NonNull Button btnAiAgent,
      @NonNull Button btnClear, @NonNull Button btnDeviceList, @NonNull Button btnGetLocation,
      @NonNull Button btnLogin, @NonNull Button btnQrCode, @NonNull Button btnReportCommand,
      @NonNull Button btnReportLocation, @NonNull Button btnReportStatus,
      @NonNull Button btnUploadMedia, @NonNull ScrollView scrollView, @NonNull TextView tvResult) {
    this.rootView = rootView;
    this.btnAiAgent = btnAiAgent;
    this.btnClear = btnClear;
    this.btnDeviceList = btnDeviceList;
    this.btnGetLocation = btnGetLocation;
    this.btnLogin = btnLogin;
    this.btnQrCode = btnQrCode;
    this.btnReportCommand = btnReportCommand;
    this.btnReportLocation = btnReportLocation;
    this.btnReportStatus = btnReportStatus;
    this.btnUploadMedia = btnUploadMedia;
    this.scrollView = scrollView;
    this.tvResult = tvResult;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRepositoryTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRepositoryTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_repository_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRepositoryTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_ai_agent;
      Button btnAiAgent = ViewBindings.findChildViewById(rootView, id);
      if (btnAiAgent == null) {
        break missingId;
      }

      id = R.id.btn_clear;
      Button btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.btn_device_list;
      Button btnDeviceList = ViewBindings.findChildViewById(rootView, id);
      if (btnDeviceList == null) {
        break missingId;
      }

      id = R.id.btn_get_location;
      Button btnGetLocation = ViewBindings.findChildViewById(rootView, id);
      if (btnGetLocation == null) {
        break missingId;
      }

      id = R.id.btn_login;
      Button btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.btn_qr_code;
      Button btnQrCode = ViewBindings.findChildViewById(rootView, id);
      if (btnQrCode == null) {
        break missingId;
      }

      id = R.id.btn_report_command;
      Button btnReportCommand = ViewBindings.findChildViewById(rootView, id);
      if (btnReportCommand == null) {
        break missingId;
      }

      id = R.id.btn_report_location;
      Button btnReportLocation = ViewBindings.findChildViewById(rootView, id);
      if (btnReportLocation == null) {
        break missingId;
      }

      id = R.id.btn_report_status;
      Button btnReportStatus = ViewBindings.findChildViewById(rootView, id);
      if (btnReportStatus == null) {
        break missingId;
      }

      id = R.id.btn_upload_media;
      Button btnUploadMedia = ViewBindings.findChildViewById(rootView, id);
      if (btnUploadMedia == null) {
        break missingId;
      }

      id = R.id.scroll_view;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.tv_result;
      TextView tvResult = ViewBindings.findChildViewById(rootView, id);
      if (tvResult == null) {
        break missingId;
      }

      return new ActivityRepositoryTestBinding((LinearLayout) rootView, btnAiAgent, btnClear,
          btnDeviceList, btnGetLocation, btnLogin, btnQrCode, btnReportCommand, btnReportLocation,
          btnReportStatus, btnUploadMedia, scrollView, tvResult);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
