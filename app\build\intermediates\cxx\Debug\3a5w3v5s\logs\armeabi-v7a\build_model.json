{"info": {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, "cxxBuildFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\Debug\\3a5w3v5s\\armeabi-v7a", "soFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\Debug\\3a5w3v5s\\obj\\armeabi-v7a", "soRepublishFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cmake\\debug\\obj\\armeabi-v7a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared"], "cFlagsList": [], "cppFlagsList": [""], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx", "intermediatesBaseFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates", "intermediatesFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app", "moduleBuildFile": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build.gradle", "makeFile": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973", "ndkFolderBeforeSymLinking": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\Workspace\\env\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient", "sdkFolder": "D:\\Workspace\\env\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\Workspace\\env\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\Debug\\3a5w3v5s\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "3a5w3v5sx6e6x29632r406w2k2t1d163o1y6z4x23129454642n4o643a6o2n", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.9.0.\n#   - $NDK is the path to NDK 27.0.12077973.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMA<PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared", "configurationArguments": ["-HD:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973", "-DCMAKE_ANDROID_NDK=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\Workspace\\env\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\Debug\\3a5w3v5s\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\Debug\\3a5w3v5s\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\Debug\\3a5w3v5s\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared"], "stlLibraryFile": "D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "intermediatesParentFolder": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\Debug\\3a5w3v5s"}