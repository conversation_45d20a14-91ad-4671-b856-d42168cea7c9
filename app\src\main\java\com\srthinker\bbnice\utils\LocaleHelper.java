package com.srthinker.bbnice.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.preference.PreferenceManager;
import android.util.Log;

import java.util.Locale;

/**
 * 语言切换工具类
 */
public class LocaleHelper {
    private static final String TAG = "LocaleHelper";
    private static final String SELECTED_LANGUAGE = "selected_language";

    /**
     * 设置应用语言
     * @param context 上下文
     * @param language 语言代码（如"zh"、"en"）
     * @return 更新后的Context
     */
    public static Context setLocale(Context context, String language) {
        saveLanguage(context, language);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }

        return updateResourcesLegacy(context, language);
    }

    /**
     * 获取当前语言
     * @param context 上下文
     * @return 当前语言代码
     */
    public static String getLanguage(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        return preferences.getString(SELECTED_LANGUAGE, getSystemLanguage());
    }

    /**
     * 获取系统语言
     * @return 系统语言代码
     */
    public static String getSystemLanguage() {
        return Locale.getDefault().getLanguage();
    }

    /**
     * 保存语言设置
     * @param context 上下文
     * @param language 语言代码
     */
    private static void saveLanguage(Context context, String language) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(SELECTED_LANGUAGE, language);
        editor.apply();
    }

    /**
     * 更新资源（Android 7.0及以上）
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的Context
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Configuration configuration = context.getResources().getConfiguration();
        configuration.setLocale(locale);
        configuration.setLayoutDirection(locale);

        return context.createConfigurationContext(configuration);
    }

    /**
     * 更新资源（Android 7.0以下）
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的Context
     */
    @SuppressWarnings("deprecation")
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        configuration.locale = locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLayoutDirection(locale);
        }
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());

        return context;
    }
}
