package com.srthinker.bbnice.work;

import android.content.Context;
import android.util.Log;

import androidx.work.Constraints;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.NetworkType;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;

import java.util.concurrent.TimeUnit;

/**
 * 设备状态管理器，负责调度设备状态上报任务
 */
public class DeviceStatusManager {
    private static final String TAG = "DeviceStatusManager";
    private static final String DEVICE_STATUS_WORK_NAME = "device_status_work";
    
    private static DeviceStatusManager instance;
    private final Context context;
    
    private DeviceStatusManager(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized DeviceStatusManager getInstance(Context context) {
        if (instance == null) {
            instance = new DeviceStatusManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 启动设备状态上报服务
     */
    public void startDeviceStatusWorker() {
        Log.d(TAG, "Starting device status report service");
        
        // 设置约束条件：需要网络连接
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();
        
        // 创建周期性工作请求，每1小时执行一次
        PeriodicWorkRequest statusWorkRequest = new PeriodicWorkRequest.Builder(
                DeviceStatusWorker.class, 1, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .build();
        
        // 调度工作
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                DEVICE_STATUS_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                statusWorkRequest
        );
    }
    
    /**
     * 停止设备状态上报服务
     */
    public void stopDeviceStatusWorker() {
        Log.d(TAG, "Stopping device status report service");
        
        // 取消工作
        WorkManager.getInstance(context).cancelUniqueWork(DEVICE_STATUS_WORK_NAME);
    }
}
