package com.srthinker.bbnice.utils;

import android.graphics.Bitmap;
import android.util.Log;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class HttpUtils {
    private static final String TAG = "HttpUtils";
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded");
    private static final MediaType FILE_TYPE = MediaType.parse("application/octet-stream");
    private static final MediaType MEDIA_TYPE_JPEG = MediaType.parse("image/jpeg");

    private static final String FORM_FILE_KEY = "file";
    private static final long TIMEOUT = 15;
    private static volatile HttpUtils instance;
    private final OkHttpClient client;

    public interface HttpCallback {
        void onSuccess(String response);

        void onFailure(String error);
    }

    /**
     * 下载回调接口
     */
    public interface DownloadCallback {
        void onProgress(long currentBytes, long totalBytes);

        void onSuccess(String filePath);

        void onFailure(String error);
    }

    /**
     * 进度监听器接口
     */
    public interface ProgressListener {
        void onProgress(long bytesWritten, long contentLength);
    }

    private HttpUtils() {
        client = new OkHttpClient.Builder()
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
                .build();
    }

    public static HttpUtils getInstance() {
        if (instance == null) {
            synchronized (HttpUtils.class) {
                if (instance == null) {
                    instance = new HttpUtils();
                }
            }
        }
        return instance;
    }

    // 异步GET请求
    public void asyncGet(String url, Map<String, String> params, HttpCallback callback) {
        asyncGet(url, params, null, callback);
    }

    // 异步GET请求（带请求头）
    public void asyncGet(String url, Map<String, String> params, Map<String, String> headers, HttpCallback callback) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
        }
        Request.Builder requestBuilder = new Request.Builder()
                .url(urlBuilder.build())
                .get();

        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                callback.onFailure(e.getMessage());
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                try (ResponseBody body = response.body()) {
                    if (body != null && response.isSuccessful()) {
                        callback.onSuccess(body.string());
                    } else {
                        callback.onFailure("请求失败，状态码：" + response.code());
                    }
                }
            }
        });
    }

    // 异步POST表单请求
    public void asyncPostForm(String url, Map<String, String> params, HttpCallback callback) {
        FormBody.Builder builder = new FormBody.Builder();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.add(entry.getKey(), entry.getValue());
            }
        }
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();

        client.newCall(request).enqueue(createCallback(callback));
    }

    // 异步POST JSON请求
    public void asyncPostJson(String url, String json, HttpCallback callback) {
        asyncPostJson(url, json, null, callback);
    }

    // 异步POST JSON请求（带请求头）
    public void asyncPostJson(String url, String json, Map<String, String> headers, HttpCallback callback) {
        Log.i(TAG, "asyncPostJson url:" + url + " data:" + json);
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        client.newCall(request).enqueue(createCallback(callback));
    }

    // 同步GET请求
    public String syncGet(String url, Map<String, String> params) throws IOException {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
        }
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();

        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }

    // 同步POST表单请求
    public String syncPostForm(String url, Map<String, String> params) throws IOException {
        FormBody.Builder builder = new FormBody.Builder();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.add(entry.getKey(), entry.getValue());
            }
        }
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();

        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }

    // 同步POST JSON请求
    public String syncPostJson(String url, String json, Map<String, String> headers) throws Exception {
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }

    private Callback createCallback(HttpCallback callback) {
        return new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.d(TAG, "onFailure: " + e.getMessage());
                callback.onFailure(e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody body = response.body()) {
                    String bodyStr = body.string();
                    Log.d(TAG, "onResponse: " + bodyStr);
                    if (response.isSuccessful()) {
                        callback.onSuccess(bodyStr);
                    } else {
                        callback.onFailure("请求失败，状态码：" + response.code());
                    }
                }
            }
        };
    }


    /**
     * 异步文件上传（支持多文件）
     *
     * @param url      请求地址
     * @param params   普通参数
     * @param fileKey  文件参数名
     * @param files    文件列表
     * @param callback 回调接口
     */
    public void asyncUploadFiles(String url, Map<String, String> params,
                                 String fileKey, List<File> files,
                                 HttpCallback callback) {
        asyncUploadFiles(url, params, fileKey, files, null, callback);
    }

    /**
     * 异步文件上传（支持多文件，带请求头）
     *
     * @param url      请求地址
     * @param params   普通参数
     * @param fileKey  文件参数名
     * @param files    文件列表
     * @param headers  请求头
     * @param callback 回调接口
     */
    public void asyncUploadFiles(String url, Map<String, String> params,
                                 String fileKey, List<File> files,
                                 Map<String, String> headers,
                                 HttpCallback callback) {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);

        // 添加普通参数
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }

        // 添加文件参数
        for (File file : files) {
            RequestBody fileBody = RequestBody.create(file, FILE_TYPE);
            builder.addFormDataPart(fileKey, file.getName(), fileBody);
        }

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(builder.build());

        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        client.newCall(request).enqueue(createCallback(callback));
    }

    /**
     * 异步上传二进制文件
     *
     * @param url      请求地址
     * @param params   普通参数
     * @param fileKey  文件参数名
     * @param fileData 文件数据
     * @param headers  请求头
     * @param callback 回调接口
     */
    public void asyncUploadFile(String url, Map<String, String> params,
                                String fileKey, byte[] fileData,
                                Map<String, String> headers,
                                HttpCallback callback) {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);

        // 添加普通参数
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }

        // 添加文件参数
        RequestBody fileBody = RequestBody.create(fileData, FILE_TYPE);
        builder.addFormDataPart(fileKey, "file.dat", fileBody);

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(builder.build());

        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        client.newCall(request).enqueue(createCallback(callback));
    }

    /**
     * 异步文件下载
     *
     * @param url      下载地址
     * @param savePath 保存路径
     * @param callback 包含进度回调的接口
     */
    public void asyncDownloadFile(String url, String savePath,
                                  DownloadCallback callback) {
        Request request = new Request.Builder()
                .url(url)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                callback.onFailure(e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    callback.onFailure("下载失败，状态码：" + response.code());
                    return;
                }

                try (ResponseBody body = response.body();
                     InputStream is = body.byteStream();
                     FileOutputStream fos = new FileOutputStream(savePath)) {

                    byte[] buffer = new byte[4096];
                    long total = body.contentLength();
                    long current = 0;
                    int bytesRead;

                    while ((bytesRead = is.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                        current += bytesRead;
                        // 进度回调
                        callback.onProgress(current, total);
                    }

                    callback.onSuccess(savePath);
                } catch (Exception e) {
                    callback.onFailure(e.getMessage());
                }
            }
        });
    }

    /**
     * 同步文件下载
     *
     * @param url      下载地址
     * @param savePath 保存路径
     * @return 文件保存路径
     */
    public String syncDownloadFile(String url, String savePath) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = client.newCall(request).execute();
             ResponseBody body = response.body();
             InputStream is = body.byteStream();
             FileOutputStream fos = new FileOutputStream(savePath)) {

            if (!response.isSuccessful()) {
                throw new IOException("下载失败，状态码：" + response.code());
            }

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            return savePath;
        }
    }

    // 上传方法
    public void uploadBitmap(String url, Bitmap bitmap, Map<String, String> headers, HttpCallback callback) {
        byte[] imageData = BitmapUtils.compressBitmap(bitmap, 300);

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(
                        "file",
                        "upload.jpg",
                        RequestBody.create(imageData, MEDIA_TYPE_JPEG)
                )
                .build();

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();
        client.newCall(request).enqueue(createCallback(callback));
    }

    // 使用示例
    public static void requestTest() {
        // 异步请求示例
        HttpUtils.getInstance().asyncGet("https://api.example.com/data", null, new HttpCallback() {
            @Override
            public void onSuccess(String response) {
                System.out.println("异步请求成功：" + response);
            }

            @Override
            public void onFailure(String error) {
                System.out.println("异步请求失败：" + error);
            }
        });

        // 同步请求示例（需要在子线程执行）
        new Thread(() -> {
            try {
                String response = HttpUtils.getInstance().syncGet("https://api.example.com/data", null);
                System.out.println("同步请求结果：" + response);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();
    }

    // 使用示例
    public static void downloadTest() {
        // 文件上传示例
        List<File> files = Arrays.asList(
                new File("document.pdf"),
                new File("image.jpg")
        );

        HttpUtils.getInstance().asyncUploadFiles("https://api.example.com/upload",
                new HashMap<String, String>() {{
                    put("category", "documents");
                }},
                "attachments",
                files,
                new HttpCallback() {
                    @Override
                    public void onSuccess(String response) {
                        System.out.println("上传成功：" + response);
                    }

                    @Override
                    public void onFailure(String error) {
                        System.out.println("上传失败：" + error);
                    }
                });

        // 文件下载示例
        HttpUtils.getInstance().asyncDownloadFile("https://example.com/file.zip",
                "/downloads/file.zip",
                new DownloadCallback() {
                    @Override
                    public void onProgress(long current, long total) {
                        System.out.printf("下载进度：%.2f%%%n", (current * 100.0 / total));
                    }

                    @Override
                    public void onSuccess(String path) {
                        System.out.println("下载完成：" + path);
                    }

                    @Override
                    public void onFailure(String error) {
                        System.out.println("下载失败：" + error);
                    }
                });
    }
}
