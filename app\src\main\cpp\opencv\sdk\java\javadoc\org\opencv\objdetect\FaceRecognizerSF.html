<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>FaceRecognizerSF (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: FaceRecognizerSF">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class FaceRecognizerSF" class="title">Class FaceRecognizerSF</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.FaceRecognizerSF</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FaceRecognizerSF</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">DNN-based face recognizer

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_recognition_sface</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#FR_COSINE" class="member-name-link">FR_COSINE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#FR_NORM_L2" class="member-name-link">FR_NORM_L2</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#alignCrop(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">alignCrop</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src_img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_box,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;aligned_img)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Aligns detected face with the source input image and crops it</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class with given parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 int&nbsp;backend_id)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class with given parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 int&nbsp;backend_id,
 int&nbsp;target_id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class with given parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 int&nbsp;backend_id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 int&nbsp;backend_id,
 int&nbsp;target_id)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#feature(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">feature</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;aligned_img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Extracts face feature from aligned image</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Calculates the distance between two face features</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#match(org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">match</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature2,
 int&nbsp;dis_type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Calculates the distance between two face features</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="FR_COSINE">
<h3>FR_COSINE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FR_COSINE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.FaceRecognizerSF.FR_COSINE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="FR_NORM_L2">
<h3>FR_NORM_L2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FR_NORM_L2</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.objdetect.FaceRecognizerSF.FR_NORM_L2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="alignCrop(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>alignCrop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">alignCrop</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src_img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_box,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;aligned_img)</span></div>
<div class="block">Aligns detected face with the source input image and crops it</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src_img</code> - input image</dd>
<dd><code>face_box</code> - the detected face result from the input image</dd>
<dd><code>aligned_img</code> - output aligned image</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="feature(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>feature</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">feature</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;aligned_img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature)</span></div>
<div class="block">Extracts face feature from aligned image</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aligned_img</code> - input aligned image</dd>
<dd><code>face_feature</code> - output face feature</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature2,
 int&nbsp;dis_type)</span></div>
<div class="block">Calculates the distance between two face features</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>face_feature1</code> - the first input feature</dd>
<dd><code>face_feature2</code> - the second input feature of the same size and the same type as face_feature1</dd>
<dd><code>dis_type</code> - defines how to calculate the distance between two face features with optional values "FR_COSINE" or "FR_NORM_L2"</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;face_feature2)</span></div>
<div class="block">Calculates the distance between two face features</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>face_feature1</code> - the first input feature</dd>
<dd><code>face_feature2</code> - the second input feature of the same size and the same type as face_feature1</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 int&nbsp;backend_id,
 int&nbsp;target_id)</span></div>
<div class="block">Creates an instance of this class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path of the onnx model used for face recognition</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dd><code>target_id</code> - the id of target device</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 int&nbsp;backend_id)</span></div>
<div class="block">Creates an instance of this class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path of the onnx model used for face recognition</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config)</span></div>
<div class="block">Creates an instance of this class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path of the onnx model used for face recognition</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 int&nbsp;backend_id,
 int&nbsp;target_id)</span></div>
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of the framework (ONNX, etc.)</dd>
<dd><code>bufferModel</code> - A buffer containing the binary model weights.</dd>
<dd><code>bufferConfig</code> - A buffer containing the network configuration.</dd>
<dd><code>backend_id</code> - The id of the backend.</dd>
<dd><code>target_id</code> - The id of the target device.</dd>
<dt>Returns:</dt>
<dd>A pointer to the created instance of FaceRecognizerSF.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 int&nbsp;backend_id)</span></div>
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of the framework (ONNX, etc.)</dd>
<dd><code>bufferModel</code> - A buffer containing the binary model weights.</dd>
<dd><code>bufferConfig</code> - A buffer containing the network configuration.</dd>
<dd><code>backend_id</code> - The id of the backend.</dd>
<dt>Returns:</dt>
<dd>A pointer to the created instance of FaceRecognizerSF.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</span></div>
<div class="block">Creates an instance of this class from a buffer containing the model weights and configuration.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of the framework (ONNX, etc.)</dd>
<dd><code>bufferModel</code> - A buffer containing the binary model weights.</dd>
<dd><code>bufferConfig</code> - A buffer containing the network configuration.</dd>
<dt>Returns:</dt>
<dd>A pointer to the created instance of FaceRecognizerSF.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
