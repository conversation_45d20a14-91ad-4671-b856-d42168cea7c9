<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>CascadeClassifier (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: CascadeClassifier">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class CascadeClassifier" class="title">Class CascadeClassifier</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.CascadeClassifier</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CascadeClassifier</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Cascade classifier class for object detection.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">CascadeClassifier</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">CascadeClassifier</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color">
<div class="block">Loads a classifier from a file.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#convert(java.lang.String,java.lang.String)" class="member-name-link">convert</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;oldcascade,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newcascade)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int,org.opencv.core.Size)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">detectMultiScale</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects objects of different sizes in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int,org.opencv.core.Size)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">detectMultiScale2</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size,org.opencv.core.Size)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size,org.opencv.core.Size,boolean)" class="member-name-link">detectMultiScale3</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
 boolean&nbsp;outputRejectLevels)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks whether the classifier has been loaded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFeatureType()" class="member-name-link">getFeatureType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOriginalWindowSize()" class="member-name-link">getOriginalWindowSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isOldFormatCascade()" class="member-name-link">isOldFormatCascade</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#load(java.lang.String)" class="member-name-link">load</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads a classifier from a file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>CascadeClassifier</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CascadeClassifier</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>CascadeClassifier</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CascadeClassifier</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Loads a classifier from a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file from which the classifier is loaded.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">empty</span>()</div>
<div class="block">Checks whether the classifier has been loaded.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="load(java.lang.String)">
<h3>load</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">load</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Loads a classifier from a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - Name of the file from which the classifier is loaded. The file may contain an old
     HAAR classifier trained by the haartraining application or a new cascade classifier trained by the
     traincascade application.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int,org.opencv.core.Size,org.opencv.core.Size)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
<dd><code>maxSize</code> - Maximum possible object size. Objects larger than that are ignored. If <code>maxSize == minSize</code> model is evaluated on single scale.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int,org.opencv.core.Size)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int,int)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double,int)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect,double)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 double&nbsp;scaleFactor)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale(org.opencv.core.Mat,org.opencv.core.MatOfRect)">
<h3>detectMultiScale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</span></div>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int,org.opencv.core.Size,org.opencv.core.Size)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
<dd><code>maxSize</code> - Maximum possible object size. Objects larger than that are ignored. If <code>maxSize == minSize</code> model is evaluated on single scale.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int,org.opencv.core.Size)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int,int)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double,int)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,double)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
 double&nbsp;scaleFactor)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale2(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt)">
<h3>detectMultiScale2</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale2</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size,org.opencv.core.Size,boolean)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
 boolean&nbsp;outputRejectLevels)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
<dd><code>maxSize</code> - automatically generated</dd>
<dd><code>outputRejectLevels</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size,org.opencv.core.Size)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
<dd><code>maxSize</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int,org.opencv.core.Size)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int,int)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors,
 int&nbsp;flags)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double,int)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor,
 int&nbsp;minNeighbors)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble,double)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
 double&nbsp;scaleFactor)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMultiScale3(org.opencv.core.Mat,org.opencv.core.MatOfRect,org.opencv.core.MatOfInt,org.opencv.core.MatOfDouble)">
<h3>detectMultiScale3</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detectMultiScale3</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
 <a href="../core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
 <a href="../core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</span></div>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isOldFormatCascade()">
<h3>isOldFormatCascade</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOldFormatCascade</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOriginalWindowSize()">
<h3>getOriginalWindowSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">getOriginalWindowSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getFeatureType()">
<h3>getFeatureType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFeatureType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="convert(java.lang.String,java.lang.String)">
<h3>convert</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">convert</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;oldcascade,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newcascade)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
