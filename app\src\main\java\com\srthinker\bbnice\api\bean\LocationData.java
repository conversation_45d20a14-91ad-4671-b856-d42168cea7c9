package com.srthinker.bbnice.api.bean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 位置数据类
 * 用于表示地理位置信息
 */
public class LocationData {
    // 纬度
    private double latitude;
    
    // 经度
    private double longitude;
    
    // 海拔
    private double altitude;
    
    // 精度
    private float accuracy;
    
    // 速度
    private float speed;
    
    // 方向
    private float bearing;
    
    // 提供者
    private String provider;
    
    // 时间
    private long time;
    
    // 地址
    private String address;
    
    // 国家
    private String country;
    
    // 省份
    private String province;
    
    // 城市
    private String city;
    
    // 区县
    private String district;
    
    // 街道
    private String street;
    
    // 街道号
    private String streetNumber;
    
    // 邮政编码
    private String postalCode;
    
    /**
     * 构造函数
     */
    public LocationData() {
    }
    
    /**
     * 构造函数
     * @param latitude 纬度
     * @param longitude 经度
     */
    public LocationData(double latitude, double longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.time = System.currentTimeMillis();
    }
    
    /**
     * 构造函数
     * @param latitude 纬度
     * @param longitude 经度
     * @param altitude 海拔
     * @param accuracy 精度
     * @param speed 速度
     * @param bearing 方向
     * @param provider 提供者
     */
    public LocationData(double latitude, double longitude, double altitude, float accuracy, float speed, float bearing, String provider) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.altitude = altitude;
        this.accuracy = accuracy;
        this.speed = speed;
        this.bearing = bearing;
        this.provider = provider;
        this.time = System.currentTimeMillis();
    }
    
    /**
     * 获取纬度
     * @return 纬度
     */
    public double getLatitude() {
        return latitude;
    }
    
    /**
     * 设置纬度
     * @param latitude 纬度
     */
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
    
    /**
     * 获取经度
     * @return 经度
     */
    public double getLongitude() {
        return longitude;
    }
    
    /**
     * 设置经度
     * @param longitude 经度
     */
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    
    /**
     * 获取海拔
     * @return 海拔
     */
    public double getAltitude() {
        return altitude;
    }
    
    /**
     * 设置海拔
     * @param altitude 海拔
     */
    public void setAltitude(double altitude) {
        this.altitude = altitude;
    }
    
    /**
     * 获取精度
     * @return 精度
     */
    public float getAccuracy() {
        return accuracy;
    }
    
    /**
     * 设置精度
     * @param accuracy 精度
     */
    public void setAccuracy(float accuracy) {
        this.accuracy = accuracy;
    }
    
    /**
     * 获取速度
     * @return 速度
     */
    public float getSpeed() {
        return speed;
    }
    
    /**
     * 设置速度
     * @param speed 速度
     */
    public void setSpeed(float speed) {
        this.speed = speed;
    }
    
    /**
     * 获取方向
     * @return 方向
     */
    public float getBearing() {
        return bearing;
    }
    
    /**
     * 设置方向
     * @param bearing 方向
     */
    public void setBearing(float bearing) {
        this.bearing = bearing;
    }
    
    /**
     * 获取提供者
     * @return 提供者
     */
    public String getProvider() {
        return provider;
    }
    
    /**
     * 设置提供者
     * @param provider 提供者
     */
    public void setProvider(String provider) {
        this.provider = provider;
    }
    
    /**
     * 获取时间
     * @return 时间
     */
    public long getTime() {
        return time;
    }
    
    /**
     * 设置时间
     * @param time 时间
     */
    public void setTime(long time) {
        this.time = time;
    }
    
    /**
     * 获取地址
     * @return 地址
     */
    public String getAddress() {
        return address;
    }
    
    /**
     * 设置地址
     * @param address 地址
     */
    public void setAddress(String address) {
        this.address = address;
    }
    
    /**
     * 获取国家
     * @return 国家
     */
    public String getCountry() {
        return country;
    }
    
    /**
     * 设置国家
     * @param country 国家
     */
    public void setCountry(String country) {
        this.country = country;
    }
    
    /**
     * 获取省份
     * @return 省份
     */
    public String getProvince() {
        return province;
    }
    
    /**
     * 设置省份
     * @param province 省份
     */
    public void setProvince(String province) {
        this.province = province;
    }
    
    /**
     * 获取城市
     * @return 城市
     */
    public String getCity() {
        return city;
    }
    
    /**
     * 设置城市
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city;
    }
    
    /**
     * 获取区县
     * @return 区县
     */
    public String getDistrict() {
        return district;
    }
    
    /**
     * 设置区县
     * @param district 区县
     */
    public void setDistrict(String district) {
        this.district = district;
    }
    
    /**
     * 获取街道
     * @return 街道
     */
    public String getStreet() {
        return street;
    }
    
    /**
     * 设置街道
     * @param street 街道
     */
    public void setStreet(String street) {
        this.street = street;
    }
    
    /**
     * 获取街道号
     * @return 街道号
     */
    public String getStreetNumber() {
        return streetNumber;
    }
    
    /**
     * 设置街道号
     * @param streetNumber 街道号
     */
    public void setStreetNumber(String streetNumber) {
        this.streetNumber = streetNumber;
    }
    
    /**
     * 获取邮政编码
     * @return 邮政编码
     */
    public String getPostalCode() {
        return postalCode;
    }
    
    /**
     * 设置邮政编码
     * @param postalCode 邮政编码
     */
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
    
    /**
     * 获取格式化的时间
     * @return 格式化的时间
     */
    public String getFormattedTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date(time));
    }
    
    @Override
    public String toString() {
        return "LocationData{" +
                "latitude=" + latitude +
                ", longitude=" + longitude +
                ", altitude=" + altitude +
                ", accuracy=" + accuracy +
                ", speed=" + speed +
                ", bearing=" + bearing +
                ", provider='" + provider + '\'' +
                ", time=" + getFormattedTime() +
                ", address='" + address + '\'' +
                '}';
    }
}
