{"logs": [{"outputFile": "com.srthinker.bbnice.test.app-mergeDebugAndroidTestResources-1:/values-v18/values-v18.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\9cf4a72b95b2d005237db73885482c8c\\transformed\\jetified-core-1.5.0\\res\\values-v18\\values.xml", "from": {"startLines": "4,12", "startColumns": "0,0", "startOffsets": "180,596", "endLines": "11,19", "endColumns": "8,8", "endOffsets": "595,1009"}, "to": {"startLines": "2,10", "startColumns": "4,4", "startOffsets": "55,475", "endLines": "9,17", "endColumns": "8,8", "endOffsets": "470,888"}}]}]}