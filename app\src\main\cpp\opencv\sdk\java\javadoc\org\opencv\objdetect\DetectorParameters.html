<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>DetectorParameters (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: DetectorParameters">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class DetectorParameters" class="title">Class DetectorParameters</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.DetectorParameters</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DetectorParameters</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">struct DetectorParameters is used by ArucoDetector</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DetectorParameters</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_adaptiveThreshConstant()" class="member-name-link">get_adaptiveThreshConstant</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_adaptiveThreshWinSizeMax()" class="member-name-link">get_adaptiveThreshWinSizeMax</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_adaptiveThreshWinSizeMin()" class="member-name-link">get_adaptiveThreshWinSizeMin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_adaptiveThreshWinSizeStep()" class="member-name-link">get_adaptiveThreshWinSizeStep</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagCriticalRad()" class="member-name-link">get_aprilTagCriticalRad</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagDeglitch()" class="member-name-link">get_aprilTagDeglitch</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagMaxLineFitMse()" class="member-name-link">get_aprilTagMaxLineFitMse</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagMaxNmaxima()" class="member-name-link">get_aprilTagMaxNmaxima</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagMinClusterPixels()" class="member-name-link">get_aprilTagMinClusterPixels</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagMinWhiteBlackDiff()" class="member-name-link">get_aprilTagMinWhiteBlackDiff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagQuadDecimate()" class="member-name-link">get_aprilTagQuadDecimate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_aprilTagQuadSigma()" class="member-name-link">get_aprilTagQuadSigma</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_cornerRefinementMaxIterations()" class="member-name-link">get_cornerRefinementMaxIterations</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_cornerRefinementMethod()" class="member-name-link">get_cornerRefinementMethod</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_cornerRefinementMinAccuracy()" class="member-name-link">get_cornerRefinementMinAccuracy</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_cornerRefinementWinSize()" class="member-name-link">get_cornerRefinementWinSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_detectInvertedMarker()" class="member-name-link">get_detectInvertedMarker</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_errorCorrectionRate()" class="member-name-link">get_errorCorrectionRate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_markerBorderBits()" class="member-name-link">get_markerBorderBits</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_maxErroneousBitsInBorderRate()" class="member-name-link">get_maxErroneousBitsInBorderRate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_maxMarkerPerimeterRate()" class="member-name-link">get_maxMarkerPerimeterRate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minCornerDistanceRate()" class="member-name-link">get_minCornerDistanceRate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minDistanceToBorder()" class="member-name-link">get_minDistanceToBorder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minGroupDistance()" class="member-name-link">get_minGroupDistance</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minMarkerDistanceRate()" class="member-name-link">get_minMarkerDistanceRate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minMarkerLengthRatioOriginalImg()" class="member-name-link">get_minMarkerLengthRatioOriginalImg</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minMarkerPerimeterRate()" class="member-name-link">get_minMarkerPerimeterRate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minOtsuStdDev()" class="member-name-link">get_minOtsuStdDev</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_minSideLengthCanonicalImg()" class="member-name-link">get_minSideLengthCanonicalImg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_perspectiveRemoveIgnoredMarginPerCell()" class="member-name-link">get_perspectiveRemoveIgnoredMarginPerCell</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_perspectiveRemovePixelPerCell()" class="member-name-link">get_perspectiveRemovePixelPerCell</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_polygonalApproxAccuracyRate()" class="member-name-link">get_polygonalApproxAccuracyRate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_relativeCornerRefinmentWinSize()" class="member-name-link">get_relativeCornerRefinmentWinSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get_useAruco3Detection()" class="member-name-link">get_useAruco3Detection</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_adaptiveThreshConstant(double)" class="member-name-link">set_adaptiveThreshConstant</a><wbr>(double&nbsp;adaptiveThreshConstant)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_adaptiveThreshWinSizeMax(int)" class="member-name-link">set_adaptiveThreshWinSizeMax</a><wbr>(int&nbsp;adaptiveThreshWinSizeMax)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_adaptiveThreshWinSizeMin(int)" class="member-name-link">set_adaptiveThreshWinSizeMin</a><wbr>(int&nbsp;adaptiveThreshWinSizeMin)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_adaptiveThreshWinSizeStep(int)" class="member-name-link">set_adaptiveThreshWinSizeStep</a><wbr>(int&nbsp;adaptiveThreshWinSizeStep)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagCriticalRad(float)" class="member-name-link">set_aprilTagCriticalRad</a><wbr>(float&nbsp;aprilTagCriticalRad)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagDeglitch(int)" class="member-name-link">set_aprilTagDeglitch</a><wbr>(int&nbsp;aprilTagDeglitch)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagMaxLineFitMse(float)" class="member-name-link">set_aprilTagMaxLineFitMse</a><wbr>(float&nbsp;aprilTagMaxLineFitMse)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagMaxNmaxima(int)" class="member-name-link">set_aprilTagMaxNmaxima</a><wbr>(int&nbsp;aprilTagMaxNmaxima)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagMinClusterPixels(int)" class="member-name-link">set_aprilTagMinClusterPixels</a><wbr>(int&nbsp;aprilTagMinClusterPixels)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagMinWhiteBlackDiff(int)" class="member-name-link">set_aprilTagMinWhiteBlackDiff</a><wbr>(int&nbsp;aprilTagMinWhiteBlackDiff)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagQuadDecimate(float)" class="member-name-link">set_aprilTagQuadDecimate</a><wbr>(float&nbsp;aprilTagQuadDecimate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_aprilTagQuadSigma(float)" class="member-name-link">set_aprilTagQuadSigma</a><wbr>(float&nbsp;aprilTagQuadSigma)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_cornerRefinementMaxIterations(int)" class="member-name-link">set_cornerRefinementMaxIterations</a><wbr>(int&nbsp;cornerRefinementMaxIterations)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_cornerRefinementMethod(int)" class="member-name-link">set_cornerRefinementMethod</a><wbr>(int&nbsp;cornerRefinementMethod)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_cornerRefinementMinAccuracy(double)" class="member-name-link">set_cornerRefinementMinAccuracy</a><wbr>(double&nbsp;cornerRefinementMinAccuracy)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_cornerRefinementWinSize(int)" class="member-name-link">set_cornerRefinementWinSize</a><wbr>(int&nbsp;cornerRefinementWinSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_detectInvertedMarker(boolean)" class="member-name-link">set_detectInvertedMarker</a><wbr>(boolean&nbsp;detectInvertedMarker)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_errorCorrectionRate(double)" class="member-name-link">set_errorCorrectionRate</a><wbr>(double&nbsp;errorCorrectionRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_markerBorderBits(int)" class="member-name-link">set_markerBorderBits</a><wbr>(int&nbsp;markerBorderBits)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_maxErroneousBitsInBorderRate(double)" class="member-name-link">set_maxErroneousBitsInBorderRate</a><wbr>(double&nbsp;maxErroneousBitsInBorderRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_maxMarkerPerimeterRate(double)" class="member-name-link">set_maxMarkerPerimeterRate</a><wbr>(double&nbsp;maxMarkerPerimeterRate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minCornerDistanceRate(double)" class="member-name-link">set_minCornerDistanceRate</a><wbr>(double&nbsp;minCornerDistanceRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minDistanceToBorder(int)" class="member-name-link">set_minDistanceToBorder</a><wbr>(int&nbsp;minDistanceToBorder)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minGroupDistance(float)" class="member-name-link">set_minGroupDistance</a><wbr>(float&nbsp;minGroupDistance)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minMarkerDistanceRate(double)" class="member-name-link">set_minMarkerDistanceRate</a><wbr>(double&nbsp;minMarkerDistanceRate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minMarkerLengthRatioOriginalImg(float)" class="member-name-link">set_minMarkerLengthRatioOriginalImg</a><wbr>(float&nbsp;minMarkerLengthRatioOriginalImg)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minMarkerPerimeterRate(double)" class="member-name-link">set_minMarkerPerimeterRate</a><wbr>(double&nbsp;minMarkerPerimeterRate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minOtsuStdDev(double)" class="member-name-link">set_minOtsuStdDev</a><wbr>(double&nbsp;minOtsuStdDev)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_minSideLengthCanonicalImg(int)" class="member-name-link">set_minSideLengthCanonicalImg</a><wbr>(int&nbsp;minSideLengthCanonicalImg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_perspectiveRemoveIgnoredMarginPerCell(double)" class="member-name-link">set_perspectiveRemoveIgnoredMarginPerCell</a><wbr>(double&nbsp;perspectiveRemoveIgnoredMarginPerCell)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_perspectiveRemovePixelPerCell(int)" class="member-name-link">set_perspectiveRemovePixelPerCell</a><wbr>(int&nbsp;perspectiveRemovePixelPerCell)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_polygonalApproxAccuracyRate(double)" class="member-name-link">set_polygonalApproxAccuracyRate</a><wbr>(double&nbsp;polygonalApproxAccuracyRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_relativeCornerRefinmentWinSize(float)" class="member-name-link">set_relativeCornerRefinmentWinSize</a><wbr>(float&nbsp;relativeCornerRefinmentWinSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set_useAruco3Detection(boolean)" class="member-name-link">set_useAruco3Detection</a><wbr>(boolean&nbsp;useAruco3Detection)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DetectorParameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DetectorParameters</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_adaptiveThreshWinSizeMin()">
<h3>get_adaptiveThreshWinSizeMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_adaptiveThreshWinSizeMin</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_adaptiveThreshWinSizeMin(int)">
<h3>set_adaptiveThreshWinSizeMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_adaptiveThreshWinSizeMin</span><wbr><span class="parameters">(int&nbsp;adaptiveThreshWinSizeMin)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_adaptiveThreshWinSizeMax()">
<h3>get_adaptiveThreshWinSizeMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_adaptiveThreshWinSizeMax</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_adaptiveThreshWinSizeMax(int)">
<h3>set_adaptiveThreshWinSizeMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_adaptiveThreshWinSizeMax</span><wbr><span class="parameters">(int&nbsp;adaptiveThreshWinSizeMax)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_adaptiveThreshWinSizeStep()">
<h3>get_adaptiveThreshWinSizeStep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_adaptiveThreshWinSizeStep</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_adaptiveThreshWinSizeStep(int)">
<h3>set_adaptiveThreshWinSizeStep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_adaptiveThreshWinSizeStep</span><wbr><span class="parameters">(int&nbsp;adaptiveThreshWinSizeStep)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_adaptiveThreshConstant()">
<h3>get_adaptiveThreshConstant</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_adaptiveThreshConstant</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_adaptiveThreshConstant(double)">
<h3>set_adaptiveThreshConstant</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_adaptiveThreshConstant</span><wbr><span class="parameters">(double&nbsp;adaptiveThreshConstant)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minMarkerPerimeterRate()">
<h3>get_minMarkerPerimeterRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_minMarkerPerimeterRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minMarkerPerimeterRate(double)">
<h3>set_minMarkerPerimeterRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minMarkerPerimeterRate</span><wbr><span class="parameters">(double&nbsp;minMarkerPerimeterRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_maxMarkerPerimeterRate()">
<h3>get_maxMarkerPerimeterRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_maxMarkerPerimeterRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_maxMarkerPerimeterRate(double)">
<h3>set_maxMarkerPerimeterRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_maxMarkerPerimeterRate</span><wbr><span class="parameters">(double&nbsp;maxMarkerPerimeterRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_polygonalApproxAccuracyRate()">
<h3>get_polygonalApproxAccuracyRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_polygonalApproxAccuracyRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_polygonalApproxAccuracyRate(double)">
<h3>set_polygonalApproxAccuracyRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_polygonalApproxAccuracyRate</span><wbr><span class="parameters">(double&nbsp;polygonalApproxAccuracyRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minCornerDistanceRate()">
<h3>get_minCornerDistanceRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_minCornerDistanceRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minCornerDistanceRate(double)">
<h3>set_minCornerDistanceRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minCornerDistanceRate</span><wbr><span class="parameters">(double&nbsp;minCornerDistanceRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minDistanceToBorder()">
<h3>get_minDistanceToBorder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_minDistanceToBorder</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minDistanceToBorder(int)">
<h3>set_minDistanceToBorder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minDistanceToBorder</span><wbr><span class="parameters">(int&nbsp;minDistanceToBorder)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minMarkerDistanceRate()">
<h3>get_minMarkerDistanceRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_minMarkerDistanceRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minMarkerDistanceRate(double)">
<h3>set_minMarkerDistanceRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minMarkerDistanceRate</span><wbr><span class="parameters">(double&nbsp;minMarkerDistanceRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minGroupDistance()">
<h3>get_minGroupDistance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_minGroupDistance</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minGroupDistance(float)">
<h3>set_minGroupDistance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minGroupDistance</span><wbr><span class="parameters">(float&nbsp;minGroupDistance)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_cornerRefinementMethod()">
<h3>get_cornerRefinementMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_cornerRefinementMethod</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_cornerRefinementMethod(int)">
<h3>set_cornerRefinementMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_cornerRefinementMethod</span><wbr><span class="parameters">(int&nbsp;cornerRefinementMethod)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_cornerRefinementWinSize()">
<h3>get_cornerRefinementWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_cornerRefinementWinSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_cornerRefinementWinSize(int)">
<h3>set_cornerRefinementWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_cornerRefinementWinSize</span><wbr><span class="parameters">(int&nbsp;cornerRefinementWinSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_relativeCornerRefinmentWinSize()">
<h3>get_relativeCornerRefinmentWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_relativeCornerRefinmentWinSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_relativeCornerRefinmentWinSize(float)">
<h3>set_relativeCornerRefinmentWinSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_relativeCornerRefinmentWinSize</span><wbr><span class="parameters">(float&nbsp;relativeCornerRefinmentWinSize)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_cornerRefinementMaxIterations()">
<h3>get_cornerRefinementMaxIterations</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_cornerRefinementMaxIterations</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_cornerRefinementMaxIterations(int)">
<h3>set_cornerRefinementMaxIterations</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_cornerRefinementMaxIterations</span><wbr><span class="parameters">(int&nbsp;cornerRefinementMaxIterations)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_cornerRefinementMinAccuracy()">
<h3>get_cornerRefinementMinAccuracy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_cornerRefinementMinAccuracy</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_cornerRefinementMinAccuracy(double)">
<h3>set_cornerRefinementMinAccuracy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_cornerRefinementMinAccuracy</span><wbr><span class="parameters">(double&nbsp;cornerRefinementMinAccuracy)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_markerBorderBits()">
<h3>get_markerBorderBits</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_markerBorderBits</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_markerBorderBits(int)">
<h3>set_markerBorderBits</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_markerBorderBits</span><wbr><span class="parameters">(int&nbsp;markerBorderBits)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_perspectiveRemovePixelPerCell()">
<h3>get_perspectiveRemovePixelPerCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_perspectiveRemovePixelPerCell</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_perspectiveRemovePixelPerCell(int)">
<h3>set_perspectiveRemovePixelPerCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_perspectiveRemovePixelPerCell</span><wbr><span class="parameters">(int&nbsp;perspectiveRemovePixelPerCell)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_perspectiveRemoveIgnoredMarginPerCell()">
<h3>get_perspectiveRemoveIgnoredMarginPerCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_perspectiveRemoveIgnoredMarginPerCell</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_perspectiveRemoveIgnoredMarginPerCell(double)">
<h3>set_perspectiveRemoveIgnoredMarginPerCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_perspectiveRemoveIgnoredMarginPerCell</span><wbr><span class="parameters">(double&nbsp;perspectiveRemoveIgnoredMarginPerCell)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_maxErroneousBitsInBorderRate()">
<h3>get_maxErroneousBitsInBorderRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_maxErroneousBitsInBorderRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_maxErroneousBitsInBorderRate(double)">
<h3>set_maxErroneousBitsInBorderRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_maxErroneousBitsInBorderRate</span><wbr><span class="parameters">(double&nbsp;maxErroneousBitsInBorderRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minOtsuStdDev()">
<h3>get_minOtsuStdDev</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_minOtsuStdDev</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minOtsuStdDev(double)">
<h3>set_minOtsuStdDev</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minOtsuStdDev</span><wbr><span class="parameters">(double&nbsp;minOtsuStdDev)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_errorCorrectionRate()">
<h3>get_errorCorrectionRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">get_errorCorrectionRate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_errorCorrectionRate(double)">
<h3>set_errorCorrectionRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_errorCorrectionRate</span><wbr><span class="parameters">(double&nbsp;errorCorrectionRate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagQuadDecimate()">
<h3>get_aprilTagQuadDecimate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_aprilTagQuadDecimate</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagQuadDecimate(float)">
<h3>set_aprilTagQuadDecimate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagQuadDecimate</span><wbr><span class="parameters">(float&nbsp;aprilTagQuadDecimate)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagQuadSigma()">
<h3>get_aprilTagQuadSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_aprilTagQuadSigma</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagQuadSigma(float)">
<h3>set_aprilTagQuadSigma</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagQuadSigma</span><wbr><span class="parameters">(float&nbsp;aprilTagQuadSigma)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagMinClusterPixels()">
<h3>get_aprilTagMinClusterPixels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_aprilTagMinClusterPixels</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagMinClusterPixels(int)">
<h3>set_aprilTagMinClusterPixels</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagMinClusterPixels</span><wbr><span class="parameters">(int&nbsp;aprilTagMinClusterPixels)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagMaxNmaxima()">
<h3>get_aprilTagMaxNmaxima</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_aprilTagMaxNmaxima</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagMaxNmaxima(int)">
<h3>set_aprilTagMaxNmaxima</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagMaxNmaxima</span><wbr><span class="parameters">(int&nbsp;aprilTagMaxNmaxima)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagCriticalRad()">
<h3>get_aprilTagCriticalRad</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_aprilTagCriticalRad</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagCriticalRad(float)">
<h3>set_aprilTagCriticalRad</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagCriticalRad</span><wbr><span class="parameters">(float&nbsp;aprilTagCriticalRad)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagMaxLineFitMse()">
<h3>get_aprilTagMaxLineFitMse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_aprilTagMaxLineFitMse</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagMaxLineFitMse(float)">
<h3>set_aprilTagMaxLineFitMse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagMaxLineFitMse</span><wbr><span class="parameters">(float&nbsp;aprilTagMaxLineFitMse)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagMinWhiteBlackDiff()">
<h3>get_aprilTagMinWhiteBlackDiff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_aprilTagMinWhiteBlackDiff</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagMinWhiteBlackDiff(int)">
<h3>set_aprilTagMinWhiteBlackDiff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagMinWhiteBlackDiff</span><wbr><span class="parameters">(int&nbsp;aprilTagMinWhiteBlackDiff)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_aprilTagDeglitch()">
<h3>get_aprilTagDeglitch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_aprilTagDeglitch</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_aprilTagDeglitch(int)">
<h3>set_aprilTagDeglitch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_aprilTagDeglitch</span><wbr><span class="parameters">(int&nbsp;aprilTagDeglitch)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_detectInvertedMarker()">
<h3>get_detectInvertedMarker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">get_detectInvertedMarker</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_detectInvertedMarker(boolean)">
<h3>set_detectInvertedMarker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_detectInvertedMarker</span><wbr><span class="parameters">(boolean&nbsp;detectInvertedMarker)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_useAruco3Detection()">
<h3>get_useAruco3Detection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">get_useAruco3Detection</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_useAruco3Detection(boolean)">
<h3>set_useAruco3Detection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_useAruco3Detection</span><wbr><span class="parameters">(boolean&nbsp;useAruco3Detection)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minSideLengthCanonicalImg()">
<h3>get_minSideLengthCanonicalImg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">get_minSideLengthCanonicalImg</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minSideLengthCanonicalImg(int)">
<h3>set_minSideLengthCanonicalImg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minSideLengthCanonicalImg</span><wbr><span class="parameters">(int&nbsp;minSideLengthCanonicalImg)</span></div>
</section>
</li>
<li>
<section class="detail" id="get_minMarkerLengthRatioOriginalImg()">
<h3>get_minMarkerLengthRatioOriginalImg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">get_minMarkerLengthRatioOriginalImg</span>()</div>
</section>
</li>
<li>
<section class="detail" id="set_minMarkerLengthRatioOriginalImg(float)">
<h3>set_minMarkerLengthRatioOriginalImg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">set_minMarkerLengthRatioOriginalImg</span><wbr><span class="parameters">(float&nbsp;minMarkerLengthRatioOriginalImg)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
