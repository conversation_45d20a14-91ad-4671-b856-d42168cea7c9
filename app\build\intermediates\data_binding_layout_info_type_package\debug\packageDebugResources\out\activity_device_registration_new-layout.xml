<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_device_registration_new" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_device_registration_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_device_registration_new_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="111" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="49"/></Target><Target id="@+id/content_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="96" endOffset="55"/></Target><Target id="@+id/ivQrCode" view="ImageView"><Expressions/><location startLine="30" startOffset="8" endLine="39" endOffset="59"/></Target><Target id="@+id/tvInstructions" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="55" endOffset="65"/></Target><Target id="@+id/tvExpireTime" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="68" endOffset="39"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="71" startOffset="8" endLine="81" endOffset="34"/></Target><Target id="@+id/tvDeviceId" view="TextView"><Expressions/><location startLine="84" startOffset="8" endLine="94" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="99" startOffset="4" endLine="109" endOffset="36"/></Target></Targets></Layout>