<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".setting.AboutActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:title="@string/about_device" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 设备名称 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/device_name"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_device_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="18sp"
                        tools:text="BBNice Device" />

                    <ImageButton
                        android:id="@+id/btn_copy_device_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/copy_to_clipboard"
                        android:src="@android:drawable/ic_menu_save" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 设备型号 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/device_model"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_device_model"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="18sp"
                        tools:text="BBNice Model X" />

                    <ImageButton
                        android:id="@+id/btn_copy_device_model"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/copy_to_clipboard"
                        android:src="@android:drawable/ic_menu_save" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 安卓版本 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/device_version"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_device_version"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="18sp"
                        tools:text="Android 11" />

                    <ImageButton
                        android:id="@+id/btn_copy_device_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/copy_to_clipboard"
                        android:src="@android:drawable/ic_menu_save" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 序列号 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/device_serial"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_device_serial"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="18sp"
                        tools:text="BBNICE123456789" />

                    <ImageButton
                        android:id="@+id/btn_copy_device_serial"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/copy_to_clipboard"
                        android:src="@android:drawable/ic_menu_save" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 应用版本 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/app_version"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_app_version"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="18sp"
                        tools:text="Version 1.0.0" />

                    <TextView
                        android:id="@+id/tv_app_build"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textSize="14sp"
                        tools:text="Build 100" />

                    <ImageButton
                        android:id="@+id/btn_copy_app_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/copy_to_clipboard"
                        android:src="@android:drawable/ic_menu_save" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 权限警告 -->
            <TextView
                android:id="@+id/tv_permission_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFECB3"
                android:padding="16dp"
                android:text="@string/phone_permission_required"
                android:textColor="#B71C1C"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
