// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityGalleryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout bottomActionBar;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnDelete;

  @NonNull
  public final Button btnSelectAll;

  @NonNull
  public final ImageButton btnSelectMode;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvEmpty;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final ViewPager2 viewPager;

  private ActivityGalleryBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout bottomActionBar, @NonNull ImageButton btnBack,
      @NonNull Button btnDelete, @NonNull Button btnSelectAll, @NonNull ImageButton btnSelectMode,
      @NonNull ProgressBar progressBar, @NonNull TabLayout tabLayout, @NonNull Toolbar toolbar,
      @NonNull TextView tvEmpty, @NonNull TextView tvTitle, @NonNull ViewPager2 viewPager) {
    this.rootView = rootView;
    this.bottomActionBar = bottomActionBar;
    this.btnBack = btnBack;
    this.btnDelete = btnDelete;
    this.btnSelectAll = btnSelectAll;
    this.btnSelectMode = btnSelectMode;
    this.progressBar = progressBar;
    this.tabLayout = tabLayout;
    this.toolbar = toolbar;
    this.tvEmpty = tvEmpty;
    this.tvTitle = tvTitle;
    this.viewPager = viewPager;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityGalleryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityGalleryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_gallery, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityGalleryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_action_bar;
      LinearLayout bottomActionBar = ViewBindings.findChildViewById(rootView, id);
      if (bottomActionBar == null) {
        break missingId;
      }

      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_delete;
      Button btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btn_select_all;
      Button btnSelectAll = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectAll == null) {
        break missingId;
      }

      id = R.id.btn_select_mode;
      ImageButton btnSelectMode = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectMode == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tab_layout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_empty;
      TextView tvEmpty = ViewBindings.findChildViewById(rootView, id);
      if (tvEmpty == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.view_pager;
      ViewPager2 viewPager = ViewBindings.findChildViewById(rootView, id);
      if (viewPager == null) {
        break missingId;
      }

      return new ActivityGalleryBinding((ConstraintLayout) rootView, bottomActionBar, btnBack,
          btnDelete, btnSelectAll, btnSelectMode, progressBar, tabLayout, toolbar, tvEmpty, tvTitle,
          viewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
