package com.srthinker.bbnice.gallery;

import android.app.Activity;
import android.app.RecoverableSecurityException;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.content.IntentSender;
import android.database.Cursor;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;
import android.util.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 媒体文件工具类，用于扫描和管理设备上的媒体文件
 */
public class MediaUtils {
    private static final String TAG = "MediaUtils";

    /**
     * 获取设备上的所有图片和视频
     * @param context 上下文
     * @return 媒体文件列表
     */
    public static List<MediaItem> getAllMedia(Context context) {
        List<MediaItem> mediaItems = new ArrayList<>();
        mediaItems.addAll(getImages(context));
        mediaItems.addAll(getVideos(context));
        return mediaItems;
    }

    /**
     * 获取设备上的所有图片
     * @param context 上下文
     * @return 图片文件列表
     */
    public static List<MediaItem> getImages(Context context) {
        List<MediaItem> imageList = new ArrayList<>();
        ContentResolver contentResolver = context.getContentResolver();
        Uri collection;

        // 根据Android版本选择不同的URI
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            collection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL);
        } else {
            collection = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        }

        // 定义查询列
        String[] projection = new String[]{
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DISPLAY_NAME,
                MediaStore.Images.Media.DATA,
                MediaStore.Images.Media.SIZE,
                MediaStore.Images.Media.DATE_ADDED
        };

        // 按日期降序排序
        String sortOrder = MediaStore.Images.Media.DATE_ADDED + " DESC";

        try (Cursor cursor = contentResolver.query(
                collection,
                projection,
                null,
                null,
                sortOrder
        )) {
            if (cursor != null) {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME);
                int dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                int sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE);
                int dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED);

                while (cursor.moveToNext()) {
                    long id = cursor.getLong(idColumn);
                    String name = cursor.getString(nameColumn);
                    String path = cursor.getString(dataColumn);
                    long size = cursor.getLong(sizeColumn);
                    long dateAdded = cursor.getLong(dateColumn);

                    Uri contentUri = ContentUris.withAppendedId(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);

                    // 创建MediaItem对象并添加到列表
                    MediaItem item = new MediaItem(id, contentUri, name, path, size, dateAdded, MediaItem.TYPE_IMAGE, 0);
                    imageList.add(item);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading images: " + e.getMessage());
        }

        return imageList;
    }

    /**
     * 获取设备上的所有视频
     * @param context 上下文
     * @return 视频文件列表
     */
    public static List<MediaItem> getVideos(Context context) {
        List<MediaItem> videoList = new ArrayList<>();
        ContentResolver contentResolver = context.getContentResolver();
        Uri collection;

        // 根据Android版本选择不同的URI
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            collection = MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL);
        } else {
            collection = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
        }

        // 定义查询列
        String[] projection = new String[]{
                MediaStore.Video.Media._ID,
                MediaStore.Video.Media.DISPLAY_NAME,
                MediaStore.Video.Media.DATA,
                MediaStore.Video.Media.SIZE,
                MediaStore.Video.Media.DATE_ADDED,
                MediaStore.Video.Media.DURATION
        };

        // 按日期降序排序
        String sortOrder = MediaStore.Video.Media.DATE_ADDED + " DESC";

        try (Cursor cursor = contentResolver.query(
                collection,
                projection,
                null,
                null,
                sortOrder
        )) {
            if (cursor != null) {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME);
                int dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA);
                int sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE);
                int dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED);
                int durationColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION);

                while (cursor.moveToNext()) {
                    try {
                        long id = cursor.getLong(idColumn);
                        String name = cursor.getString(nameColumn);
                        String path = cursor.getString(dataColumn);
                        long size = cursor.getLong(sizeColumn);
                        long dateAdded = cursor.getLong(dateColumn);
                        long duration = cursor.getLong(durationColumn);

                        // 检查文件是否存在且可读
                        File file = new File(path);
                        if (!file.exists() || !file.canRead()) {
                            Log.w(TAG, "Video file does not exist or cannot be read: " + path);
                            continue;
                        }

                        Uri contentUri = ContentUris.withAppendedId(
                                MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id);

                        // 验证视频文件是否可以被MediaMetadataRetriever打开
                        if (isValidVideoFile(context, contentUri)) {
                            // 创建MediaItem对象并添加到列表
                            MediaItem item = new MediaItem(id, contentUri, name, path, size, dateAdded, MediaItem.TYPE_VIDEO, duration);
                            videoList.add(item);
                        } else {
                            Log.w(TAG, "Invalid video file: " + path);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error processing video item: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading videos: " + e.getMessage());
        }

        return videoList;
    }

    /**
     * 验证视频文件是否有效
     * @param context 上下文
     * @param uri 视频URI
     * @return 是否有效
     */
    private static boolean isValidVideoFile(Context context, Uri uri) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            retriever.setDataSource(context, uri);
            // 尝试获取视频时长，如果成功则视频有效
            String duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            return duration != null;
        } catch (Exception e) {
            Log.e(TAG, "Error validating video file: " + e.getMessage());
            return false;
        } finally {
            try {
                retriever.release();
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 删除媒体文件
     * @param context 上下文
     * @param item 要删除的媒体项
     * @return 是否删除成功
     */
    public static boolean deleteMedia(Context context, MediaItem item) {
        try {
            ContentResolver contentResolver = context.getContentResolver();
            Uri uri = item.getUri();

            Log.d(TAG, "Attempting to delete media: " + item.getDisplayName() + ", URI: " + uri);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10及以上版本，需要特殊处理
                try {
                    // 尝试使用ContentResolver删除
                    int deletedRows = contentResolver.delete(uri, null, null);
                    Log.d(TAG, "Delete result (Android 10+): " + deletedRows + " rows deleted");

                    if (deletedRows > 0) {
                        return true;
                    }
                } catch (SecurityException e) {
                    Log.e(TAG, "Security exception when deleting media: " + e.getMessage());
                    return false;
                }
            } else {
                // Android 9及以下版本，可以直接使用ContentResolver删除
                int deletedRows = contentResolver.delete(uri, null, null);
                Log.d(TAG, "Delete result (Android 9-): " + deletedRows + " rows deleted");

                if (deletedRows > 0) {
                    return true;
                }
            }

            // 如果ContentResolver删除失败，尝试直接删除文件
            // 注意：这在Android 10+上可能不起作用，除非应用有所有文件的访问权限
            File file = new File(item.getPath());
            boolean fileDeleted = file.exists() && file.delete();
            Log.d(TAG, "File delete result: " + fileDeleted + ", path: " + item.getPath());

            if (fileDeleted) {
                // 通知媒体库刷新
                if (item.isImage()) {
                    contentResolver.delete(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            MediaStore.Images.Media.DATA + "=?",
                            new String[]{item.getPath()});
                } else if (item.isVideo()) {
                    contentResolver.delete(
                            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                            MediaStore.Video.Media.DATA + "=?",
                            new String[]{item.getPath()});
                }
                return true;
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error deleting media: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 请求删除媒体文件（适用于Android 10及以上版本）
     * 此方法会触发系统权限对话框，请求用户授权删除文件
     * @param activity 活动
     * @param item 要删除的媒体项
     * @param requestCode 请求码，用于在onActivityResult中识别请求
     * @return 是否成功发送请求
     */
    public static boolean requestDeleteMedia(Activity activity, MediaItem item, int requestCode) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            // Android 9及以下版本不需要特殊处理
            return deleteMedia(activity, item);
        }

        try {
            ContentResolver contentResolver = activity.getContentResolver();
            Uri uri = item.getUri();

            try {
                // 尝试直接删除
                int deletedRows = contentResolver.delete(uri, null, null);
                if (deletedRows > 0) {
                    return true;
                }
            } catch (RecoverableSecurityException e) {
                // 捕获可恢复的安全异常，获取IntentSender
                IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                activity.startIntentSenderForResult(intentSender, requestCode, null, 0, 0, 0);
                return true; // 请求已发送，但删除尚未完成
            } catch (SecurityException e) {
                Log.e(TAG, "Security exception when requesting delete: " + e.getMessage());
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error requesting delete media: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 批量请求删除媒体文件（适用于Android 10及以上版本）
     * @param activity 活动
     * @param items 要删除的媒体项列表
     * @param requestCode 请求码
     * @return 成功删除的项目数量
     */
    public static int requestDeleteMediaItems(Activity activity, List<MediaItem> items, int requestCode) {
        if (items == null || items.isEmpty()) {
            return 0;
        }

        int successCount = 0;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本，使用批量删除请求
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                try {
                    // Android 11及以上版本支持批量删除请求
                    List<Uri> uris = new ArrayList<>();
                    for (MediaItem item : items) {
                        uris.add(item.getUri());
                    }

                    // 创建批量删除请求
                    MediaStore.createDeleteRequest(activity.getContentResolver(), uris);
                    // 发送请求
                    IntentSender intentSender = MediaStore.createDeleteRequest(activity.getContentResolver(), uris).getIntentSender();
                    activity.startIntentSenderForResult(intentSender, requestCode, null, 0, 0, 0);
                    return 0; // 请求已发送，但删除尚未完成，返回0
                } catch (Exception e) {
                    Log.e(TAG, "Error creating batch delete request: " + e.getMessage());
                    // 如果批量删除失败，回退到单个删除
                }
            }

            // 如果不支持批量删除或批量删除失败，尝试逐个删除
            for (MediaItem item : items) {
                if (deleteMedia(activity, item)) {
                    successCount++;
                } else {
                    // 尝试请求权限删除
                    requestDeleteMedia(activity, item, requestCode);
                    // 注意：这里不增加successCount，因为删除结果将在onActivityResult中处理
                    break; // 只发送一个请求，避免多个对话框
                }
            }
        } else {
            // Android 9及以下版本，直接删除
            for (MediaItem item : items) {
                if (deleteMedia(activity, item)) {
                    successCount++;
                }
            }
        }

        return successCount;
    }
}
