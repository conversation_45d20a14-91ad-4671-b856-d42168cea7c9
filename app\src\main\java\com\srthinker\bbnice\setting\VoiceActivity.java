package com.srthinker.bbnice.setting;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;

import com.srthinker.bbnice.R;

public class VoiceActivity extends AppCompatActivity {

    private static final int REQUEST_DO_NOT_DISTURB_PERMISSION = 100;

    private Toolbar toolbar;
    private SwitchCompat switchMute;
    private SeekBar seekBarMediaVolume;
    private SeekBar seekBarRingVolume;
    private TextView tvMediaVolumeLevel;
    private TextView tvRingVolumeLevel;
    private TextView tvPermissionWarning;
    private ImageButton btnMediaVolumeUp;
    private ImageButton btnMediaVolumeDown;
    private ImageButton btnRingVolumeUp;
    private ImageButton btnRingVolumeDown;

    private AudioManager audioManager;
    private NotificationManager notificationManager;

    private int maxMediaVolume;
    private int maxRingVolume;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice);

        // 初始化视图
        initViews();

        // 初始化音频管理器
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // 获取最大音量
        maxMediaVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        maxRingVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_RING);

        // 设置SeekBar最大值
        seekBarMediaVolume.setMax(maxMediaVolume);
        seekBarRingVolume.setMax(maxRingVolume);

        // 更新当前音量
        updateVolumeUI();

        // 更新静音状态
        updateMuteUI();

        // 设置监听器
        setupListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.voice_settings));

        switchMute = findViewById(R.id.switch_mute);
        seekBarMediaVolume = findViewById(R.id.seekbar_media_volume);
        seekBarRingVolume = findViewById(R.id.seekbar_ring_volume);
        tvMediaVolumeLevel = findViewById(R.id.tv_media_volume_level);
        tvRingVolumeLevel = findViewById(R.id.tv_ring_volume_level);
        tvPermissionWarning = findViewById(R.id.tv_permission_warning);
        btnMediaVolumeUp = findViewById(R.id.btn_media_volume_up);
        btnMediaVolumeDown = findViewById(R.id.btn_media_volume_down);
        btnRingVolumeUp = findViewById(R.id.btn_ring_volume_up);
        btnRingVolumeDown = findViewById(R.id.btn_ring_volume_down);
    }

    private void setupListeners() {
        // 静音开关监听器
        switchMute.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                toggleMuteMode(isChecked);
            }
        });

        // 媒体音量SeekBar监听器
        seekBarMediaVolume.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    setMediaVolume(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
            }
        });

        // 铃声音量SeekBar监听器
        seekBarRingVolume.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    setRingVolume(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
            }
        });

        // 媒体音量按钮监听器
        btnMediaVolumeUp.setOnClickListener(v -> increaseMediaVolume());
        btnMediaVolumeDown.setOnClickListener(v -> decreaseMediaVolume());

        // 铃声音量按钮监听器
        btnRingVolumeUp.setOnClickListener(v -> increaseRingVolume());
        btnRingVolumeDown.setOnClickListener(v -> decreaseRingVolume());
    }

    private void updateVolumeUI() {
        // 获取当前音量
        int currentMediaVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        int currentRingVolume = audioManager.getStreamVolume(AudioManager.STREAM_RING);

        // 更新SeekBar
        seekBarMediaVolume.setProgress(currentMediaVolume);
        seekBarRingVolume.setProgress(currentRingVolume);

        // 更新音量百分比显示
        int mediaVolumePercent = (int) (((float) currentMediaVolume / maxMediaVolume) * 100);
        int ringVolumePercent = (int) (((float) currentRingVolume / maxRingVolume) * 100);

        tvMediaVolumeLevel.setText(getString(R.string.volume_level, mediaVolumePercent));
        tvRingVolumeLevel.setText(getString(R.string.volume_level, ringVolumePercent));
    }

    private void updateMuteUI() {
        boolean isMuted = false;

        // 检查是否处于勿扰模式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (notificationManager.isNotificationPolicyAccessGranted()) {
                int currentInterruptionFilter = notificationManager.getCurrentInterruptionFilter();
                isMuted = currentInterruptionFilter != NotificationManager.INTERRUPTION_FILTER_ALL;
                switchMute.setChecked(isMuted);
                tvPermissionWarning.setVisibility(View.GONE);
            } else {
                // 没有勿扰模式权限
                tvPermissionWarning.setVisibility(View.VISIBLE);
                switchMute.setEnabled(false);
            }
        } else {
            // 检查铃声模式
            int ringerMode = audioManager.getRingerMode();
            isMuted = ringerMode == AudioManager.RINGER_MODE_SILENT || ringerMode == AudioManager.RINGER_MODE_VIBRATE;
            switchMute.setChecked(isMuted);
        }
    }

    private void toggleMuteMode(boolean enable) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (notificationManager.isNotificationPolicyAccessGranted()) {
                // 设置勿扰模式
                notificationManager.setInterruptionFilter(enable ?
                        NotificationManager.INTERRUPTION_FILTER_PRIORITY :
                        NotificationManager.INTERRUPTION_FILTER_ALL);
                Toast.makeText(this, enable ? R.string.mute_enabled : R.string.mute_disabled, Toast.LENGTH_SHORT).show();
            } else {
                // 请求勿扰模式权限
                requestDoNotDisturbPermission();
            }
        } else {
            // 设置铃声模式
            audioManager.setRingerMode(enable ? AudioManager.RINGER_MODE_SILENT : AudioManager.RINGER_MODE_NORMAL);
            Toast.makeText(this, enable ? R.string.mute_enabled : R.string.mute_disabled, Toast.LENGTH_SHORT).show();
        }
    }

    private void requestDoNotDisturbPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            new AlertDialog.Builder(this)
                    .setTitle(R.string.permission_required)
                    .setMessage(R.string.do_not_disturb_permission_required)
                    .setPositiveButton(R.string.go_to_notification_settings, (dialog, which) -> {
                        Intent intent = new Intent(Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS);
                        startActivityForResult(intent, REQUEST_DO_NOT_DISTURB_PERMISSION);
                    })
                    .setNegativeButton(R.string.cancel, null)
                    .show();
        }
    }

    private void setMediaVolume(int volume) {
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);
        updateVolumeUI();
    }

    private void setRingVolume(int volume) {
        audioManager.setStreamVolume(AudioManager.STREAM_RING, volume, 0);
        updateVolumeUI();
    }

    private void increaseMediaVolume() {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (currentVolume < maxMediaVolume) {
            setMediaVolume(currentVolume + 1);
        }
    }

    private void decreaseMediaVolume() {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (currentVolume > 0) {
            setMediaVolume(currentVolume - 1);
        }
    }

    private void increaseRingVolume() {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_RING);
        if (currentVolume < maxRingVolume) {
            setRingVolume(currentVolume + 1);
        }
    }

    private void decreaseRingVolume() {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_RING);
        if (currentVolume > 0) {
            setRingVolume(currentVolume - 1);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_DO_NOT_DISTURB_PERMISSION) {
            // 检查权限是否已授予
            updateMuteUI();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 更新UI
        updateVolumeUI();
        updateMuteUI();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}