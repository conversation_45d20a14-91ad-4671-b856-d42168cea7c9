// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CallFragmentPageBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnCall;

  @NonNull
  public final ImageView btnFinish;

  @NonNull
  public final ImageView callBg;

  @NonNull
  public final ImageView callIcon;

  @NonNull
  public final TextView tvName;

  private CallFragmentPageBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnCall,
      @NonNull ImageView btnFinish, @NonNull ImageView callBg, @NonNull ImageView callIcon,
      @NonNull TextView tvName) {
    this.rootView = rootView;
    this.btnCall = btnCall;
    this.btnFinish = btnFinish;
    this.callBg = callBg;
    this.callIcon = callIcon;
    this.tvName = tvName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CallFragmentPageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CallFragmentPageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.call_fragment_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CallFragmentPageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_call;
      Button btnCall = ViewBindings.findChildViewById(rootView, id);
      if (btnCall == null) {
        break missingId;
      }

      id = R.id.btn_finish;
      ImageView btnFinish = ViewBindings.findChildViewById(rootView, id);
      if (btnFinish == null) {
        break missingId;
      }

      id = R.id.call_bg;
      ImageView callBg = ViewBindings.findChildViewById(rootView, id);
      if (callBg == null) {
        break missingId;
      }

      id = R.id.call_icon;
      ImageView callIcon = ViewBindings.findChildViewById(rootView, id);
      if (callIcon == null) {
        break missingId;
      }

      id = R.id.tv_name;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      return new CallFragmentPageBinding((ConstraintLayout) rootView, btnCall, btnFinish, callBg,
          callIcon, tvName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
