package com.srthinker.bbnice.db;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

/**
 * Room数据库类
 */
@Database(entities = {ChatMessageEntity.class}, version = 1, exportSchema = false)
public abstract class ChatDatabase extends RoomDatabase {
    
    private static final String DATABASE_NAME = "bbnice_chat.db";
    private static volatile ChatDatabase instance;
    
    /**
     * 获取ChatMessageDao
     * @return ChatMessageDao实例
     */
    public abstract ChatMessageDao chatMessageDao();
    
    /**
     * 获取数据库实例
     * @param context 上下文
     * @return 数据库实例
     */
    public static synchronized ChatDatabase getInstance(Context context) {
        if (instance == null) {
            instance = Room.databaseBuilder(
                    context.getApplicationContext(),
                    ChatDatabase.class,
                    DATABASE_NAME)
                    .build();
        }
        return instance;
    }
}
