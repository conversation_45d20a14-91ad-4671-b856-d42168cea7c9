package com.srthinker.bbnice.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.srthinker.bbnice.chat.ChatMessage;

import java.util.UUID;

/**
 * 聊天消息实体类，用于Room数据库
 */
@Entity(tableName = "chat_messages")
public class ChatMessageEntity {
    
    @PrimaryKey
    @NonNull
    private String messageId;
    
    private String content;
    private boolean isSentByMe;
    private long timestamp;
    private String roomType;
    private boolean isUploaded;
    
    /**
     * 默认构造函数，Room需要
     */
    public ChatMessageEntity() {
        this.messageId = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.isUploaded = false;
    }
    
    /**
     * 从ChatMessage创建实体
     * @param message 聊天消息
     */
    public ChatMessageEntity(ChatMessage message) {
        this.messageId = message.getMessageId();
        this.content = message.getContent();
        this.isSentByMe = message.isSentByMe();
        this.timestamp = message.getTimestamp();
        this.roomType = message.getRoomType();
        this.isUploaded = message.isUploaded();
    }
    
    /**
     * 转换为ChatMessage
     * @return ChatMessage对象
     */
    public ChatMessage toChatMessage() {
        return new ChatMessage(
                messageId,
                content,
                isSentByMe,
                timestamp,
                roomType,
                isUploaded
        );
    }
    
    @NonNull
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(@NonNull String messageId) {
        this.messageId = messageId;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public boolean isSentByMe() {
        return isSentByMe;
    }
    
    public void setSentByMe(boolean sentByMe) {
        isSentByMe = sentByMe;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getRoomType() {
        return roomType;
    }
    
    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }
    
    public boolean isUploaded() {
        return isUploaded;
    }
    
    public void setUploaded(boolean uploaded) {
        isUploaded = uploaded;
    }
}
