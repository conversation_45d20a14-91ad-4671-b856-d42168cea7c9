package com.srthinker.bbnice.registration;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.utils.SPUtils;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.home.HomeActivity;
import com.srthinker.bbnice.utils.DeviceIdUtils;

import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * 设备注册Activity
 * 用于显示设备注册二维码
 */
public class DeviceRegistrationActivity extends AppCompatActivity {
    private static final String TAG = "DeviceRegistration";
    private static final long QR_REFRESH_INTERVAL = 15 * 60 * 1000; // 15分钟刷新一次二维码
    private static final long LOGIN_CHECK_INTERVAL = 5000; // 5秒检查一次登录状态
    private static final String KEY_DEVICE_REGISTERED = "device_registered"; // 设备注册状态的键

    private DeviceRepository deviceRepository;
    private TextView tvDeviceId;
    private TextView tvStatus;
    private ImageView ivQrCode;
    private TextView tvExpireTime;
    private TextView tvInstructions;
    private ProgressBar progressBar;
    private String deviceId;
    private CountDownTimer qrRefreshTimer;
    private Handler handler;
    private Runnable loginCheckRunnable;
    private boolean isLoginChecking = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_device_registration_new);

        // 初始化Repository
        deviceRepository = ApiRepositoryProvider.getInstance(this).getDeviceRepository();

        // 初始化视图
        initViews();

        // 获取设备ID
        deviceId = DeviceIdUtils.getDeviceId(this);
        tvDeviceId.setText(getString(R.string.device_id) + ": " + deviceId);

        // 初始化Handler
        handler = new Handler(Looper.getMainLooper());

        // 获取二维码
        requestQrCode();
    }

    private void initViews() {
        // 初始化Toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setTitle(getString(R.string.device_registration));

        // 初始化其他视图
        tvDeviceId = findViewById(R.id.tvDeviceId);
        tvStatus = findViewById(R.id.tvStatus);
        ivQrCode = findViewById(R.id.ivQrCode);
        tvExpireTime = findViewById(R.id.tvExpireTime);
        tvInstructions = findViewById(R.id.tvInstructions);
        progressBar = findViewById(R.id.progressBar);

        // 设置说明文字
        tvInstructions.setText(getString(R.string.scan_qr_instruction));
    }

    /**
     * 请求二维码
     */
    private void requestQrCode() {
        showLoading();
        tvStatus.setText(getString(R.string.refreshing_qr_code));

        String deviceId = DeviceIdUtils.getDeviceId(this);
        // 使用deviceRepository获取二维码
        deviceRepository.getDeviceQrCode().observe(this, new Observer<Result<QrCodeResponse>>() {
            @Override
            public void onChanged(Result<QrCodeResponse> result) {
                if (result.isLoading()) {
                    showLoading();
                } else if (result.isSuccess()) {
                    hideLoading();
                    QrCodeResponse.QrCodeResponseData data = result.getData().getData();
                    if (data != null && data.getQrcodeUrl() != null) {
                        // 加载二维码图片
                        loadQrCodeImage(data.getQrcodeUrl());
                        // 开始倒计时
                        startQrRefreshTimer();
                        // 开始检查登录状态
                        startLoginCheck();
                    } else {
                        showError(getString(R.string.error_unknown));
                    }
                } else if (result.isError()) {
                    hideLoading();
                    showError(result.getError().getMessage());
                }
            }
        });
    }

    /**
     * 加载二维码图片
     */
    private void loadQrCodeImage(String url) {
        // 使用Glide加载二维码图片
        Glide.with(this)
                .load(url)
                .apply(new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true))
                .into(ivQrCode);

        tvStatus.setText(getString(R.string.waiting_for_scan));
    }

    /**
     * 开始二维码刷新倒计时
     */
    private void startQrRefreshTimer() {
        // 取消之前的倒计时
        if (qrRefreshTimer != null) {
            qrRefreshTimer.cancel();
        }

        // 创建新的倒计时
        qrRefreshTimer = new CountDownTimer(QR_REFRESH_INTERVAL, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                // 格式化剩余时间
                String remainingTimeStr = formatTime(millisUntilFinished);
                tvExpireTime.setText(getString(R.string.qr_code_refresh_time, remainingTimeStr));
            }

            @Override
            public void onFinish() {
                // 倒计时结束，刷新二维码
                requestQrCode();
            }
        }.start();
    }

    /**
     * 开始检查登录状态
     */
    private void startLoginCheck() {
        // 避免重复启动
        if (isLoginChecking) {
            return;
        }

        isLoginChecking = true;

        // 创建检查任务
        loginCheckRunnable = new Runnable() {
            @Override
            public void run() {
                checkLoginStatus();
            }
        };

        // 开始检查
        handler.postDelayed(loginCheckRunnable, LOGIN_CHECK_INTERVAL);
    }

    /**
     * 检查登录状态
     */
    private void checkLoginStatus() {
        tvStatus.setText(getString(R.string.checking_login_status));

        // 调用登录接口
        deviceRepository.login().observe(this, new Observer<Result<LoginResponse>>() {
            @Override
            public void onChanged(Result<LoginResponse> result) {
                if (result.isLoading()) {
                    // 正在加载，不做处理
                } else if (result.isSuccess()) {
                    // 登录成功
                    loginSuccess();
                } else {
                    // 登录失败，继续检查
                    tvStatus.setText(getString(R.string.waiting_for_scan));
                    handler.postDelayed(loginCheckRunnable, LOGIN_CHECK_INTERVAL);
                }
            }
        });
    }

    /**
     * 登录成功处理
     */
    private void loginSuccess() {
        // 停止所有定时任务
        stopAllTimers();

        // 保存设备注册状态
        SPUtils.getInstance().saveBoolean(KEY_DEVICE_REGISTERED, true);

        // 显示成功提示
        Toast.makeText(this, getString(R.string.login_success), Toast.LENGTH_SHORT).show();

        // 跳转到主页
        Intent intent = new Intent(this, HomeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * 格式化时间
     */
    private String formatTime(long millis) {
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis);
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) -
                TimeUnit.MINUTES.toSeconds(minutes);
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
    }

    /**
     * 显示加载中
     */
    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        ivQrCode.setVisibility(View.GONE);
        tvExpireTime.setVisibility(View.GONE);
    }

    /**
     * 隐藏加载中
     */
    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        ivQrCode.setVisibility(View.VISIBLE);
        tvExpireTime.setVisibility(View.VISIBLE);
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        tvStatus.setText(message);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        // 5秒后重试
        handler.postDelayed(this::requestQrCode, 5000);
    }

    /**
     * 停止所有定时任务
     */
    private void stopAllTimers() {
        // 停止二维码刷新倒计时
        if (qrRefreshTimer != null) {
            qrRefreshTimer.cancel();
        }

        // 停止登录检查
        if (loginCheckRunnable != null) {
            handler.removeCallbacks(loginCheckRunnable);
        }

        isLoginChecking = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopAllTimers();
    }
}
