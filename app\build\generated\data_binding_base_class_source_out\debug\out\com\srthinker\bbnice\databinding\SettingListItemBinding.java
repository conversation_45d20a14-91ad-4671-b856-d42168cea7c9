// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SettingListItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView settingIcon;

  @NonNull
  public final TextView settingTitle;

  private SettingListItemBinding(@NonNull LinearLayout rootView, @NonNull ImageView settingIcon,
      @NonNull TextView settingTitle) {
    this.rootView = rootView;
    this.settingIcon = settingIcon;
    this.settingTitle = settingTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SettingListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SettingListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.setting_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SettingListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.setting_icon;
      ImageView settingIcon = ViewBindings.findChildViewById(rootView, id);
      if (settingIcon == null) {
        break missingId;
      }

      id = R.id.setting_title;
      TextView settingTitle = ViewBindings.findChildViewById(rootView, id);
      if (settingTitle == null) {
        break missingId;
      }

      return new SettingListItemBinding((LinearLayout) rootView, settingIcon, settingTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
