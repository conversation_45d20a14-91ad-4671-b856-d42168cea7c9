package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * 媒体响应类
 * 用于表示上传媒体API的响应
 */
public class MediaResponse extends BaseResponse {
    // 响应数据
    private final MediaResponseData data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public MediaResponse(ApiResponse<MediaResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public MediaResponseData getData() {
        return data;
    }
    
    /**
     * 媒体响应数据类
     */
    public static class MediaResponseData {
        // 媒体ID
        private String media_id;
        
        // URL
        private String url;
        
        /**
         * 获取媒体ID
         * @return 媒体ID
         */
        public String getMediaId() {
            return media_id;
        }
        
        /**
         * 设置媒体ID
         * @param media_id 媒体ID
         */
        public void setMediaId(String media_id) {
            this.media_id = media_id;
        }
        
        /**
         * 获取URL
         * @return URL
         */
        public String getUrl() {
            return url;
        }
        
        /**
         * 设置URL
         * @param url URL
         */
        public void setUrl(String url) {
            this.url = url;
        }

        @Override
        public String toString() {
            return "MediaResponseData{" +
                    "media_id='" + media_id + '\'' +
                    ", url='" + url + '\'' +
                    '}';
        }
    }
}
