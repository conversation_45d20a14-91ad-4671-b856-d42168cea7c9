package com.srthinker.bbnice.setting;

import android.Manifest;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.srthinker.bbnice.R;

import java.util.UUID;

public class AboutActivity extends AppCompatActivity {

    private static final int REQUEST_PHONE_STATE = 100;

    private Toolbar toolbar;
    private TextView tvDeviceName;
    private TextView tvDeviceModel;
    private TextView tvDeviceVersion;
    private TextView tvDeviceSerial;
    private TextView tvAppVersion;
    private TextView tvAppBuild;
    private TextView tvPermissionWarning;
    private ImageButton btnCopyDeviceName;
    private ImageButton btnCopyDeviceModel;
    private ImageButton btnCopyDeviceVersion;
    private ImageButton btnCopyDeviceSerial;
    private ImageButton btnCopyAppVersion;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);

        initViews();
        setupListeners();
        
        // 检查权限并更新UI
        checkPhoneStatePermission();
        updateDeviceInfo();
        updateAppInfo();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.about_device));

        tvDeviceName = findViewById(R.id.tv_device_name);
        tvDeviceModel = findViewById(R.id.tv_device_model);
        tvDeviceVersion = findViewById(R.id.tv_device_version);
        tvDeviceSerial = findViewById(R.id.tv_device_serial);
        tvAppVersion = findViewById(R.id.tv_app_version);
        tvAppBuild = findViewById(R.id.tv_app_build);
        tvPermissionWarning = findViewById(R.id.tv_permission_warning);
        
        btnCopyDeviceName = findViewById(R.id.btn_copy_device_name);
        btnCopyDeviceModel = findViewById(R.id.btn_copy_device_model);
        btnCopyDeviceVersion = findViewById(R.id.btn_copy_device_version);
        btnCopyDeviceSerial = findViewById(R.id.btn_copy_device_serial);
        btnCopyAppVersion = findViewById(R.id.btn_copy_app_version);
    }

    private void setupListeners() {
        // 复制按钮点击监听器
        btnCopyDeviceName.setOnClickListener(v -> copyToClipboard("Device Name", tvDeviceName.getText().toString()));
        btnCopyDeviceModel.setOnClickListener(v -> copyToClipboard("Device Model", tvDeviceModel.getText().toString()));
        btnCopyDeviceVersion.setOnClickListener(v -> copyToClipboard("Android Version", tvDeviceVersion.getText().toString()));
        btnCopyDeviceSerial.setOnClickListener(v -> copyToClipboard("Serial Number", tvDeviceSerial.getText().toString()));
        btnCopyAppVersion.setOnClickListener(v -> copyToClipboard("App Version", 
                tvAppVersion.getText().toString() + " " + tvAppBuild.getText().toString()));
        
        // 权限警告点击监听器
        tvPermissionWarning.setOnClickListener(v -> requestPhoneStatePermission());
    }

    private void updateDeviceInfo() {
        // 设备名称
        String deviceName = Settings.Global.getString(getContentResolver(), Settings.Global.DEVICE_NAME);
        if (deviceName == null || deviceName.isEmpty()) {
            deviceName = Build.MODEL;
        }
        tvDeviceName.setText(deviceName);
        
        // 设备型号
        String deviceModel = Build.MANUFACTURER + " " + Build.MODEL;
        tvDeviceModel.setText(deviceModel);
        
        // 安卓版本
        String deviceVersion = "Android " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")";
        tvDeviceVersion.setText(deviceVersion);
        
        // 序列号
        String serial = getDeviceSerial();
        tvDeviceSerial.setText(serial);
    }

    private void updateAppInfo() {
        try {
            PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String versionName = packageInfo.versionName;
            int versionCode = packageInfo.versionCode;
            
            tvAppVersion.setText(getString(R.string.app_version_name, versionName));
            tvAppBuild.setText(getString(R.string.app_build_number, String.valueOf(versionCode)));
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            tvAppVersion.setText(getString(R.string.app_version_name, "Unknown"));
            tvAppBuild.setText(getString(R.string.app_build_number, "0"));
        }
    }

    private String getDeviceSerial() {
        // 检查权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) 
                != PackageManager.PERMISSION_GRANTED) {
            tvPermissionWarning.setVisibility(View.VISIBLE);
            return getString(R.string.unknown_device);
        }
        
        tvPermissionWarning.setVisibility(View.GONE);
        
        // 获取序列号
        String serial = "";
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                serial = Build.getSerial();
            } else {
                serial = Build.SERIAL;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
            serial = getString(R.string.unknown_device);
        }
        
        // 如果序列号为空或unknown，生成一个唯一ID
        if (serial == null || serial.isEmpty() || "unknown".equalsIgnoreCase(serial)) {
            String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
            if (androidId == null || androidId.isEmpty() || "9774d56d682e549c".equals(androidId)) {
                // 生成UUID作为备选
                serial = UUID.randomUUID().toString();
            } else {
                serial = androidId;
            }
        }
        
        return serial;
    }

    private void copyToClipboard(String label, String text) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText(label, text);
        clipboard.setPrimaryClip(clip);
        Toast.makeText(this, getString(R.string.copied_to_clipboard), Toast.LENGTH_SHORT).show();
    }

    private void checkPhoneStatePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) 
                != PackageManager.PERMISSION_GRANTED) {
            tvPermissionWarning.setVisibility(View.VISIBLE);
        } else {
            tvPermissionWarning.setVisibility(View.GONE);
        }
    }

    private void requestPhoneStatePermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.READ_PHONE_STATE}, 
                REQUEST_PHONE_STATE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PHONE_STATE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限已授予，更新UI
                tvPermissionWarning.setVisibility(View.GONE);
                updateDeviceInfo();
            } else {
                // 权限被拒绝
                tvPermissionWarning.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 更新UI
        checkPhoneStatePermission();
        updateDeviceInfo();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
