// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLanguageSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnApply;

  @NonNull
  public final RadioGroup languageRadioGroup;

  @NonNull
  public final RadioButton radioChinese;

  @NonNull
  public final RadioButton radioEnglish;

  @NonNull
  public final RadioButton radioSystem;

  private ActivityLanguageSettingsBinding(@NonNull LinearLayout rootView, @NonNull Button btnApply,
      @NonNull RadioGroup languageRadioGroup, @NonNull RadioButton radioChinese,
      @NonNull RadioButton radioEnglish, @NonNull RadioButton radioSystem) {
    this.rootView = rootView;
    this.btnApply = btnApply;
    this.languageRadioGroup = languageRadioGroup;
    this.radioChinese = radioChinese;
    this.radioEnglish = radioEnglish;
    this.radioSystem = radioSystem;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLanguageSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLanguageSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_language_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLanguageSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply;
      Button btnApply = ViewBindings.findChildViewById(rootView, id);
      if (btnApply == null) {
        break missingId;
      }

      id = R.id.language_radio_group;
      RadioGroup languageRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (languageRadioGroup == null) {
        break missingId;
      }

      id = R.id.radio_chinese;
      RadioButton radioChinese = ViewBindings.findChildViewById(rootView, id);
      if (radioChinese == null) {
        break missingId;
      }

      id = R.id.radio_english;
      RadioButton radioEnglish = ViewBindings.findChildViewById(rootView, id);
      if (radioEnglish == null) {
        break missingId;
      }

      id = R.id.radio_system;
      RadioButton radioSystem = ViewBindings.findChildViewById(rootView, id);
      if (radioSystem == null) {
        break missingId;
      }

      return new ActivityLanguageSettingsBinding((LinearLayout) rootView, btnApply,
          languageRadioGroup, radioChinese, radioEnglish, radioSystem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
