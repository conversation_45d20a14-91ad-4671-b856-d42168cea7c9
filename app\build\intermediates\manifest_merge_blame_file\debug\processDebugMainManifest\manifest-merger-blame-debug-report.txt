1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:5-75
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:22-72
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-77
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:5-79
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-81
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-85
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-82
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-80
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-81
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-75
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-76
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-73
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-79
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-76
33    <uses-permission
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-31:31
34        android:name="android.permission.NEARBY_WIFI_DEVICES"
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:9-62
35        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:9-55
36    <uses-permission android:name="android.permission.BLUETOOTH" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-68
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-65
37    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-74
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-71
38    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-76
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-73
39    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
40    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-77
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-74
41    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-80
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-77
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:5-85
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 精确闹钟权限，用于MQTT服务 -->
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-73
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:22-70
44    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:5-79
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:22-76
45    <!-- 前台服务位置权限，Android 14及以上需要 -->
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:5-86
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:22-83
47    <!-- 前台服务数据同步权限，Android 14及以上需要 -->
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
48-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:5-87
48-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:22-84
49    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
49-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
49-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
50    <uses-feature
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
51        android:name="android.hardware.camera"
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
52        android:required="false" />
52-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
54        android:name="android.hardware.camera.front"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
55        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
56    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
57    <uses-feature
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
58        android:name="android.hardware.camera.autofocus"
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
59        android:required="false" />
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
60    <uses-feature
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
61        android:name="android.hardware.camera.flash"
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
62        android:required="false" />
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
63    <uses-feature
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
64        android:name="android.hardware.screen.landscape"
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
65        android:required="false" />
65-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
66    <uses-feature
66-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
67        android:name="android.hardware.wifi"
67-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
68        android:required="false" />
68-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
69
70    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
70-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
70-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
71
72    <permission
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
73        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
73-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
74        android:protectionLevel="signature" />
74-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
75
76    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
76-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
76-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
77
78    <application
78-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:5-163:19
79        android:name="com.srthinker.bbnice.App"
79-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-28
80        android:allowBackup="true"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-35
81        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
81-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
82        android:debuggable="true"
83        android:extractNativeLibs="false"
84        android:icon="@mipmap/ic_launcher"
84-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-43
85        android:label="@string/app_name"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:9-41
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-54
87        android:supportsRtl="true"
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:9-35
88        android:testOnly="true"
89        android:theme="@style/Theme.AppCompat.NoActionBar"
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:9-59
90        android:usesCleartextTraffic="true" >
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-44
91        <activity
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:9-57:40
92            android:name="com.srthinker.bbnice.chat.VideoChatActivity"
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-51
93            android:exported="false" />
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:13-37
94        <activity
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:9-60:40
95            android:name="com.srthinker.bbnice.setting.PrivateActivity"
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-52
96            android:exported="false" />
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:13-37
97        <activity
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:9-63:40
98            android:name="com.srthinker.bbnice.setting.AboutActivity"
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-50
99            android:exported="false" />
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:13-37
100        <activity
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:9-66:40
101            android:name="com.srthinker.bbnice.setting.VoiceActivity"
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-50
102            android:exported="false" />
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:13-37
103        <activity
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:9-69:40
104            android:name="com.srthinker.bbnice.setting.DisplayActivity"
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-52
105            android:exported="false" />
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:13-37
106        <activity
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:9-72:40
107            android:name="com.srthinker.bbnice.setting.LocationActivity"
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-53
108            android:exported="false" />
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:13-37
109        <activity
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:9-75:40
110            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-54
111            android:exported="false" />
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:13-37
112        <activity
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:9-78:40
113            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-58
114            android:exported="false" />
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-37
115        <activity
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:9-81:40
116            android:name="com.srthinker.bbnice.setting.WifiActivity"
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-49
117            android:exported="false" />
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:13-37
118        <activity
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:9-84:40
119            android:name="com.srthinker.bbnice.setting.SettingActivity"
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-52
120            android:exported="false" />
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:13-37
121        <activity
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:9-87:40
122            android:name="com.srthinker.bbnice.call.CallActivity"
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-46
123            android:exported="false" />
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:13-37
124        <activity
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:9-90:40
125            android:name="com.srthinker.bbnice.capture.CaptureActivity"
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-52
126            android:exported="false" />
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:13-37
127        <activity
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:9-93:40
128            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:13-60
129            android:exported="false" />
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:13-37
130        <activity
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:9-96:40
131            android:name="com.srthinker.bbnice.learn.LearnActivity"
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:13-48
132            android:exported="false" />
132-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:13-37
133        <activity
133-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:9-107:20
134            android:name="com.srthinker.bbnice.home.HomeActivity"
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:13-46
135            android:exported="false" >
135-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:99:13-37
136
137            <!-- <intent-filter> -->
138            <!-- <action android:name="android.intent.action.MAIN" /> -->
139
140
141            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
142            <!-- </intent-filter> -->
143        </activity>
144        <activity
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:9-110:40
145            android:name="com.srthinker.bbnice.chat.ChatActivity"
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:13-46
146            android:exported="false" />
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-37
147        <activity
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:9-120:20
148            android:name="com.srthinker.bbnice.SplashActivity"
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-43
149            android:exported="true"
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:13-36
150            android:theme="@style/Theme.AppCompat.NoActionBar" >
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:13-63
151            <intent-filter>
151-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:115:13-119:29
152                <action android:name="android.intent.action.MAIN" />
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:17-69
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:25-66
153
154                <category android:name="android.intent.category.LAUNCHER" />
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:17-77
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:27-74
155            </intent-filter>
156        </activity>
157        <activity
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:9-124:48
158            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-68
159            android:exported="false"
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-37
160            android:label="@string/register" />
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-45
161        <activity
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:9-128:57
162            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:13-61
163            android:exported="false"
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-37
164            android:label="@string/language_settings" /> <!-- MQTT服务 - 使用HiveMQ客户端，兼容Android 14+ -->
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-54
165        <service
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:9-132:56
166            android:name="com.srthinker.bbnice.mqtt.HiveMqttService"
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-49
167            android:enabled="true"
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:13-35
168            android:foregroundServiceType="dataSync" />
168-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:132:13-53
169        <!--
170        备用的Eclipse Paho MQTT服务（已禁用）
171        <service android:name="org.eclipse.paho.android.service.MqttService" />
172        <service
173            android:name=".mqtt.MqttService"
174            android:enabled="false"
175            android:foregroundServiceType="dataSync" />
176        -->
177        <!-- 位置服务 -->
178        <service
178-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:140:9-144:58
179            android:name="com.srthinker.bbnice.location.LocationService"
179-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:141:13-53
180            android:enabled="true"
180-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:142:13-35
181            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
181-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:143:13-53
182        <activity
182-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:145:9-157:20
183            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
183-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:146:13-56
184            android:exported="true"
184-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:147:13-36
185            android:label="Repository测试"
185-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:148:13-41
186            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
186-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:149:13-71
187
188            <!-- <intent-filter> -->
189            <!-- <action android:name="android.intent.action.MAIN" /> -->
190
191
192            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
193            <!-- </intent-filter> -->
194        </activity> <!-- 相册Activity -->
195        <activity
195-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:158:9-162:66
196            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
196-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:159:13-52
197            android:exported="false"
197-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:160:13-37
198            android:label="@string/gallery"
198-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:161:13-44
199            android:theme="@style/Theme.AppCompat.NoActionBar" />
199-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:162:13-63
200
201        <service
201-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
202            android:name="androidx.camera.core.impl.MetadataHolderService"
202-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
203            android:enabled="false"
203-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
204            android:exported="false" >
204-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
205            <meta-data
205-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
206                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
206-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
207                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
207-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
208        </service>
209
210        <activity
210-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
211            android:name="com.journeyapps.barcodescanner.CaptureActivity"
211-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
212            android:clearTaskOnLaunch="true"
212-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
213            android:screenOrientation="sensorLandscape"
213-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
214            android:stateNotNeeded="true"
214-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
215            android:theme="@style/zxing_CaptureTheme"
215-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
216            android:windowSoftInputMode="stateAlwaysHidden" />
216-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
217
218        <provider
218-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
219            android:name="androidx.startup.InitializationProvider"
219-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
220            android:authorities="com.srthinker.bbnice.androidx-startup"
220-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
221            android:exported="false" >
221-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
222            <meta-data
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
223                android:name="androidx.work.WorkManagerInitializer"
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
224                android:value="androidx.startup" />
224-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
225            <meta-data
225-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.emoji2.text.EmojiCompatInitializer"
226-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
227                android:value="androidx.startup" />
227-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
228            <meta-data
228-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
229                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
229-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
230                android:value="androidx.startup" />
230-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
231            <meta-data
231-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
232                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
232-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
233                android:value="androidx.startup" />
233-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
234        </provider>
235
236        <service
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
237            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
239            android:enabled="@bool/enable_system_alarm_service_default"
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
240            android:exported="false" />
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
241        <service
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
242            android:name="androidx.work.impl.background.systemjob.SystemJobService"
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
244            android:enabled="@bool/enable_system_job_service_default"
244-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
245            android:exported="true"
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
246            android:permission="android.permission.BIND_JOB_SERVICE" />
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
247        <service
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
248            android:name="androidx.work.impl.foreground.SystemForegroundService"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
250            android:enabled="@bool/enable_system_foreground_service_default"
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
251            android:exported="false" />
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
252
253        <receiver
253-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
254            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
255            android:directBootAware="false"
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
256            android:enabled="true"
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
257            android:exported="false" />
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
258        <receiver
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
259            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
261            android:enabled="false"
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
262            android:exported="false" >
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
263            <intent-filter>
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
264                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
265                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
266            </intent-filter>
267        </receiver>
268        <receiver
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
269            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
270            android:directBootAware="false"
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
271            android:enabled="false"
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
272            android:exported="false" >
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
273            <intent-filter>
273-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
274                <action android:name="android.intent.action.BATTERY_OKAY" />
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
275                <action android:name="android.intent.action.BATTERY_LOW" />
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
276            </intent-filter>
277        </receiver>
278        <receiver
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
279            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
280            android:directBootAware="false"
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
281            android:enabled="false"
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
282            android:exported="false" >
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
283            <intent-filter>
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
284                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
285                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
286            </intent-filter>
287        </receiver>
288        <receiver
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
289            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
290            android:directBootAware="false"
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
291            android:enabled="false"
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
292            android:exported="false" >
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
293            <intent-filter>
293-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
294                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
295            </intent-filter>
296        </receiver>
297        <receiver
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
298            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
299            android:directBootAware="false"
299-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
300            android:enabled="false"
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
301            android:exported="false" >
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
302            <intent-filter>
302-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
303                <action android:name="android.intent.action.BOOT_COMPLETED" />
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
304                <action android:name="android.intent.action.TIME_SET" />
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
305                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
309            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
309-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
310            android:directBootAware="false"
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
311            android:enabled="@bool/enable_system_alarm_service_default"
311-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
312            android:exported="false" >
312-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
313            <intent-filter>
313-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
314                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
314-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
314-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
315            </intent-filter>
316        </receiver>
317        <receiver
317-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
318            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
318-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
319            android:directBootAware="false"
319-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
320            android:enabled="true"
320-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
321            android:exported="true"
321-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
322            android:permission="android.permission.DUMP" >
322-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
323            <intent-filter>
323-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
324                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
324-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
324-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
325            </intent-filter>
326        </receiver>
327
328        <service
328-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
329            android:name="androidx.room.MultiInstanceInvalidationService"
329-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
330            android:directBootAware="true"
330-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
331            android:exported="false" />
331-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
332
333        <uses-library
333-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
334            android:name="androidx.window.extensions"
334-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
335            android:required="false" />
335-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
336        <uses-library
336-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
337            android:name="androidx.window.sidecar"
337-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
338            android:required="false" />
338-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
339
340        <receiver
340-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
341            android:name="androidx.profileinstaller.ProfileInstallReceiver"
341-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
342            android:directBootAware="false"
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
343            android:enabled="true"
343-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
344            android:exported="true"
344-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
345            android:permission="android.permission.DUMP" >
345-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
346            <intent-filter>
346-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
347                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
347-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
347-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
348            </intent-filter>
349            <intent-filter>
349-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
350                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
350-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
350-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
351            </intent-filter>
352            <intent-filter>
352-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
353                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
353-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
353-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
354            </intent-filter>
355            <intent-filter>
355-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
356                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
356-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
356-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
357            </intent-filter>
358        </receiver>
359    </application>
360
361</manifest>
