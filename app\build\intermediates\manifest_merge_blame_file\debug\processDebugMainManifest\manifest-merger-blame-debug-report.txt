1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:5-75
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:22-72
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-77
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:5-79
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-81
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-85
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-82
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-80
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-81
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-75
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-76
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-73
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-79
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-76
33    <uses-permission
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-31:31
34        android:name="android.permission.NEARBY_WIFI_DEVICES"
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:9-62
35        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:9-55
36    <uses-permission android:name="android.permission.BLUETOOTH" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-68
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-65
37    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-74
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-71
38    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-76
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-73
39    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
40    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-77
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-74
41    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-80
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-77
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:5-85
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 精确闹钟权限，用于MQTT服务 -->
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-73
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:22-70
44    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:5-79
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:22-76
45    <!-- 前台服务位置权限，Android 14及以上需要 -->
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:5-86
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:22-83
47    <!-- 前台服务数据同步权限，Android 14及以上需要 -->
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
48-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:5-87
48-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:22-84
49    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
49-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
49-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
50    <uses-feature
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
51        android:name="android.hardware.camera"
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
52        android:required="false" />
52-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
54        android:name="android.hardware.camera.front"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
55        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
56    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
57    <uses-feature
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
58        android:name="android.hardware.camera.autofocus"
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
59        android:required="false" />
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
60    <uses-feature
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
61        android:name="android.hardware.camera.flash"
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
62        android:required="false" />
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
63    <uses-feature
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
64        android:name="android.hardware.screen.landscape"
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
65        android:required="false" />
65-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
66    <uses-feature
66-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
67        android:name="android.hardware.wifi"
67-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
68        android:required="false" />
68-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
69
70    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
70-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
70-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
71
72    <permission
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
73        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
73-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
74        android:protectionLevel="signature" />
74-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
75
76    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
76-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
76-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
77
78    <application
78-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:5-157:19
79        android:name="com.srthinker.bbnice.App"
79-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-28
80        android:allowBackup="true"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-35
81        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
81-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
82        android:debuggable="true"
83        android:extractNativeLibs="false"
84        android:icon="@mipmap/ic_launcher"
84-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-43
85        android:label="@string/app_name"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:9-41
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-54
87        android:supportsRtl="true"
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:9-35
88        android:theme="@style/Theme.AppCompat.NoActionBar"
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:9-59
89        android:usesCleartextTraffic="true" >
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-44
90        <activity
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:9-57:40
91            android:name="com.srthinker.bbnice.chat.VideoChatActivity"
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-51
92            android:exported="false" />
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:13-37
93        <activity
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:9-60:40
94            android:name="com.srthinker.bbnice.setting.PrivateActivity"
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-52
95            android:exported="false" />
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:13-37
96        <activity
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:9-63:40
97            android:name="com.srthinker.bbnice.setting.AboutActivity"
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-50
98            android:exported="false" />
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:13-37
99        <activity
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:9-66:40
100            android:name="com.srthinker.bbnice.setting.VoiceActivity"
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-50
101            android:exported="false" />
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:13-37
102        <activity
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:9-69:40
103            android:name="com.srthinker.bbnice.setting.DisplayActivity"
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-52
104            android:exported="false" />
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:13-37
105        <activity
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:9-72:40
106            android:name="com.srthinker.bbnice.setting.LocationActivity"
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-53
107            android:exported="false" />
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:13-37
108        <activity
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:9-75:40
109            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-54
110            android:exported="false" />
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:13-37
111        <activity
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:9-78:40
112            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-58
113            android:exported="false" />
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-37
114        <activity
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:9-81:40
115            android:name="com.srthinker.bbnice.setting.WifiActivity"
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-49
116            android:exported="false" />
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:13-37
117        <activity
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:9-84:40
118            android:name="com.srthinker.bbnice.setting.SettingActivity"
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-52
119            android:exported="false" />
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:13-37
120        <activity
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:9-87:40
121            android:name="com.srthinker.bbnice.call.CallActivity"
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-46
122            android:exported="false" />
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:13-37
123        <activity
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:9-90:40
124            android:name="com.srthinker.bbnice.capture.CaptureActivity"
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-52
125            android:exported="false" />
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:13-37
126        <activity
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:9-93:40
127            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:13-60
128            android:exported="false" />
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:13-37
129        <activity
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:9-96:40
130            android:name="com.srthinker.bbnice.learn.LearnActivity"
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:13-48
131            android:exported="false" />
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:13-37
132        <activity
132-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:9-107:20
133            android:name="com.srthinker.bbnice.home.HomeActivity"
133-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:13-46
134            android:exported="false" >
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:99:13-37
135
136            <!-- <intent-filter> -->
137            <!-- <action android:name="android.intent.action.MAIN" /> -->
138
139
140            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
141            <!-- </intent-filter> -->
142        </activity>
143        <activity
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:9-110:40
144            android:name="com.srthinker.bbnice.chat.ChatActivity"
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:13-46
145            android:exported="false" />
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-37
146        <activity
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:9-120:20
147            android:name="com.srthinker.bbnice.SplashActivity"
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-43
148            android:exported="true"
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:13-36
149            android:theme="@style/Theme.AppCompat.NoActionBar" >
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:13-63
150            <intent-filter>
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:115:13-119:29
151                <action android:name="android.intent.action.MAIN" />
151-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:17-69
151-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:25-66
152
153                <category android:name="android.intent.category.LAUNCHER" />
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:17-77
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:27-74
154            </intent-filter>
155        </activity>
156        <activity
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:9-124:48
157            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-68
158            android:exported="false"
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-37
159            android:label="@string/register" />
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-45
160        <activity
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:9-128:57
161            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:13-61
162            android:exported="false"
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-37
163            android:label="@string/language_settings" /> <!-- MQTT服务 - 使用HiveMQ客户端，兼容Android 14+ -->
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-54
164        <service
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:9-132:56
165            android:name="com.srthinker.bbnice.mqtt.HiveMqttService"
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-49
166            android:enabled="true"
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:13-35
167            android:foregroundServiceType="dataSync" />
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:132:13-53
168        --> 
169        <!-- 位置服务 -->
170        <service
170-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:134:9-138:58
171            android:name="com.srthinker.bbnice.location.LocationService"
171-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:135:13-53
172            android:enabled="true"
172-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:136:13-35
173            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
173-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:13-53
174        <activity
174-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:139:9-151:20
175            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
175-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:140:13-56
176            android:exported="true"
176-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:141:13-36
177            android:label="Repository测试"
177-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:142:13-41
178            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
178-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:143:13-71
179
180            <!-- <intent-filter> -->
181            <!-- <action android:name="android.intent.action.MAIN" /> -->
182
183
184            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
185            <!-- </intent-filter> -->
186        </activity> <!-- 相册Activity -->
187        <activity
187-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:152:9-156:66
188            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
188-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:153:13-52
189            android:exported="false"
189-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:154:13-37
190            android:label="@string/gallery"
190-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:155:13-44
191            android:theme="@style/Theme.AppCompat.NoActionBar" />
191-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:156:13-63
192
193        <service
193-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
194            android:name="androidx.camera.core.impl.MetadataHolderService"
194-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
195            android:enabled="false"
195-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
196            android:exported="false" >
196-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
197            <meta-data
197-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
198                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
198-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
199                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
199-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
200        </service>
201
202        <activity
202-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
203            android:name="com.journeyapps.barcodescanner.CaptureActivity"
203-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
204            android:clearTaskOnLaunch="true"
204-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
205            android:screenOrientation="sensorLandscape"
205-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
206            android:stateNotNeeded="true"
206-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
207            android:theme="@style/zxing_CaptureTheme"
207-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
208            android:windowSoftInputMode="stateAlwaysHidden" />
208-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
209
210        <provider
210-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
211            android:name="androidx.startup.InitializationProvider"
211-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
212            android:authorities="com.srthinker.bbnice.androidx-startup"
212-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
214            <meta-data
214-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
215                android:name="androidx.work.WorkManagerInitializer"
215-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
216                android:value="androidx.startup" />
216-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
217            <meta-data
217-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
218                android:name="androidx.emoji2.text.EmojiCompatInitializer"
218-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
219                android:value="androidx.startup" />
219-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
220            <meta-data
220-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
221-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
222                android:value="androidx.startup" />
222-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
223            <meta-data
223-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
224                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
224-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
225                android:value="androidx.startup" />
225-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
226        </provider>
227
228        <service
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
229            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
231            android:enabled="@bool/enable_system_alarm_service_default"
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
233        <service
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
234            android:name="androidx.work.impl.background.systemjob.SystemJobService"
234-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
236            android:enabled="@bool/enable_system_job_service_default"
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
237            android:exported="true"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
238            android:permission="android.permission.BIND_JOB_SERVICE" />
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
239        <service
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
240            android:name="androidx.work.impl.foreground.SystemForegroundService"
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
242            android:enabled="@bool/enable_system_foreground_service_default"
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
243            android:exported="false" />
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
244
245        <receiver
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
246            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
248            android:enabled="true"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
249            android:exported="false" />
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
250        <receiver
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
251            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
252            android:directBootAware="false"
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
253            android:enabled="false"
253-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
254            android:exported="false" >
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
255            <intent-filter>
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
256                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
257                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
258            </intent-filter>
259        </receiver>
260        <receiver
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
261            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
263            android:enabled="false"
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
264            android:exported="false" >
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
265            <intent-filter>
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
266                <action android:name="android.intent.action.BATTERY_OKAY" />
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
267                <action android:name="android.intent.action.BATTERY_LOW" />
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
268            </intent-filter>
269        </receiver>
270        <receiver
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
276                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
277                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
286                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
290            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
295                <action android:name="android.intent.action.BOOT_COMPLETED" />
295-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
295-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
296                <action android:name="android.intent.action.TIME_SET" />
296-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
296-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
297                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
301            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
303            android:enabled="@bool/enable_system_alarm_service_default"
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
305            <intent-filter>
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
306                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
310            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
312            android:enabled="true"
312-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
313            android:exported="true"
313-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
314            android:permission="android.permission.DUMP" >
314-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
315            <intent-filter>
315-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
316                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
316-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
316-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
317            </intent-filter>
318        </receiver>
319
320        <service
320-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
321            android:name="androidx.room.MultiInstanceInvalidationService"
321-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
322            android:directBootAware="true"
322-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
323            android:exported="false" />
323-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
324
325        <uses-library
325-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
326            android:name="androidx.window.extensions"
326-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
327            android:required="false" />
327-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
328        <uses-library
328-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
329            android:name="androidx.window.sidecar"
329-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
330            android:required="false" />
330-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
331
332        <receiver
332-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
333            android:name="androidx.profileinstaller.ProfileInstallReceiver"
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
334            android:directBootAware="false"
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
335            android:enabled="true"
335-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
336            android:exported="true"
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
337            android:permission="android.permission.DUMP" >
337-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
339                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
342                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
343            </intent-filter>
344            <intent-filter>
344-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
345                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
345-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
345-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
346            </intent-filter>
347            <intent-filter>
347-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
348                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
348-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
348-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
349            </intent-filter>
350        </receiver>
351    </application>
352
353</manifest>
