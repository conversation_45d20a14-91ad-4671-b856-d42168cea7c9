package com.srthinker.bbnice.utils;

import android.graphics.Bitmap;
import android.graphics.Color;

public class BlurUtil {

    public static Bitmap blur(Bitmap originalBitmap, float radius, float scaleFactor) {
        // 检查输入参数
        if (originalBitmap == null) {
            return null;
        }

        if (originalBitmap.isRecycled()) {
            return null;
        }

        // 缩小图片尺寸
        int scaledWidth = (int) (originalBitmap.getWidth() * scaleFactor);
        int scaledHeight = (int) (originalBitmap.getHeight() * scaleFactor);

        // 确保尺寸有效
        if (scaledWidth <= 0 || scaledHeight <= 0) {
            return null;
        }

        Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, scaledWidth, scaledHeight, false);

        // 应用高斯模糊
        Bitmap blurredBitmap = fastBlur(scaledBitmap, (int) radius);

        // 放大到原始尺寸（可选）
        Bitmap finalBitmap = Bitmap.createScaledBitmap(blurredBitmap,
                originalBitmap.getWidth(), originalBitmap.getHeight(), true);

        // 回收临时 Bitmap（谨慎操作，避免原始 Bitmap 被回收）
        if (scaledBitmap != null && !scaledBitmap.isRecycled()) {
            scaledBitmap.recycle();
        }
        if (blurredBitmap != null && !blurredBitmap.isRecycled()) {
            blurredBitmap.recycle();
        }

        return finalBitmap;
    }

    private static Bitmap fastBlur(Bitmap sentBitmap, int radius) {
        if (sentBitmap == null || sentBitmap.isRecycled()) {
            return null;
        }

        if (radius < 1) return sentBitmap;

        int width = sentBitmap.getWidth();
        int height = sentBitmap.getHeight();
        int[] pixels = new int[width * height];
        sentBitmap.getPixels(pixels, 0, width, 0, 0, width, height);

        // 计算高斯核
        int[] gaussianKernel = createGaussianKernel(radius);

        // 水平模糊
        applyHorizontalBlur(pixels, width, height, gaussianKernel, radius);

        // 垂直模糊
        applyVerticalBlur(pixels, width, height, gaussianKernel, radius);

        Bitmap result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        result.setPixels(pixels, 0, width, 0, 0, width, height);
        return result;
    }

    private static int[] createGaussianKernel(int radius) {
        int size = 2 * radius + 1;
        int[] kernel = new int[size];
        int sum = 0;

        // 生成一维高斯核
        for (int i = -radius; i <= radius; i++) {
            float weight = (float) Math.exp(-i * i / (2 * radius * radius));
            kernel[i + radius] = (int) (100 * weight);
            sum += kernel[i + radius];
        }

        // 归一化
        for (int i = 0; i < kernel.length; i++) {
            kernel[i] = kernel[i] * 100 / sum;
        }

        return kernel;
    }

    private static void applyHorizontalBlur(int[] pixels, int width, int height,
                                            int[] kernel, int radius) {
        int[] tempPixels = new int[pixels.length];
        int kernelSize = kernel.length;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int r = 0, g = 0, b = 0;

                // 应用水平核
                for (int k = -radius; k <= radius; k++) {
                    int pos = x + k;
                    if (pos < 0) pos = 0;
                    if (pos >= width) pos = width - 1;

                    int pixel = pixels[y * width + pos];
                    int weight = kernel[k + radius];

                    r += Color.red(pixel) * weight;
                    g += Color.green(pixel) * weight;
                    b += Color.blue(pixel) * weight;
                }

                tempPixels[y * width + x] = Color.rgb(r / 100, g / 100, b / 100);
            }
        }

        System.arraycopy(tempPixels, 0, pixels, 0, pixels.length);
    }

    private static void applyVerticalBlur(int[] pixels, int width, int height,
                                          int[] kernel, int radius) {
        int[] tempPixels = new int[pixels.length];
        int kernelSize = kernel.length;

        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                int r = 0, g = 0, b = 0;

                // 应用垂直核
                for (int k = -radius; k <= radius; k++) {
                    int pos = y + k;
                    if (pos < 0) pos = 0;
                    if (pos >= height) pos = height - 1;

                    int pixel = pixels[pos * width + x];
                    int weight = kernel[k + radius];

                    r += Color.red(pixel) * weight;
                    g += Color.green(pixel) * weight;
                    b += Color.blue(pixel) * weight;
                }

                tempPixels[y * width + x] = Color.rgb(r / 100, g / 100, b / 100);
            }
        }

        System.arraycopy(tempPixels, 0, pixels, 0, pixels.length);
    }
}