package com.srthinker.bbnice.gallery;

import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

/**
 * 媒体文件数据模型类，用于表示相册中的照片或视频
 */
public class MediaItem implements Parcelable {
    public static final int TYPE_IMAGE = 1;
    public static final int TYPE_VIDEO = 2;

    private final long id;
    private final Uri uri;
    private final String displayName;
    private final String path;
    private final long size;
    private final long dateAdded;
    private final int mediaType;
    private final long duration; // 仅视频有效，单位为毫秒
    private boolean isSelected;

    public MediaItem(long id, Uri uri, String displayName, String path, long size, long dateAdded, int mediaType, long duration) {
        this.id = id;
        this.uri = uri;
        this.displayName = displayName;
        this.path = path;
        this.size = size;
        this.dateAdded = dateAdded;
        this.mediaType = mediaType;
        this.duration = duration;
        this.isSelected = false;
    }

    protected MediaItem(Parcel in) {
        id = in.readLong();
        uri = in.readParcelable(Uri.class.getClassLoader());
        displayName = in.readString();
        path = in.readString();
        size = in.readLong();
        dateAdded = in.readLong();
        mediaType = in.readInt();
        duration = in.readLong();
        isSelected = in.readByte() != 0;
    }

    public static final Creator<MediaItem> CREATOR = new Creator<MediaItem>() {
        @Override
        public MediaItem createFromParcel(Parcel in) {
            return new MediaItem(in);
        }

        @Override
        public MediaItem[] newArray(int size) {
            return new MediaItem[size];
        }
    };

    public long getId() {
        return id;
    }

    public Uri getUri() {
        return uri;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getPath() {
        return path;
    }

    public long getSize() {
        return size;
    }

    public long getDateAdded() {
        return dateAdded;
    }

    public int getMediaType() {
        return mediaType;
    }

    public long getDuration() {
        return duration;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isImage() {
        return mediaType == TYPE_IMAGE;
    }

    public boolean isVideo() {
        return mediaType == TYPE_VIDEO;
    }

    /**
     * 获取格式化的视频时长
     * @return 格式化的时长字符串，如 "01:23"
     */
    public String getFormattedDuration() {
        if (mediaType != TYPE_VIDEO) {
            return "";
        }
        
        long seconds = duration / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        return String.format("%02d:%02d", minutes, seconds);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeParcelable(uri, flags);
        dest.writeString(displayName);
        dest.writeString(path);
        dest.writeLong(size);
        dest.writeLong(dateAdded);
        dest.writeInt(mediaType);
        dest.writeLong(duration);
        dest.writeByte((byte) (isSelected ? 1 : 0));
    }
}
