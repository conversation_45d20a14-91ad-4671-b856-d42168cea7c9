// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.VideoView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentVideoDetailBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnPlayPause;

  @NonNull
  public final LinearLayout controlPanel;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final TextView tvDuration;

  @NonNull
  public final VideoView videoView;

  private FragmentVideoDetailBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton btnBack, @NonNull ImageButton btnPlayPause,
      @NonNull LinearLayout controlPanel, @NonNull ProgressBar progressBar,
      @NonNull SeekBar seekBar, @NonNull TextView tvDuration, @NonNull VideoView videoView) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnPlayPause = btnPlayPause;
    this.controlPanel = controlPanel;
    this.progressBar = progressBar;
    this.seekBar = seekBar;
    this.tvDuration = tvDuration;
    this.videoView = videoView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentVideoDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentVideoDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_video_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentVideoDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_play_pause;
      ImageButton btnPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (btnPlayPause == null) {
        break missingId;
      }

      id = R.id.control_panel;
      LinearLayout controlPanel = ViewBindings.findChildViewById(rootView, id);
      if (controlPanel == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.seek_bar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.tv_duration;
      TextView tvDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvDuration == null) {
        break missingId;
      }

      id = R.id.video_view;
      VideoView videoView = ViewBindings.findChildViewById(rootView, id);
      if (videoView == null) {
        break missingId;
      }

      return new FragmentVideoDetailBinding((ConstraintLayout) rootView, btnBack, btnPlayPause,
          controlPanel, progressBar, seekBar, tvDuration, videoView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
