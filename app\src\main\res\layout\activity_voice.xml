<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".setting.VoiceActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:title="@string/voice_settings" />

    <!-- 静音模式开关 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mute_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <TextView
            android:id="@+id/tv_mute_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mute_mode"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_mute"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray"
        app:layout_constraintTop_toBottomOf="@+id/mute_container" />

    <!-- 媒体音量控制 -->
    <TextView
        android:id="@+id/tv_media_volume_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="@string/media_volume"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/media_volume_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_media_volume_label">

        <ImageButton
            android:id="@+id/btn_media_volume_down"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/volume_down"
            android:src="@android:drawable/ic_media_previous"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <SeekBar
            android:id="@+id/seekbar_media_volume"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btn_media_volume_up"
            app:layout_constraintStart_toEndOf="@+id/btn_media_volume_down"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/btn_media_volume_up"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/volume_up"
            android:src="@android:drawable/ic_media_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_media_volume_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp"
        android:text="@string/volume_level"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@+id/media_volume_container" />

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@android:color/darker_gray"
        app:layout_constraintTop_toBottomOf="@+id/tv_media_volume_level" />

    <!-- 铃声音量控制 -->
    <TextView
        android:id="@+id/tv_ring_volume_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="@string/ring_volume"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/divider2" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ring_volume_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_ring_volume_label">

        <ImageButton
            android:id="@+id/btn_ring_volume_down"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/volume_down"
            android:src="@android:drawable/ic_media_previous"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <SeekBar
            android:id="@+id/seekbar_ring_volume"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btn_ring_volume_up"
            app:layout_constraintStart_toEndOf="@+id/btn_ring_volume_down"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/btn_ring_volume_up"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/volume_up"
            android:src="@android:drawable/ic_media_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_ring_volume_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp"
        android:text="@string/volume_level"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@+id/ring_volume_container" />

    <TextView
        android:id="@+id/tv_permission_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="#FFECB3"
        android:padding="16dp"
        android:text="@string/do_not_disturb_permission_required"
        android:textColor="#B71C1C"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tv_ring_volume_level" />

</androidx.constraintlayout.widget.ConstraintLayout>