package com.srthinker.bbnice.db;

import android.content.Context;
import android.util.Log;

import com.srthinker.bbnice.chat.ChatMessage;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 聊天记录仓库类，使用Room实现
 */
public class ChatRepository {
    private static final String TAG = "ChatRepository";
    private static final int PAGE_SIZE = 30; // 每页加载的消息数量
    
    private final ChatDatabase database;
    private final ChatMessageDao chatMessageDao;
    private final Executor executor;
    
    private static ChatRepository instance;
    
    private ChatRepository(Context context) {
        database = ChatDatabase.getInstance(context);
        chatMessageDao = database.chatMessageDao();
        executor = Executors.newSingleThreadExecutor();
    }
    
    public static synchronized ChatRepository getInstance(Context context) {
        if (instance == null) {
            instance = new ChatRepository(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 保存聊天消息
     * @param message 聊天消息
     * @return 是否保存成功
     */
    public boolean saveMessage(ChatMessage message) {
        try {
            ChatMessageEntity entity = new ChatMessageEntity(message);
            long id = chatMessageDao.insert(entity);
            return id != -1;
        } catch (Exception e) {
            Log.e(TAG, "Error saving message: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 异步保存聊天消息
     * @param message 聊天消息
     */
    public void saveMessageAsync(ChatMessage message) {
        executor.execute(() -> {
            saveMessage(message);
        });
    }
    
    /**
     * 更新消息内容
     * @param messageId 消息ID
     * @param content 新内容
     * @return 是否更新成功
     */
    public boolean updateMessageContent(String messageId, String content) {
        try {
            int count = chatMessageDao.updateContent(messageId, content);
            return count > 0;
        } catch (Exception e) {
            Log.e(TAG, "Error updating message content: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 异步更新消息内容
     * @param messageId 消息ID
     * @param content 新内容
     */
    public void updateMessageContentAsync(String messageId, String content) {
        executor.execute(() -> {
            updateMessageContent(messageId, content);
        });
    }
    
    /**
     * 标记消息为已上传
     * @param messageId 消息ID
     * @return 是否更新成功
     */
    public boolean markMessageAsUploaded(String messageId) {
        try {
            int count = chatMessageDao.markAsUploaded(messageId);
            return count > 0;
        } catch (Exception e) {
            Log.e(TAG, "Error marking message as uploaded: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 异步标记消息为已上传
     * @param messageId 消息ID
     */
    public void markMessageAsUploadedAsync(String messageId) {
        executor.execute(() -> {
            markMessageAsUploaded(messageId);
        });
    }
    
    /**
     * 获取指定房间类型的最新消息
     * @param roomType 聊天类型
     * @param limit 限制数量
     * @return 消息列表
     */
    public List<ChatMessage> getLatestMessages(RoomType roomType, int limit) {
        try {
            List<ChatMessageEntity> entities = chatMessageDao.getLatestMessages(roomType.getValue(), limit);
            List<ChatMessage> messages = new ArrayList<>();
            
            for (ChatMessageEntity entity : entities) {
                messages.add(entity.toChatMessage());
            }
            
            return messages;
        } catch (Exception e) {
            Log.e(TAG, "Error getting latest messages: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 分页获取消息
     * @param roomType 房间类型
     * @param page 页码（从0开始）
     * @return 消息列表
     */
    public List<ChatMessage> getMessagesByPage(RoomType roomType, int page) {
        try {
            int offset = page * PAGE_SIZE;
            List<ChatMessageEntity> entities = chatMessageDao.getMessagesByPage(roomType.getValue(), PAGE_SIZE, offset);
            List<ChatMessage> messages = new ArrayList<>();
            
            for (ChatMessageEntity entity : entities) {
                messages.add(entity.toChatMessage());
            }
            
            return messages;
        } catch (Exception e) {
            Log.e(TAG, "Error getting messages by page: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取未上传的消息
     * @param limit 限制数量
     * @return 未上传的消息列表
     */
    public List<ChatMessage> getNotUploadedMessages(int limit) {
        try {
            List<ChatMessageEntity> entities = chatMessageDao.getNotUploadedMessages(limit);
            List<ChatMessage> messages = new ArrayList<>();
            
            for (ChatMessageEntity entity : entities) {
                messages.add(entity.toChatMessage());
            }
            
            return messages;
        } catch (Exception e) {
            Log.e(TAG, "Error getting not uploaded messages: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取昨天的消息
     * @return 昨天的消息列表
     */
    public List<ChatMessage> getYesterdayMessages() {
        try {
            // 计算昨天的开始和结束时间戳
            long now = System.currentTimeMillis();
            long oneDayMillis = 24 * 60 * 60 * 1000;
            long yesterdayStart = now - (now % oneDayMillis) - oneDayMillis;
            long yesterdayEnd = yesterdayStart + oneDayMillis;
            
            List<ChatMessageEntity> entities = chatMessageDao.getMessagesByTimeRange(yesterdayStart, yesterdayEnd);
            List<ChatMessage> messages = new ArrayList<>();
            
            for (ChatMessageEntity entity : entities) {
                messages.add(entity.toChatMessage());
            }
            
            return messages;
        } catch (Exception e) {
            Log.e(TAG, "Error getting yesterday messages: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}
