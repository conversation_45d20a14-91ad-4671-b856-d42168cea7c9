package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * RTC启动响应类
 * 用于表示RTC启动API的响应
 */
public class RTCStartResponse extends BaseResponse {
    // 响应数据
    private final RTCStartResponseData data;

    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public RTCStartResponse(ApiResponse<RTCStartResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public RTCStartResponseData getData() {
        return data;
    }


    @Override
    public String toString() {
        return "RTCStartResponse{" +
                "data=" + data +
                '}';
    }

    /**
     * RTC启动响应数据类
     */
    public static class RTCStartResponseData {
        // 状态
        private String status;
        
        // 消息
        private String message;

        // 任务ID，用于后续停止AI代理
        private String task_id;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getTask_id() {
            return task_id;
        }

        public void setTask_id(String task_id) {
            this.task_id = task_id;
        }

        @Override
        public String toString() {
            return "RTCStartResponseData{" +
                    "status='" + status + '\'' +
                    ", message='" + message + '\'' +
                    ", task_id='" + task_id + '\'' +
                    '}';
        }
    }
}
