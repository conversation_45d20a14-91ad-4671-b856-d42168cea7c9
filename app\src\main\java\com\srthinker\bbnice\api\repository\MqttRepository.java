package com.srthinker.bbnice.api.repository;

import androidx.lifecycle.LiveData;

import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.core.Repository;
import com.srthinker.bbnice.core.Result;

/**
 * MQTT相关的Repository接口
 */
public interface MqttRepository extends Repository {
    
    /**
     * 上报指令执行状态
     * @param commandId 指令ID
     * @param status 状态
     * @param message 消息
     * @param timestamp 时间戳
     * @return 上报结果LiveData
     */
    LiveData<Result<BaseResponse>> reportCommandStatus(String commandId, String status, String message, long timestamp);
}
