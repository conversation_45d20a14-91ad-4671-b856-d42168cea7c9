[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: arm64-v8a", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a'", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a'", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\Workspace\\\\env\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\Workspace\\\\Projects\\\\BBNice\\\\AndroidClient\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\697m1ue3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a\\compile_commands.json.bin normally", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a\\compile_commands.json to D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\tools\\release\\arm64-v8a\\compile_commands.json", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]