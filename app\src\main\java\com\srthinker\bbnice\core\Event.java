package com.srthinker.bbnice.core;

/**
 * 用于LiveData的一次性事件包装器
 * 确保事件只被处理一次
 * @param <T> 事件内容的类型
 */
public class Event<T> {
    private final T content;
    private boolean hasBeenHandled = false;
    
    public Event(T content) {
        this.content = content;
    }
    
    /**
     * 返回内容并防止再次使用
     * @return 内容，如果已经被处理过则返回null
     */
    public T getContentIfNotHandled() {
        if (hasBeenHandled) {
            return null;
        } else {
            hasBeenHandled = true;
            return content;
        }
    }
    
    /**
     * 返回内容，无论是否已被处理
     * @return 内容
     */
    public T peekContent() {
        return content;
    }
    
    /**
     * 检查事件是否已被处理
     * @return 是否已被处理
     */
    public boolean hasBeenHandled() {
        return hasBeenHandled;
    }
}
