package com.srthinker.bbnice.setting;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.ConnectivityManager;
import android.net.MacAddress;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.NetworkSpecifier;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.net.wifi.WifiNetworkSpecifier;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.PatternMatcher;
import android.provider.Settings;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.textfield.TextInputEditText;
import com.srthinker.bbnice.R;
import com.srthinker.bbnice.registration.DeviceRegistrationActivity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class WifiActivity extends AppCompatActivity implements WifiAdapter.OnWifiItemClickListener {

    private static final String TAG = "WifiActivity";
    private static final int PERMISSIONS_REQUEST_CODE = 100;
    private static final int REQUEST_WIFI_SETTINGS = 200;

    private Toolbar toolbar;
    private SwitchCompat switchWifi;
    private RecyclerView rvWifiList;
    private SwipeRefreshLayout swipeRefresh;
    private ProgressBar progressBar;
    private TextView tvEmptyState;

    private boolean fromSplash = false; // 是否来自启动页

    private WifiManager wifiManager;
    private ConnectivityManager connectivityManager;
    private WifiAdapter wifiAdapter;
    private List<ScanResult> wifiList = new ArrayList<>();
    private BroadcastReceiver wifiScanReceiver;
    private BroadcastReceiver wifiStateReceiver;
    private ConnectivityManager.NetworkCallback networkCallback;
    private Handler handler = new Handler(Looper.getMainLooper());
    private Executor executor = Executors.newSingleThreadExecutor();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wifi);

        // 检查是否来自启动页
        fromSplash = getIntent().getBooleanExtra("from_splash", false);

        // 初始化视图
        initViews();

        // 初始化WiFi管理器
        wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);

        // 初始化适配器
        wifiAdapter = new WifiAdapter(this);
        rvWifiList.setLayoutManager(new LinearLayoutManager(this));
        rvWifiList.setAdapter(wifiAdapter);

        // 设置下拉刷新监听器
        swipeRefresh.setOnRefreshListener(this::scanWifiNetworks);

        // 设置WiFi开关监听器
        switchWifi.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                if (isChecked) {
                    enableWifi();
                } else {
                    disableWifi();
                }
            }
        });

        // 初始化WiFi扫描接收器
        initWifiScanReceiver();

        // 初始化WiFi状态接收器
        initWifiStateReceiver();

        // 检查WiFi状态
        updateWifiState();

        // 如果来自启动页，自动开启WiFi
        if (fromSplash && !wifiManager.isWifiEnabled()) {
            enableWifi();
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.wifi_settings));

        switchWifi = findViewById(R.id.switch_wifi);
        rvWifiList = findViewById(R.id.rv_wifi_list);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        progressBar = findViewById(R.id.progress_bar);
        tvEmptyState = findViewById(R.id.tv_empty_state);
    }

    private void initWifiScanReceiver() {
        wifiScanReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                boolean success = intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false);
                if (success) {
                    scanSuccess();
                } else {
                    scanFailure();
                }
            }
        };
    }

    private void initWifiStateReceiver() {
        wifiStateReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN);
                updateWifiState(wifiState);
            }
        };
    }

    private void updateWifiState() {
        if (wifiManager != null) {
            int wifiState = wifiManager.getWifiState();
            updateWifiState(wifiState);
        }
    }

    private void updateWifiState(int wifiState) {
        switch (wifiState) {
            case WifiManager.WIFI_STATE_ENABLED:
                switchWifi.setChecked(true);
                scanWifiNetworks();
                break;
            case WifiManager.WIFI_STATE_DISABLED:
                switchWifi.setChecked(false);
                clearWifiList();
                break;
            case WifiManager.WIFI_STATE_ENABLING:
                switchWifi.setChecked(true);
                showLoading();
                break;
            case WifiManager.WIFI_STATE_DISABLING:
                switchWifi.setChecked(false);
                showLoading();
                break;
        }
    }

    private void enableWifi() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本，引导用户到系统设置中开启WiFi
            Intent panelIntent = new Intent(Settings.Panel.ACTION_WIFI);
            startActivityForResult(panelIntent, REQUEST_WIFI_SETTINGS);
            Toast.makeText(this, R.string.please_enable_wifi, Toast.LENGTH_SHORT).show();
        } else {
            // Android 9及以下版本，可以直接开启WiFi
            if (wifiManager != null && !wifiManager.isWifiEnabled()) {
                wifiManager.setWifiEnabled(true);
                showLoading();
            }
        }
    }

    private void disableWifi() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本，引导用户到系统设置中关闭WiFi
            Intent panelIntent = new Intent(Settings.Panel.ACTION_WIFI);
            startActivityForResult(panelIntent, REQUEST_WIFI_SETTINGS);
            Toast.makeText(this, R.string.please_disable_wifi, Toast.LENGTH_SHORT).show();
        } else {
            // Android 9及以下版本，可以直接关闭WiFi
            if (wifiManager != null && wifiManager.isWifiEnabled()) {
                wifiManager.setWifiEnabled(false);
                clearWifiList();
            }
        }
    }

    private void scanWifiNetworks() {
        if (wifiManager == null || !wifiManager.isWifiEnabled()) {
            hideLoading();
            return;
        }

        // 检查位置权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, PERMISSIONS_REQUEST_CODE);
                return;
            }
        }

        // 检查位置服务是否开启
        LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        boolean isLocationEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
                locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && !isLocationEnabled) {
            showLocationServiceDialog();
            hideLoading();
            return;
        }

        showLoading();
        boolean success = wifiManager.startScan();
        if (!success) {
            scanFailure();
        }
    }

    private void scanSuccess() {
        if (wifiManager != null) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                showLocationServiceDialog();
                return;
            }
            List<ScanResult> results = wifiManager.getScanResults();
            WifiInfo currentWifi = wifiManager.getConnectionInfo();

            // 过滤和排序WiFi列表
            wifiList.clear();
            for (ScanResult result : results) {
                if (result.SSID != null && !result.SSID.isEmpty()) {
                    wifiList.add(result);
                }
            }

            // 按信号强度排序
            Collections.sort(wifiList, (o1, o2) -> Integer.compare(o2.level, o1.level));

            // 更新适配器
            wifiAdapter.updateWifiList(wifiList, currentWifi);

            // 更新UI状态
            hideLoading();
            if (wifiList.isEmpty()) {
                showEmptyState();
            } else {
                hideEmptyState();
            }
        }
    }

    private void scanFailure() {
        // 扫描失败，尝试使用上次的扫描结果
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            showLocationServiceDialog();
            return;
        }
        List<ScanResult> results = wifiManager.getScanResults();
        if (results != null && !results.isEmpty()) {
            wifiList.clear();
            wifiList.addAll(results);
            WifiInfo currentWifi = wifiManager.getConnectionInfo();
            wifiAdapter.updateWifiList(wifiList, currentWifi);
            hideLoading();
            hideEmptyState();
        } else {
            hideLoading();
            showEmptyState();
        }
    }

    private void clearWifiList() {
        wifiList.clear();
        wifiAdapter.updateWifiList(wifiList, null);
        hideLoading();
        showEmptyState();
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        swipeRefresh.setRefreshing(false);
    }

    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        swipeRefresh.setRefreshing(false);
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
    }

    private void showLocationServiceDialog() {
        new AlertDialog.Builder(this)
                .setTitle(R.string.permission_required)
                .setMessage(R.string.location_service_required)
                .setPositiveButton(R.string.enable_location, (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                    startActivity(intent);
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    @Override
    public void onWifiItemClick(ScanResult scanResult) {
        // 检查是否已连接该网络
        WifiInfo currentWifi = wifiManager.getConnectionInfo();
        if (currentWifi != null && currentWifi.getSSID() != null) {
            String currentSsid = currentWifi.getSSID().replace("\"", "");
            if (currentSsid.equals(scanResult.SSID)) {
                Toast.makeText(this, R.string.wifi_already_connected, Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 检查是否需要密码
        boolean isSecure = scanResult.capabilities != null &&
                (scanResult.capabilities.contains("WEP") ||
                 scanResult.capabilities.contains("PSK") ||
                 scanResult.capabilities.contains("EAP"));

        if (isSecure) {
            showPasswordDialog(scanResult);
        } else {
            connectToWifi(scanResult, null);
        }
    }

    private void showPasswordDialog(ScanResult scanResult) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View view = getLayoutInflater().inflate(R.layout.dialog_wifi_password, null);

        TextView tvWifiName = view.findViewById(R.id.tv_wifi_name);
        TextInputEditText etPassword = view.findViewById(R.id.et_wifi_password);
        CheckBox cbShowPassword = view.findViewById(R.id.cb_show_password);

        tvWifiName.setText(scanResult.SSID);

        cbShowPassword.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            } else {
                etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
            }
            etPassword.setSelection(etPassword.getText().length());
        });

        builder.setView(view)
                .setTitle(R.string.wifi_enter_password)
                .setPositiveButton(R.string.wifi_connect, (dialog, which) -> {
                    String password = etPassword.getText().toString();
                    if (password.isEmpty()) {
                        Toast.makeText(this, R.string.wifi_password_empty, Toast.LENGTH_SHORT).show();
                    } else {
                        connectToWifi(scanResult, password);
                    }
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    private void connectToWifi(ScanResult scanResult, String password) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本使用新的API连接WiFi
            connectToWifiAndroid10(scanResult, password);
        } else {
            // Android 9及以下版本使用旧的API连接WiFi
            connectToWifiLegacy(scanResult, password);
        }
    }

    private void connectToWifiAndroid10(ScanResult scanResult, String password) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            try {
                // 创建WiFi网络规范
                WifiNetworkSpecifier.Builder specifierBuilder = new WifiNetworkSpecifier.Builder();
                specifierBuilder.setSsid(scanResult.SSID);

                if (password != null) {
                    // 加密网络
                    specifierBuilder.setWpa2Passphrase(password);
                }

                WifiNetworkSpecifier wifiNetworkSpecifier = specifierBuilder.build();

                // 创建网络请求
                NetworkRequest.Builder networkRequestBuilder = new NetworkRequest.Builder();
                networkRequestBuilder.addTransportType(NetworkCapabilities.TRANSPORT_WIFI);
                networkRequestBuilder.setNetworkSpecifier(wifiNetworkSpecifier);
                NetworkRequest networkRequest = networkRequestBuilder.build();

                // 如果有之前的回调，先移除
                if (networkCallback != null) {
                    connectivityManager.unregisterNetworkCallback(networkCallback);
                    networkCallback = null;
                }

                // 创建新的回调
                networkCallback = new ConnectivityManager.NetworkCallback() {
                    @Override
                    public void onAvailable(Network network) {
                        super.onAvailable(network);
                        // 连接成功
                        handler.post(() -> {
                            Toast.makeText(WifiActivity.this,
                                    getString(R.string.wifi_connected, scanResult.SSID),
                                    Toast.LENGTH_SHORT).show();

                            // 如果来自启动页，跳转到设备注册页面
                            if (fromSplash) {
                                // 延迟1秒，确保连接稳定
                                handler.postDelayed(() -> navigateToRegistration(), 1000);
                            }
                        });
                    }

                    @Override
                    public void onUnavailable() {
                        super.onUnavailable();
                        // 连接失败
                        handler.post(() -> {
                            Toast.makeText(WifiActivity.this,
                                    R.string.wifi_connection_failed,
                                    Toast.LENGTH_SHORT).show();
                        });
                    }
                };

                // 请求连接到网络
                connectivityManager.requestNetwork(networkRequest, networkCallback);

                Toast.makeText(this,
                        getString(R.string.wifi_connecting, scanResult.SSID),
                        Toast.LENGTH_SHORT).show();

            } catch (Exception e) {
                Log.e(TAG, "Failed to connect to WiFi: " + e.getMessage());
                Toast.makeText(this, R.string.wifi_connection_failed, Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void connectToWifiLegacy(ScanResult scanResult, String password) {
        WifiConfiguration wifiConfig = new WifiConfiguration();
        wifiConfig.SSID = "\"" + scanResult.SSID + "\"";

        if (password == null) {
            // 开放网络
            wifiConfig.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
        } else {
            // 加密网络
            if (scanResult.capabilities.contains("WEP")) {
                wifiConfig.wepKeys[0] = "\"" + password + "\"";
                wifiConfig.wepTxKeyIndex = 0;
                wifiConfig.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
                wifiConfig.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40);
            } else if (scanResult.capabilities.contains("PSK")) {
                wifiConfig.preSharedKey = "\"" + password + "\"";
            } else if (scanResult.capabilities.contains("EAP")) {
                wifiConfig.enterpriseConfig.setPassword(password);
                wifiConfig.enterpriseConfig.setIdentity(scanResult.SSID);
            }
        }

        int netId = wifiManager.addNetwork(wifiConfig);
        if (netId != -1) {
            wifiManager.disconnect();
            wifiManager.enableNetwork(netId, true);
            wifiManager.reconnect();
            Toast.makeText(this, getString(R.string.wifi_connecting, scanResult.SSID), Toast.LENGTH_SHORT).show();

            // 如果来自启动页，等待连接成功后跳转到设备注册页面
            if (fromSplash) {
                // 延迟3秒，等待WiFi连接
                handler.postDelayed(() -> {
                    // 检查是否连接成功
                    WifiInfo connectionInfo = wifiManager.getConnectionInfo();
                    if (connectionInfo != null && connectionInfo.getNetworkId() != -1) {
                        // 跳转到设备注册页面
                        navigateToRegistration();
                    }
                }, 3000);
            }
        } else {
            Toast.makeText(this, R.string.wifi_connection_failed, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 跳转到设备注册页面
     */
    private void navigateToRegistration() {
        Intent intent = new Intent(this, DeviceRegistrationActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                scanWifiNetworks();
            } else {
                Toast.makeText(this, R.string.location_required, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_WIFI_SETTINGS) {
            // 从WiFi设置页面返回，更新WiFi状态
            updateWifiState();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 注册广播接收器
        registerReceiver(wifiScanReceiver, new IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION));
        registerReceiver(wifiStateReceiver, new IntentFilter(WifiManager.WIFI_STATE_CHANGED_ACTION));

        // 更新WiFi状态
        updateWifiState();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 注销广播接收器
        unregisterReceiver(wifiScanReceiver);
        unregisterReceiver(wifiStateReceiver);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 注销网络回调
        if (networkCallback != null && connectivityManager != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            } catch (Exception e) {
                Log.e(TAG, "Failed to unregister network callback: " + e.getMessage());
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
