@echo off
"D:\\Workspace\\env\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\cpp" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=26" ^
  "-DANDROID_PLATFORM=android-26" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973" ^
  "-DCMAKE_ANDROID_NDK=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973" ^
  "-DCMAKE_TOOLCHAIN_FILE=D:\\Workspace\\env\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=D:\\Workspace\\env\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\697m1ue3\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\697m1ue3\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-BD:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\.cxx\\RelWithDebInfo\\697m1ue3\\arm64-v8a" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared"
