package com.srthinker.bbnice.setting;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.srthinker.bbnice.home.HomeItemType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * HomeActivity的ViewModel，负责处理业务逻辑
 */
public class SettingViewModel extends ViewModel {

    private final MutableLiveData<List<SettingItemType>> itemsLiveData = new MutableLiveData<>();


    /**
     * 获取首页项目列表的LiveData
     * @return 首页项目列表LiveData
     */
    public LiveData<List<SettingItemType>> getItems() {
        return itemsLiveData;
    }


    /**
     * 加载首页项目列表
     */
    public void loadItems() {
        List<SettingItemType> items = new ArrayList<>(Arrays.asList(SettingItemType.values()));
        itemsLiveData.setValue(items);
    }
}
