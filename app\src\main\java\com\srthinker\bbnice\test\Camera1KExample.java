package com.srthinker.bbnice.test;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.view.TextureView;

import com.srthinker.bbnice.utils.BitmapUtils;
import com.srthinker.bbnice.utils.Camera2Helper;

/**
 * Camera2Helper 1K清晰度拍照使用示例
 */
public class Camera1KExample {
    private static final String TAG = "Camera1KExample";

    /**
     * 演示如何使用1K清晰度拍照
     */
    public static void demonstrateCamera1K(Context context, TextureView textureView) {
        Log.d(TAG, "=== Camera2Helper 1K清晰度拍照示例 ===");

        // 创建Camera2Helper实例
        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);

        // 1. 默认使用1K清晰度（MEDIUM质量）
        Log.d(TAG, "1. 使用默认1K清晰度拍照");
        cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
            @Override
            public void onBitmapCaptured(Bitmap bitmap) {
                if (bitmap != null) {
                    Log.d(TAG, "默认1K拍照成功: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    // 处理bitmap...
                } else {
                    Log.e(TAG, "默认1K拍照失败");
                }
            }
        });

        // 2. 明确设置为1K清晰度
        Log.d(TAG, "2. 明确设置1K清晰度");
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.MEDIUM);
        cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
            @Override
            public void onBitmapCaptured(Bitmap bitmap) {
                if (bitmap != null) {
                    Log.d(TAG, "1K清晰度拍照成功: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    // 应该得到接近1024x768的尺寸
                } else {
                    Log.e(TAG, "1K清晰度拍照失败");
                }
            }
        });

        // 3. 对比不同清晰度
        demonstrateDifferentQualities(cameraHelper);
    }

    /**
     * 演示不同清晰度的拍照效果
     */
    private static void demonstrateDifferentQualities(Camera2Helper cameraHelper) {
        Log.d(TAG, "=== 不同清晰度对比 ===");

        Camera2Helper.CaptureQuality[] qualities = {
            Camera2Helper.CaptureQuality.LOW,     // VGA: 640x480
            Camera2Helper.CaptureQuality.MEDIUM,  // 1K: 1024x768
            Camera2Helper.CaptureQuality.HIGH,    // 2K: 1920x1080
            Camera2Helper.CaptureQuality.ULTRA    // 4K: 4096x3072
        };

        for (Camera2Helper.CaptureQuality quality : qualities) {
            Log.d(TAG, "\n--- 测试 " + quality.name() + " 质量 ---");
            Log.d(TAG, "目标尺寸: " + quality.width + "x" + quality.height);

            cameraHelper.setCaptureQuality(quality);

            cameraHelper.takePicture(new Camera2Helper.OnBitmapCapturedListener() {
                @Override
                public void onBitmapCaptured(Bitmap bitmap) {
                    if (bitmap != null) {
                        int width = bitmap.getWidth();
                        int height = bitmap.getHeight();
                        int sizeKB = (width * height * 4) / 1024; // 粗略估算

                        Log.d(TAG, quality.name() + " 拍照结果:");
                        Log.d(TAG, "  实际尺寸: " + width + "x" + height);
                        Log.d(TAG, "  估算大小: " + sizeKB + "KB");
                        Log.d(TAG, "  宽高比: " + String.format("%.2f", (float) width / height));

                        // 验证是否符合预期
                        boolean isExpectedSize = isExpectedSizeForQuality(width, height, quality);
                        Log.d(TAG, "  符合预期: " + (isExpectedSize ? "✅" : "❌"));

                    } else {
                        Log.e(TAG, quality.name() + " 拍照失败");
                    }
                }
            });

            // 等待一段时间再拍下一张
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 检查尺寸是否符合质量预期
     */
    private static boolean isExpectedSizeForQuality(int width, int height, Camera2Helper.CaptureQuality quality) {
        switch (quality) {
            case LOW:
                // VGA质量：应该在400-1024像素范围
                return (width >= 400 && width <= 1024) && (height >= 300 && height <= 768);
            case MEDIUM:
                // 1K质量：应该在800-1536像素范围
                return (width >= 800 && width <= 1536) && (height >= 600 && height <= 1152);
            case HIGH:
                // 2K质量：应该在1200-2560像素范围
                return (width >= 1200 && width <= 2560) && (height >= 900 && height <= 1920);
            case ULTRA:
                // 4K质量：应该在2000像素以上
                return width >= 2000 || height >= 1500;
            default:
                return true;
        }
    }

    /**
     * 实际应用场景示例
     */
    public static void realWorldUsage(Context context, TextureView textureView) {
        Log.d(TAG, "=== 实际应用场景 ===");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);

        // 场景1：快速预览拍照（低质量，快速处理）
        Log.d(TAG, "场景1: 快速预览拍照");
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.LOW);
        cameraHelper.takePicture(bitmap -> {
            if (bitmap != null) {
                Log.d(TAG, "预览照片: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                // 用于快速预览、缩略图等
            }
        });

        // 场景2：标准拍照（1K质量，平衡质量和大小）
        Log.d(TAG, "场景2: 标准1K拍照");
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.MEDIUM);
        cameraHelper.takePicture(bitmap -> {
            if (bitmap != null) {
                Log.d(TAG, "标准照片: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                // 用于一般拍照、社交分享等
                
                // 可以进一步压缩
                byte[] compressed = BitmapUtils.compressBitmap(bitmap, 500); // 压缩到500KB
                Log.d(TAG, "压缩后大小: " + (compressed.length / 1024) + "KB");
            }
        });

        // 场景3：高质量拍照（仅在需要时使用）
        Log.d(TAG, "场景3: 高质量拍照");
        cameraHelper.setCaptureQuality(Camera2Helper.CaptureQuality.HIGH);
        cameraHelper.takePicture(bitmap -> {
            if (bitmap != null) {
                Log.d(TAG, "高质量照片: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                // 用于重要照片、打印等
            }
        });
    }

    /**
     * 性能对比测试
     */
    public static void performanceComparison(Context context, TextureView textureView) {
        Log.d(TAG, "=== 性能对比测试 ===");

        Camera2Helper cameraHelper = new Camera2Helper(context, textureView);

        Camera2Helper.CaptureQuality[] qualities = {
            Camera2Helper.CaptureQuality.LOW,
            Camera2Helper.CaptureQuality.MEDIUM,
            Camera2Helper.CaptureQuality.HIGH
        };

        for (Camera2Helper.CaptureQuality quality : qualities) {
            cameraHelper.setCaptureQuality(quality);

            long startTime = System.currentTimeMillis();

            cameraHelper.takePicture(bitmap -> {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                if (bitmap != null) {
                    int pixels = bitmap.getWidth() * bitmap.getHeight();
                    int estimatedSizeKB = (pixels * 4) / 1024;

                    Log.d(TAG, quality.name() + " 性能数据:");
                    Log.d(TAG, "  拍照耗时: " + duration + "ms");
                    Log.d(TAG, "  图片尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    Log.d(TAG, "  像素总数: " + pixels);
                    Log.d(TAG, "  估算大小: " + estimatedSizeKB + "KB");
                    Log.d(TAG, "  处理效率: " + (pixels / Math.max(duration, 1)) + " 像素/ms");
                }
            });

            // 等待处理完成
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}
