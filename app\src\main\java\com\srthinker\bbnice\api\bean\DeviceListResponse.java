package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

import java.util.List;

/**
 * 设备列表响应类
 * 用于表示获取设备列表API的响应
 */
public class DeviceListResponse extends BaseResponse {
    // 响应数据
    private final List<DeviceInfo> data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public DeviceListResponse(ApiResponse<List<DeviceInfo>> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public List<DeviceInfo> getData() {
        return data;
    }
}
