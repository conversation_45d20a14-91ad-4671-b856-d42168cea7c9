package com.srthinker.bbnice.db;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ChatMessageDao_Impl implements ChatMessageDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ChatMessageEntity> __insertionAdapterOfChatMessageEntity;

  private final EntityDeletionOrUpdateAdapter<ChatMessageEntity> __updateAdapterOfChatMessageEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateContent;

  private final SharedSQLiteStatement __preparedStmtOfMarkAsUploaded;

  public ChatMessageDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfChatMessageEntity = new EntityInsertionAdapter<ChatMessageEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `chat_messages` (`messageId`,`content`,`isSentByMe`,`timestamp`,`roomType`,`isUploaded`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ChatMessageEntity entity) {
        if (entity.getMessageId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getMessageId());
        }
        if (entity.getContent() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getContent());
        }
        final int _tmp = entity.isSentByMe() ? 1 : 0;
        statement.bindLong(3, _tmp);
        statement.bindLong(4, entity.getTimestamp());
        if (entity.getRoomType() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoomType());
        }
        final int _tmp_1 = entity.isUploaded() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
      }
    };
    this.__updateAdapterOfChatMessageEntity = new EntityDeletionOrUpdateAdapter<ChatMessageEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `chat_messages` SET `messageId` = ?,`content` = ?,`isSentByMe` = ?,`timestamp` = ?,`roomType` = ?,`isUploaded` = ? WHERE `messageId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ChatMessageEntity entity) {
        if (entity.getMessageId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getMessageId());
        }
        if (entity.getContent() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getContent());
        }
        final int _tmp = entity.isSentByMe() ? 1 : 0;
        statement.bindLong(3, _tmp);
        statement.bindLong(4, entity.getTimestamp());
        if (entity.getRoomType() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoomType());
        }
        final int _tmp_1 = entity.isUploaded() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        if (entity.getMessageId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getMessageId());
        }
      }
    };
    this.__preparedStmtOfUpdateContent = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE chat_messages SET content = ? WHERE messageId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkAsUploaded = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE chat_messages SET isUploaded = 1 WHERE messageId = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final ChatMessageEntity message) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfChatMessageEntity.insertAndReturnId(message);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int update(final ChatMessageEntity message) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __updateAdapterOfChatMessageEntity.handle(message);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateContent(final String messageId, final String content) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateContent.acquire();
    int _argIndex = 1;
    if (content == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, content);
    }
    _argIndex = 2;
    if (messageId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, messageId);
    }
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateContent.release(_stmt);
    }
  }

  @Override
  public int markAsUploaded(final String messageId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAsUploaded.acquire();
    int _argIndex = 1;
    if (messageId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, messageId);
    }
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfMarkAsUploaded.release(_stmt);
    }
  }

  @Override
  public List<ChatMessageEntity> getLatestMessages(final String roomType, final int limit) {
    final String _sql = "SELECT * FROM chat_messages WHERE roomType = ? ORDER BY timestamp ASC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (roomType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, roomType);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
      final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
      final int _cursorIndexOfIsSentByMe = CursorUtil.getColumnIndexOrThrow(_cursor, "isSentByMe");
      final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
      final int _cursorIndexOfRoomType = CursorUtil.getColumnIndexOrThrow(_cursor, "roomType");
      final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
      final List<ChatMessageEntity> _result = new ArrayList<ChatMessageEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ChatMessageEntity _item;
        _item = new ChatMessageEntity();
        final String _tmpMessageId;
        if (_cursor.isNull(_cursorIndexOfMessageId)) {
          _tmpMessageId = null;
        } else {
          _tmpMessageId = _cursor.getString(_cursorIndexOfMessageId);
        }
        _item.setMessageId(_tmpMessageId);
        final String _tmpContent;
        if (_cursor.isNull(_cursorIndexOfContent)) {
          _tmpContent = null;
        } else {
          _tmpContent = _cursor.getString(_cursorIndexOfContent);
        }
        _item.setContent(_tmpContent);
        final boolean _tmpIsSentByMe;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSentByMe);
        _tmpIsSentByMe = _tmp != 0;
        _item.setSentByMe(_tmpIsSentByMe);
        final long _tmpTimestamp;
        _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
        _item.setTimestamp(_tmpTimestamp);
        final String _tmpRoomType;
        if (_cursor.isNull(_cursorIndexOfRoomType)) {
          _tmpRoomType = null;
        } else {
          _tmpRoomType = _cursor.getString(_cursorIndexOfRoomType);
        }
        _item.setRoomType(_tmpRoomType);
        final boolean _tmpIsUploaded;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsUploaded);
        _tmpIsUploaded = _tmp_1 != 0;
        _item.setUploaded(_tmpIsUploaded);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<ChatMessageEntity> getMessagesByPage(final String roomType, final int limit,
      final int offset) {
    final String _sql = "SELECT * FROM chat_messages WHERE roomType = ? ORDER BY timestamp ASC LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (roomType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, roomType);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    _argIndex = 3;
    _statement.bindLong(_argIndex, offset);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
      final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
      final int _cursorIndexOfIsSentByMe = CursorUtil.getColumnIndexOrThrow(_cursor, "isSentByMe");
      final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
      final int _cursorIndexOfRoomType = CursorUtil.getColumnIndexOrThrow(_cursor, "roomType");
      final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
      final List<ChatMessageEntity> _result = new ArrayList<ChatMessageEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ChatMessageEntity _item;
        _item = new ChatMessageEntity();
        final String _tmpMessageId;
        if (_cursor.isNull(_cursorIndexOfMessageId)) {
          _tmpMessageId = null;
        } else {
          _tmpMessageId = _cursor.getString(_cursorIndexOfMessageId);
        }
        _item.setMessageId(_tmpMessageId);
        final String _tmpContent;
        if (_cursor.isNull(_cursorIndexOfContent)) {
          _tmpContent = null;
        } else {
          _tmpContent = _cursor.getString(_cursorIndexOfContent);
        }
        _item.setContent(_tmpContent);
        final boolean _tmpIsSentByMe;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSentByMe);
        _tmpIsSentByMe = _tmp != 0;
        _item.setSentByMe(_tmpIsSentByMe);
        final long _tmpTimestamp;
        _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
        _item.setTimestamp(_tmpTimestamp);
        final String _tmpRoomType;
        if (_cursor.isNull(_cursorIndexOfRoomType)) {
          _tmpRoomType = null;
        } else {
          _tmpRoomType = _cursor.getString(_cursorIndexOfRoomType);
        }
        _item.setRoomType(_tmpRoomType);
        final boolean _tmpIsUploaded;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsUploaded);
        _tmpIsUploaded = _tmp_1 != 0;
        _item.setUploaded(_tmpIsUploaded);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<ChatMessageEntity> getNotUploadedMessages(final int limit) {
    final String _sql = "SELECT * FROM chat_messages WHERE isUploaded = 0 ORDER BY timestamp ASC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
      final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
      final int _cursorIndexOfIsSentByMe = CursorUtil.getColumnIndexOrThrow(_cursor, "isSentByMe");
      final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
      final int _cursorIndexOfRoomType = CursorUtil.getColumnIndexOrThrow(_cursor, "roomType");
      final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
      final List<ChatMessageEntity> _result = new ArrayList<ChatMessageEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ChatMessageEntity _item;
        _item = new ChatMessageEntity();
        final String _tmpMessageId;
        if (_cursor.isNull(_cursorIndexOfMessageId)) {
          _tmpMessageId = null;
        } else {
          _tmpMessageId = _cursor.getString(_cursorIndexOfMessageId);
        }
        _item.setMessageId(_tmpMessageId);
        final String _tmpContent;
        if (_cursor.isNull(_cursorIndexOfContent)) {
          _tmpContent = null;
        } else {
          _tmpContent = _cursor.getString(_cursorIndexOfContent);
        }
        _item.setContent(_tmpContent);
        final boolean _tmpIsSentByMe;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSentByMe);
        _tmpIsSentByMe = _tmp != 0;
        _item.setSentByMe(_tmpIsSentByMe);
        final long _tmpTimestamp;
        _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
        _item.setTimestamp(_tmpTimestamp);
        final String _tmpRoomType;
        if (_cursor.isNull(_cursorIndexOfRoomType)) {
          _tmpRoomType = null;
        } else {
          _tmpRoomType = _cursor.getString(_cursorIndexOfRoomType);
        }
        _item.setRoomType(_tmpRoomType);
        final boolean _tmpIsUploaded;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsUploaded);
        _tmpIsUploaded = _tmp_1 != 0;
        _item.setUploaded(_tmpIsUploaded);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<ChatMessageEntity> getMessagesByTimeRange(final long startTime, final long endTime) {
    final String _sql = "SELECT * FROM chat_messages WHERE timestamp >= ? AND timestamp < ? ORDER BY timestamp ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
      final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
      final int _cursorIndexOfIsSentByMe = CursorUtil.getColumnIndexOrThrow(_cursor, "isSentByMe");
      final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
      final int _cursorIndexOfRoomType = CursorUtil.getColumnIndexOrThrow(_cursor, "roomType");
      final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
      final List<ChatMessageEntity> _result = new ArrayList<ChatMessageEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ChatMessageEntity _item;
        _item = new ChatMessageEntity();
        final String _tmpMessageId;
        if (_cursor.isNull(_cursorIndexOfMessageId)) {
          _tmpMessageId = null;
        } else {
          _tmpMessageId = _cursor.getString(_cursorIndexOfMessageId);
        }
        _item.setMessageId(_tmpMessageId);
        final String _tmpContent;
        if (_cursor.isNull(_cursorIndexOfContent)) {
          _tmpContent = null;
        } else {
          _tmpContent = _cursor.getString(_cursorIndexOfContent);
        }
        _item.setContent(_tmpContent);
        final boolean _tmpIsSentByMe;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSentByMe);
        _tmpIsSentByMe = _tmp != 0;
        _item.setSentByMe(_tmpIsSentByMe);
        final long _tmpTimestamp;
        _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
        _item.setTimestamp(_tmpTimestamp);
        final String _tmpRoomType;
        if (_cursor.isNull(_cursorIndexOfRoomType)) {
          _tmpRoomType = null;
        } else {
          _tmpRoomType = _cursor.getString(_cursorIndexOfRoomType);
        }
        _item.setRoomType(_tmpRoomType);
        final boolean _tmpIsUploaded;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsUploaded);
        _tmpIsUploaded = _tmp_1 != 0;
        _item.setUploaded(_tmpIsUploaded);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
