// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBluetoothBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivBluetoothIcon;

  @NonNull
  public final TextView tvDeviceAddress;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvDeviceStatus;

  private ItemBluetoothBinding(@NonNull CardView rootView, @NonNull ImageView ivBluetoothIcon,
      @NonNull TextView tvDeviceAddress, @NonNull TextView tvDeviceName,
      @NonNull TextView tvDeviceStatus) {
    this.rootView = rootView;
    this.ivBluetoothIcon = ivBluetoothIcon;
    this.tvDeviceAddress = tvDeviceAddress;
    this.tvDeviceName = tvDeviceName;
    this.tvDeviceStatus = tvDeviceStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBluetoothBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBluetoothBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_bluetooth, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBluetoothBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_bluetooth_icon;
      ImageView ivBluetoothIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivBluetoothIcon == null) {
        break missingId;
      }

      id = R.id.tv_device_address;
      TextView tvDeviceAddress = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceAddress == null) {
        break missingId;
      }

      id = R.id.tv_device_name;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tv_device_status;
      TextView tvDeviceStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceStatus == null) {
        break missingId;
      }

      return new ItemBluetoothBinding((CardView) rootView, ivBluetoothIcon, tvDeviceAddress,
          tvDeviceName, tvDeviceStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
