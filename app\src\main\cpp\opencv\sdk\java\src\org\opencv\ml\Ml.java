//
// This file is auto-generated. Please don't modify it!
//
package org.opencv.ml;



// C++: class Ml

public class Ml {

    // C++: enum ErrorTypes (cv.ml.ErrorTypes)
    public static final int
            TEST_ERROR = 0,
            TRAIN_ERROR = 1;


    // C++: enum SampleTypes (cv.ml.SampleTypes)
    public static final int
            ROW_SAMPLE = 0,
            COL_SAMPLE = 1;


    // C++: enum VariableTypes (cv.ml.VariableTypes)
    public static final int
            VAR_NUMERICAL = 0,
            VAR_ORDERED = 0,
            VAR_CATEGORICAL = 1;




}
