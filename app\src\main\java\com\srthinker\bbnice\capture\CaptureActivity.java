package com.srthinker.bbnice.capture;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.TextureView;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.ImageRecognizeResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.common.AIServerUtils;
import com.srthinker.bbnice.common.LoadingDialog;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.utils.BitmapUtils;
import com.srthinker.bbnice.utils.BlurUtil;
import com.srthinker.bbnice.utils.Camera2Helper;

public class CaptureActivity extends AppCompatActivity {
    private static final String  TAG = "CaptureActivity";
    private TextureView surfaceView;
    private ImageView btnCap;
    private TextView tvENResult;
    private TextView tvCNResult;
    private View imgResultBgMask;
    private ImageView imgResultBg;
    private Button btnBack;
    private Camera2Helper mCameraHelper;

    private AIRepository aiRepository;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_capturectivity);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        surfaceView = findViewById(R.id.surface);
        btnCap = findViewById(R.id.btn_cap);
        tvENResult = findViewById(R.id.tv_en_result);
        tvCNResult = findViewById(R.id.tv_cn_result);
        imgResultBgMask = findViewById(R.id.result_bg_mask);
        imgResultBg = findViewById(R.id.result_bg);
        btnBack = findViewById(R.id.btn_back);

        mCameraHelper = new Camera2Helper(this, surfaceView);
        aiRepository = ApiRepositoryProvider.getInstance(this).getAiRepository();

        btnCap.setOnClickListener(v -> {



            btnCap.setVisibility(GONE);
            LoadingDialog.display("正在识别...");
            mCameraHelper.takePicture(bitmap -> {
//                Bitmap blur = BlurUtil.blur(bitmap, 15, 0.3f);
                runOnUiThread(()-> {
                    aiRepository.imageRecognize(bitmap).observe(CaptureActivity.this, new Observer<Result<ImageRecognizeResponse>>() {
                        @Override
                        public void onChanged(Result<ImageRecognizeResponse> result) {
                            if (result.isLoading()) {

                            } else {
                                if (result.isError()) {
                                    tvCNResult.setText("识别失败" + result.getError().getMessage());
                                    tvENResult.setText("Identification failed\n");
                                } else {
                                    ImageRecognizeResponse.ImageRecognizeResponseData responseData = result.getData().getData();
                                    tvCNResult.setText(responseData.getText());
                                    tvENResult.setText(responseData.getEn());
                                }

                                LoadingDialog.finish();
                                imgResultBgMask.setBackground(new BitmapDrawable(bitmap));
                                imgResultBgMask.setVisibility(VISIBLE);
                                imgResultBg.setVisibility(VISIBLE);
                                btnBack.setVisibility(VISIBLE);
                            }
                        }
                    });
                });

            });
        });

        btnBack.setOnClickListener(v -> {
            imgResultBgMask.setVisibility(GONE);
            imgResultBg.setVisibility(GONE);
            btnBack.setVisibility(GONE);
            btnCap.setVisibility(VISIBLE);
            tvCNResult.setText("");
            tvENResult.setText("");
        });


    }

    @Override
    protected void onResume() {
        super.onResume();
        mCameraHelper.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mCameraHelper.onPause();
    }

    @Override
    protected void onStart() {
        super.onStart();
        LoadingDialog.init(this);
    }
}