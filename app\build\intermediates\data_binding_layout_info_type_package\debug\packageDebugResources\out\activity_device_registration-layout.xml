<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_device_registration" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_device_registration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_device_registration_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="103" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/tvDeviceId" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="30" endOffset="60"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="32" startOffset="4" endLine="41" endOffset="63"/></Target><Target id="@+id/ivQrCode" view="ImageView"><Expressions/><location startLine="43" startOffset="4" endLine="54" endOffset="61"/></Target><Target id="@+id/tvExpireTime" view="TextView"><Expressions/><location startLine="56" startOffset="4" endLine="67" endOffset="61"/></Target><Target id="@+id/tvInstructions" view="TextView"><Expressions/><location startLine="69" startOffset="4" endLine="80" endOffset="65"/></Target><Target id="@+id/btnRefresh" view="Button"><Expressions/><location startLine="82" startOffset="4" endLine="91" endOffset="67"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="93" startOffset="4" endLine="101" endOffset="51"/></Target></Targets></Layout>