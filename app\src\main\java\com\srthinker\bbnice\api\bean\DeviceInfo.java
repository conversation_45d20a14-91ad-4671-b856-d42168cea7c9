package com.srthinker.bbnice.api.bean;

/**
 * 设备信息类
 * 用于表示设备信息
 */
public class DeviceInfo {
    // 设备ID
    private String device_id;
    
    // 宠物名称
    private String pet_name;
    
    /**
     * 获取设备ID
     * @return 设备ID
     */
    public String getDeviceId() {
        return device_id;
    }
    
    /**
     * 设置设备ID
     * @param device_id 设备ID
     */
    public void setDeviceId(String device_id) {
        this.device_id = device_id;
    }
    
    /**
     * 获取宠物名称
     * @return 宠物名称
     */
    public String getPetName() {
        return pet_name;
    }
    
    /**
     * 设置宠物名称
     * @param pet_name 宠物名称
     */
    public void setPetName(String pet_name) {
        this.pet_name = pet_name;
    }
}
