package com.srthinker.bbnice.mqtt;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.hivemq.client.mqtt.MqttClient;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.hivemq.client.mqtt.mqtt3.message.publish.Mqtt3Publish;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.home.HomeActivity;
import com.srthinker.bbnice.mqtt.handler.AICommandHandler;
import com.srthinker.bbnice.mqtt.handler.ActionCommandHandler;
import com.srthinker.bbnice.mqtt.handler.CommandHandler;
import com.srthinker.bbnice.mqtt.model.MqttCommand;
import com.srthinker.bbnice.mqtt.model.MqttCommandStatus;
import com.srthinker.bbnice.utils.JsonUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 使用HiveMQ客户端的MQTT服务
 * 兼容Android 14+
 */
public class HiveMqttService extends Service {
    private static final String TAG = "HiveMqttService";

    // 通知相关常量
    private static final String NOTIFICATION_CHANNEL_ID = "mqtt_service_channel";
    private static final int NOTIFICATION_ID = 1001;

    // 重连相关常量
    private static final long RECONNECT_INTERVAL = 5000; // 5秒

    // MQTT客户端
    private Mqtt3AsyncClient mqttClient;

    // MQTT配置
    private MqttConfig mqttConfig;

    // MQTT Repository
    private BBNiceApi api;

    // 指令处理器列表
    private final List<CommandHandler> commandHandlers = new ArrayList<>();

    // 已处理的指令ID集合，用于去重
    private final Set<String> processedCommandIds = new HashSet<>();

    // 线程池，用于处理指令
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    // 重连处理器
    private final Handler reconnectHandler = new Handler(Looper.getMainLooper());

    // 重连任务
    private final Runnable reconnectRunnable = this::connectToMqttBroker;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "HiveMQ MQTT service created");

        // 初始化MQTT配置
        mqttConfig = MqttConfig.getInstance(this);

        // 初始化MQTT Repository
        api = BBNiceApi.getInstance(this);

        // 初始化指令处理器
        initCommandHandlers();

        // 创建通知渠道
        createNotificationChannel();

        // 启动前台服务
        try {
            startForeground(NOTIFICATION_ID, createNotification());
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to start foreground service: " + e.getMessage());
            // 如果权限不足，停止服务
            stopSelf();
            return;
        }

        // 连接到MQTT服务器
        connectToMqttBroker();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "HiveMQ MQTT service started");
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "HiveMQ MQTT service destroyed");

        // 断开MQTT连接
        disconnectFromMqttBroker();

        // 关闭线程池
        executorService.shutdown();

        // 移除重连任务
        reconnectHandler.removeCallbacks(reconnectRunnable);

        super.onDestroy();
    }

    /**
     * 初始化指令处理器
     */
    private void initCommandHandlers() {
        commandHandlers.add(new ActionCommandHandler(this));
        commandHandlers.add(new AICommandHandler(this));
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    NOTIFICATION_CHANNEL_ID,
                    "MQTT服务",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("MQTT消息推送服务");
            channel.setShowBadge(false);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    /**
     * 创建通知
     */
    private Notification createNotification() {
        Intent intent = new Intent(this, HomeActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ?
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE :
                        PendingIntent.FLAG_UPDATE_CURRENT
        );

        return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("MQTT服务运行中")
                .setContentText("正在接收消息推送")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .build();
    }

    /**
     * 连接到MQTT服务器
     */
    private void connectToMqttBroker() {
        try {
            Log.d(TAG, "Connecting to MQTT broker: " + mqttConfig.getBrokerUrl());

            // 创建MQTT客户端
            mqttClient = MqttClient.builder()
                    .useMqttVersion3()
                    .identifier(mqttConfig.getClientId())
                    .serverHost(extractHost(mqttConfig.getBrokerUrl()))
                    .serverPort(extractPort(mqttConfig.getBrokerUrl()))
                    .buildAsync();

            // 连接到服务器
            mqttClient.connectWith()
                    .keepAlive(60)
                    .cleanSession(true)
                    .send()
                    .whenComplete((connAck, throwable) -> {
                        if (throwable != null) {
                            Log.e(TAG, "Failed to connect to MQTT broker", throwable);
                            // 安排重连任务
                            reconnectHandler.postDelayed(reconnectRunnable, RECONNECT_INTERVAL);
                        } else {
                            Log.d(TAG, "Connected to MQTT broker successfully");
                            // 订阅主题
                            subscribeToTopic();
                        }
                    });

        } catch (Exception e) {
            Log.e(TAG, "Error connecting to MQTT broker", e);
            // 安排重连任务
            reconnectHandler.postDelayed(reconnectRunnable, RECONNECT_INTERVAL);
        }
    }

    /**
     * 从URL中提取主机名
     */
    private String extractHost(String brokerUrl) {
        // 移除协议前缀
        String host = brokerUrl.replaceFirst("tcp://", "").replaceFirst("ssl://", "");
        // 移除端口号
        int colonIndex = host.indexOf(':');
        if (colonIndex != -1) {
            host = host.substring(0, colonIndex);
        }
        return host;
    }

    /**
     * 从URL中提取端口号
     */
    private int extractPort(String brokerUrl) {
        int colonIndex = brokerUrl.lastIndexOf(':');
        if (colonIndex != -1) {
            try {
                return Integer.parseInt(brokerUrl.substring(colonIndex + 1));
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse port from URL: " + brokerUrl);
            }
        }
        // 默认端口
        return brokerUrl.startsWith("ssl://") ? 8883 : 1883;
    }

    /**
     * 订阅主题
     */
    private void subscribeToTopic() {
        String topic = mqttConfig.getTopic();
        Log.d(TAG, "Subscribing to topic: " + topic);

        mqttClient.subscribeWith()
                .topicFilter(topic)
                .qos(com.hivemq.client.mqtt.datatypes.MqttQos.AT_LEAST_ONCE)
                .callback(this::onMessageReceived)
                .send()
                .whenComplete((subAck, throwable) -> {
                    if (throwable != null) {
                        Log.e(TAG, "Failed to subscribe to topic: " + topic, throwable);
                    } else {
                        Log.d(TAG, "Successfully subscribed to topic: " + topic);
                    }
                });
    }

    /**
     * 消息接收回调
     */
    private void onMessageReceived(Mqtt3Publish publish) {
        String topic = publish.getTopic().toString();
        String payload = new String(publish.getPayloadAsBytes(), StandardCharsets.UTF_8);
        Log.d(TAG, "Message received on topic: " + topic + ", payload: " + payload);

        // 处理消息
        processMessage(topic, payload);
    }

    /**
     * 断开MQTT连接
     */
    private void disconnectFromMqttBroker() {
        if (mqttClient != null && mqttClient.getState().isConnected()) {
            Log.d(TAG, "Disconnecting from MQTT broker");
            mqttClient.disconnect()
                    .whenComplete((unused, throwable) -> {
                        if (throwable != null) {
                            Log.e(TAG, "Error disconnecting from MQTT broker", throwable);
                        } else {
                            Log.d(TAG, "Disconnected from MQTT broker");
                        }
                    });
        }
    }

    /**
     * 处理消息
     * @param topic 主题
     * @param payload 消息内容
     */
    private void processMessage(String topic, String payload) {
        // 在线程池中处理消息
        executorService.execute(() -> {
            try {
                Log.d(TAG, "processMqttMessage: " + payload);
                // 解析消息
                MqttCommand command = JsonUtils.fromJson(payload, MqttCommand.class);
                if (command == null) {
                    Log.e(TAG, "Failed to parse message: " + payload);
                    return;
                }

                // 检查是否已处理过该指令
                if (processedCommandIds.contains(command.getCommandId())) {
                    Log.d(TAG, "Command already processed: " + command.getCommandId());
                    return;
                }

                // 添加到已处理指令集合
                processedCommandIds.add(command.getCommandId());

                // 限制已处理指令集合大小
                if (processedCommandIds.size() > 100) {
                    // 移除最早的50个指令ID
                    List<String> toRemove = new ArrayList<>(processedCommandIds).subList(0, 50);
                    toRemove.forEach(processedCommandIds::remove);
                }

                // 查找合适的处理器
                CommandHandler handler = findHandler(command);
                if (handler == null) {
                    Log.e(TAG, "No handler found for command: " + command);
                    reportCommandStatus(MqttCommandStatus.createFailed(
                            command.getCommandId(),
                            "No handler found for command: " + command.getCommand()));
                    return;
                }

                // 处理指令
                MqttCommandStatus status = handler.handleCommand(command);

                // 上报指令执行状态
                reportCommandStatus(status);
            } catch (Exception e) {
                Log.e(TAG, "Error processing message: " + payload, e);
            }
        });
    }

    /**
     * 查找合适的处理器
     * @param command MQTT指令
     * @return 处理器
     */
    private CommandHandler findHandler(MqttCommand command) {
        for (CommandHandler handler : commandHandlers) {
            if (handler.canHandle(command)) {
                return handler;
            }
        }
        return null;
    }

    /**
     * 上报指令执行状态
     * @param status 指令状态
     */
    private void reportCommandStatus(MqttCommandStatus status) {
        if (status == null) {
            Log.w(TAG, "Command status is null, skipping report");
            return;
        }

        Log.d(TAG, "Reporting command status: " + status);

        // 通过API上报状态
        api.reportCommandStatus(status.getCommandId(),
                status.getStatus(),
                status.getMessage(),
                status.getTimestamp(),
                new com.srthinker.bbnice.api.ApiCallback<com.srthinker.bbnice.api.BaseResponse>() {
            @Override
            public void onSuccess(com.srthinker.bbnice.api.BaseResponse response) {
                Log.d(TAG, "Command status reported successfully: " + status.getCommandId());
            }

            @Override
            public void onError(com.srthinker.bbnice.api.ApiError error) {
                Log.e(TAG, "Failed to report command status: " + error.getMessage());
            }
        });
    }
}
