// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVoiceBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnMediaVolumeDown;

  @NonNull
  public final ImageButton btnMediaVolumeUp;

  @NonNull
  public final ImageButton btnRingVolumeDown;

  @NonNull
  public final ImageButton btnRingVolumeUp;

  @NonNull
  public final View divider;

  @NonNull
  public final View divider2;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final ConstraintLayout mediaVolumeContainer;

  @NonNull
  public final ConstraintLayout muteContainer;

  @NonNull
  public final ConstraintLayout ringVolumeContainer;

  @NonNull
  public final SeekBar seekbarMediaVolume;

  @NonNull
  public final SeekBar seekbarRingVolume;

  @NonNull
  public final SwitchCompat switchMute;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvMediaVolumeLabel;

  @NonNull
  public final TextView tvMediaVolumeLevel;

  @NonNull
  public final TextView tvMuteLabel;

  @NonNull
  public final TextView tvPermissionWarning;

  @NonNull
  public final TextView tvRingVolumeLabel;

  @NonNull
  public final TextView tvRingVolumeLevel;

  private ActivityVoiceBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton btnMediaVolumeDown, @NonNull ImageButton btnMediaVolumeUp,
      @NonNull ImageButton btnRingVolumeDown, @NonNull ImageButton btnRingVolumeUp,
      @NonNull View divider, @NonNull View divider2, @NonNull ConstraintLayout main,
      @NonNull ConstraintLayout mediaVolumeContainer, @NonNull ConstraintLayout muteContainer,
      @NonNull ConstraintLayout ringVolumeContainer, @NonNull SeekBar seekbarMediaVolume,
      @NonNull SeekBar seekbarRingVolume, @NonNull SwitchCompat switchMute,
      @NonNull Toolbar toolbar, @NonNull TextView tvMediaVolumeLabel,
      @NonNull TextView tvMediaVolumeLevel, @NonNull TextView tvMuteLabel,
      @NonNull TextView tvPermissionWarning, @NonNull TextView tvRingVolumeLabel,
      @NonNull TextView tvRingVolumeLevel) {
    this.rootView = rootView;
    this.btnMediaVolumeDown = btnMediaVolumeDown;
    this.btnMediaVolumeUp = btnMediaVolumeUp;
    this.btnRingVolumeDown = btnRingVolumeDown;
    this.btnRingVolumeUp = btnRingVolumeUp;
    this.divider = divider;
    this.divider2 = divider2;
    this.main = main;
    this.mediaVolumeContainer = mediaVolumeContainer;
    this.muteContainer = muteContainer;
    this.ringVolumeContainer = ringVolumeContainer;
    this.seekbarMediaVolume = seekbarMediaVolume;
    this.seekbarRingVolume = seekbarRingVolume;
    this.switchMute = switchMute;
    this.toolbar = toolbar;
    this.tvMediaVolumeLabel = tvMediaVolumeLabel;
    this.tvMediaVolumeLevel = tvMediaVolumeLevel;
    this.tvMuteLabel = tvMuteLabel;
    this.tvPermissionWarning = tvPermissionWarning;
    this.tvRingVolumeLabel = tvRingVolumeLabel;
    this.tvRingVolumeLevel = tvRingVolumeLevel;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVoiceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVoiceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_voice, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVoiceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_media_volume_down;
      ImageButton btnMediaVolumeDown = ViewBindings.findChildViewById(rootView, id);
      if (btnMediaVolumeDown == null) {
        break missingId;
      }

      id = R.id.btn_media_volume_up;
      ImageButton btnMediaVolumeUp = ViewBindings.findChildViewById(rootView, id);
      if (btnMediaVolumeUp == null) {
        break missingId;
      }

      id = R.id.btn_ring_volume_down;
      ImageButton btnRingVolumeDown = ViewBindings.findChildViewById(rootView, id);
      if (btnRingVolumeDown == null) {
        break missingId;
      }

      id = R.id.btn_ring_volume_up;
      ImageButton btnRingVolumeUp = ViewBindings.findChildViewById(rootView, id);
      if (btnRingVolumeUp == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.divider2;
      View divider2 = ViewBindings.findChildViewById(rootView, id);
      if (divider2 == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.media_volume_container;
      ConstraintLayout mediaVolumeContainer = ViewBindings.findChildViewById(rootView, id);
      if (mediaVolumeContainer == null) {
        break missingId;
      }

      id = R.id.mute_container;
      ConstraintLayout muteContainer = ViewBindings.findChildViewById(rootView, id);
      if (muteContainer == null) {
        break missingId;
      }

      id = R.id.ring_volume_container;
      ConstraintLayout ringVolumeContainer = ViewBindings.findChildViewById(rootView, id);
      if (ringVolumeContainer == null) {
        break missingId;
      }

      id = R.id.seekbar_media_volume;
      SeekBar seekbarMediaVolume = ViewBindings.findChildViewById(rootView, id);
      if (seekbarMediaVolume == null) {
        break missingId;
      }

      id = R.id.seekbar_ring_volume;
      SeekBar seekbarRingVolume = ViewBindings.findChildViewById(rootView, id);
      if (seekbarRingVolume == null) {
        break missingId;
      }

      id = R.id.switch_mute;
      SwitchCompat switchMute = ViewBindings.findChildViewById(rootView, id);
      if (switchMute == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_media_volume_label;
      TextView tvMediaVolumeLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvMediaVolumeLabel == null) {
        break missingId;
      }

      id = R.id.tv_media_volume_level;
      TextView tvMediaVolumeLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvMediaVolumeLevel == null) {
        break missingId;
      }

      id = R.id.tv_mute_label;
      TextView tvMuteLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvMuteLabel == null) {
        break missingId;
      }

      id = R.id.tv_permission_warning;
      TextView tvPermissionWarning = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionWarning == null) {
        break missingId;
      }

      id = R.id.tv_ring_volume_label;
      TextView tvRingVolumeLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvRingVolumeLabel == null) {
        break missingId;
      }

      id = R.id.tv_ring_volume_level;
      TextView tvRingVolumeLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvRingVolumeLevel == null) {
        break missingId;
      }

      return new ActivityVoiceBinding((ConstraintLayout) rootView, btnMediaVolumeDown,
          btnMediaVolumeUp, btnRingVolumeDown, btnRingVolumeUp, divider, divider2, main,
          mediaVolumeContainer, muteContainer, ringVolumeContainer, seekbarMediaVolume,
          seekbarRingVolume, switchMute, toolbar, tvMediaVolumeLabel, tvMediaVolumeLevel,
          tvMuteLabel, tvPermissionWarning, tvRingVolumeLabel, tvRingVolumeLevel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
