// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRecognitionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnSpeak;

  @NonNull
  public final FrameLayout localViewContainer;

  @NonNull
  public final ConstraintLayout main;

  private ActivityRecognitionBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView btnSpeak, @NonNull FrameLayout localViewContainer,
      @NonNull ConstraintLayout main) {
    this.rootView = rootView;
    this.btnSpeak = btnSpeak;
    this.localViewContainer = localViewContainer;
    this.main = main;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRecognitionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRecognitionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_recognition, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRecognitionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_speak;
      ImageView btnSpeak = ViewBindings.findChildViewById(rootView, id);
      if (btnSpeak == null) {
        break missingId;
      }

      id = R.id.local_view_container;
      FrameLayout localViewContainer = ViewBindings.findChildViewById(rootView, id);
      if (localViewContainer == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      return new ActivityRecognitionBinding((ConstraintLayout) rootView, btnSpeak,
          localViewContainer, main);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
