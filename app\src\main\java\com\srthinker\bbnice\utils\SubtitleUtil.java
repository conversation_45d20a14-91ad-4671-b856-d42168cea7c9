package com.srthinker.bbnice.utils;

import com.srthinker.bbnice.chat.MySubtitleMessage;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

public class SubtitleUtil {
    public static MySubtitleMessage unpack(ByteBuffer message, StringBuilder subtitles) {
        final int kSubtitleHeaderSize = 8;
        if (message.remaining() < kSubtitleHeaderSize) {
            return null;
        }

        // 魔法数字 "subv"
        int magicNumber = (message.get() << 24) | (message.get() << 16) | (message.get() << 8) | (message.get());
        if (magicNumber != 0x73756276) {
            return null;
        }

        int length = message.getInt();

        if (message.remaining() != length) {
            return null;
        }

        // 读取字幕内容
        byte[] subtitleBytes = new byte[length];
        message.get(subtitleBytes);
        subtitles.append(new String(subtitleBytes, StandardCharsets.UTF_8));
        return JsonUtils.fromJson(subtitles.toString(), MySubtitleMessage.class);
    }
}
