package com.srthinker.bbnice.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.graphics.YuvImage;
import android.hardware.camera2.*;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;
import androidx.annotation.NonNull;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class Camera2Helper implements TextureView.SurfaceTextureListener {
    private static final String TAG = "Camera2Helper";

    private final Context context;
    private final TextureView textureView;
    private final AtomicBoolean isCapturing = new AtomicBoolean(false);

    private String cameraId;
    private CameraDevice cameraDevice;
    private CameraCaptureSession captureSession;
    private Handler backgroundHandler;
    private HandlerThread backgroundThread;

    // 相机特性参数
    private boolean supportsAutoFocus;
    private boolean supportsOIS;

    private ImageReader photoImageReader;
    private Surface previewSurface;
    private final Object sessionLock = new Object();

    // 回调接口
    public interface OnBitmapCapturedListener {
        void onBitmapCaptured(Bitmap bitmap);
    }

    public Camera2Helper(Context context, TextureView textureView) {
        this.context = context;
        this.textureView = textureView;
        this.textureView.setSurfaceTextureListener(this);
    }

    // SurfaceTexture回调
    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
        openCamera();
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
        // 需要重新配置会话
        createCameraPreviewSession();
    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        closeCamera();
        return true;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {}

    // 启动后台线程
    private void startBackgroundThread() {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }

    // 停止后台线程
    private void stopBackgroundThread() {
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join();
                backgroundThread = null;
                backgroundHandler = null;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // 打开相机
    private void openCamera() {
        startBackgroundThread();
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);

            // 获取后置摄像头
            for (String id : manager.getCameraIdList()) {
                CameraCharacteristics characteristics = manager.getCameraCharacteristics(id);
                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
                if (facing != null && facing == CameraCharacteristics.LENS_FACING_BACK) {
                    cameraId = id;

                    // 检查自动对焦支持
                    int[] afModes = characteristics.get(CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES);
                    supportsAutoFocus = Arrays.asList(afModes).contains(
                            CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_PICTURE);

                    // 检查光学防抖支持
                    int[] stabilizationModes = characteristics.get(
                            CameraCharacteristics.CONTROL_AVAILABLE_VIDEO_STABILIZATION_MODES);
                    supportsOIS = Arrays.asList(stabilizationModes).contains(
                            CameraCharacteristics.CONTROL_VIDEO_STABILIZATION_MODE_ON);
                    break;
                }
            }

            if (cameraId == null) return;

            manager.openCamera(cameraId, stateCallback, backgroundHandler);
        } catch (CameraAccessException | SecurityException e) {
            e.printStackTrace();
        }
    }

    // 相机设备状态回调
    private final CameraDevice.StateCallback stateCallback = new CameraDevice.StateCallback() {
        @Override
        public void onOpened(@NonNull CameraDevice camera) {
            cameraDevice = camera;
            createCameraPreviewSession();
        }

        @Override
        public void onDisconnected(@NonNull CameraDevice camera) {
            closeCamera();
        }

        @Override
        public void onError(@NonNull CameraDevice camera, int error) {
            closeCamera();
        }
    };

    // 创建预览会话
    private void createCameraPreviewSession() {
        synchronized (sessionLock) {
            try {
                if (cameraDevice == null) return;

                SurfaceTexture texture = textureView.getSurfaceTexture();
                if (texture == null) return;

                texture.setDefaultBufferSize(getPreviewSize().getWidth(), getPreviewSize().getHeight());
                previewSurface = new Surface(texture);

                // 创建始终包含预览Surface的会话
                cameraDevice.createCaptureSession(
                        Collections.singletonList(previewSurface),
                        new CameraCaptureSession.StateCallback() {
                            @Override
                            public void onConfigured(@NonNull CameraCaptureSession session) {
                                synchronized (sessionLock) {
                                    captureSession = session;
                                    try {
                                        CaptureRequest.Builder previewBuilder =
                                                cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
                                        previewBuilder.addTarget(previewSurface);
                                        configureCaptureRequest(previewBuilder);

                                        session.setRepeatingRequest(
                                                previewBuilder.build(), null, backgroundHandler);
                                    } catch (CameraAccessException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                                // 处理配置失败
                            }
                        }, backgroundHandler);
            } catch (CameraAccessException e) {
                e.printStackTrace();
            }
        }
    }

    // 拍照方法 - 重写版本
    public void takePicture(OnBitmapCapturedListener listener) {
        if (!isCapturing.compareAndSet(false, true)) {
            Log.w(TAG, "Camera is already capturing");
            return;
        }

        synchronized (sessionLock) {
            try {
                if (cameraDevice == null) {
                    Log.e(TAG, "Camera device is null");
                    isCapturing.set(false);
                    return;
                }

                // 获取最佳拍照尺寸
                Size captureSize = getBestCaptureSize();
                Log.d(TAG, "Capture size: " + captureSize.getWidth() + "x" + captureSize.getHeight());

                // 创建ImageReader并设置监听器
                photoImageReader = ImageReader.newInstance(
                        captureSize.getWidth(),
                        captureSize.getHeight(),
                        ImageFormat.JPEG, 1);

                // 设置图像可用监听器
                photoImageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
                    @Override
                    public void onImageAvailable(ImageReader reader) {
                        Log.d(TAG, "Image available in ImageReader");
                        processCapturedImage(listener);
                    }
                }, backgroundHandler);

                // 创建包含预览和拍照Surface的会话
                List<Surface> surfaces = Arrays.asList(
                        previewSurface,
                        photoImageReader.getSurface()
                );

                cameraDevice.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                    @Override
                    public void onConfigured(@NonNull CameraCaptureSession session) {
                        Log.d(TAG, "Capture session configured");
                        try {
                            // 创建拍照请求
                            CaptureRequest.Builder captureBuilder =
                                    cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);
                            captureBuilder.addTarget(photoImageReader.getSurface());

                            // 配置拍照参数
                            configureCaptureRequest(captureBuilder);

                            // 设置JPEG质量
                            captureBuilder.set(CaptureRequest.JPEG_QUALITY, (byte) 95);

                            // 执行拍照
                            session.capture(captureBuilder.build(), new CameraCaptureSession.CaptureCallback() {
                                @Override
                                public void onCaptureStarted(@NonNull CameraCaptureSession session,
                                                           @NonNull CaptureRequest request,
                                                           long timestamp, long frameNumber) {
                                    Log.d(TAG, "Capture started");
                                }

                                @Override
                                public void onCaptureCompleted(@NonNull CameraCaptureSession session,
                                                               @NonNull CaptureRequest request,
                                                               @NonNull TotalCaptureResult result) {
                                    Log.d(TAG, "Capture completed");
                                    // 注意：不在这里处理图像，而是在OnImageAvailableListener中处理
                                }

                                @Override
                                public void onCaptureFailed(@NonNull CameraCaptureSession session,
                                                           @NonNull CaptureRequest request,
                                                           @NonNull CaptureFailure failure) {
                                    Log.e(TAG, "Capture failed: " + failure.getReason());
                                    cleanupAfterCapture();
                                    if (listener != null) {
                                        listener.onBitmapCaptured(null);
                                    }
                                }
                            }, backgroundHandler);
                        } catch (CameraAccessException e) {
                            Log.e(TAG, "Error creating capture request", e);
                            cleanupAfterCapture();
                            if (listener != null) {
                                listener.onBitmapCaptured(null);
                            }
                        }
                    }

                    @Override
                    public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                        Log.e(TAG, "Failed to configure capture session");
                        cleanupAfterCapture();
                        if (listener != null) {
                            listener.onBitmapCaptured(null);
                        }
                    }
                }, backgroundHandler);
            } catch (CameraAccessException e) {
                Log.e(TAG, "Error setting up capture session", e);
                cleanupAfterCapture();
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
            }
        }
    }

    // 图像处理方法
    private void processCapturedImage(OnBitmapCapturedListener listener) {
        Image image = null;
        try {
            image = photoImageReader.acquireNextImage();
            if (image == null) {
                Log.w("Camera2Helper", "Failed to acquire image from ImageReader");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            // 检查图像格式和平面
            Image.Plane[] planes = image.getPlanes();
            if (planes == null || planes.length == 0) {
                Log.w("Camera2Helper", "Image has no planes");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            ByteBuffer buffer = planes[0].getBuffer();
            if (buffer == null) {
                Log.w("Camera2Helper", "Image plane buffer is null");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            byte[] bytes = new byte[buffer.remaining()];
            buffer.get(bytes);
            Bitmap bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

            if (listener != null) {
                listener.onBitmapCaptured(bitmap);
            }
        } catch (Exception e) {
            Log.e("Camera2Helper", "Error processing captured image", e);
            if (listener != null) {
                listener.onBitmapCaptured(null);
            }
        } finally {
            if (image != null) {
                image.close();
            }
            // 清理资源
            cleanupAfterCapture();
        }
    }

    /**
     * 获取最佳拍照尺寸
     */
    private Size getBestCaptureSize() {
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
            CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
            StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);

            // 获取JPEG格式支持的尺寸
            Size[] jpegSizes = map.getOutputSizes(ImageFormat.JPEG);
            if (jpegSizes != null && jpegSizes.length > 0) {
                // 选择最大的JPEG尺寸
                return Collections.max(Arrays.asList(jpegSizes), new CompareSizesByArea());
            }

            // 如果没有JPEG尺寸，使用预览尺寸
            return getPreviewSize();
        } catch (CameraAccessException e) {
            Log.e(TAG, "Error getting capture size", e);
            return new Size(1920, 1080); // 默认值
        }
    }

    /**
     * 清理拍照后的资源
     */
    private void cleanupAfterCapture() {
        try {
            if (photoImageReader != null) {
                photoImageReader.close();
                photoImageReader = null;
            }
            isCapturing.set(false);

            // 恢复预览会话
            createCameraPreviewSession();
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }

    // 公共配置方法
    private void configureCaptureRequest(CaptureRequest.Builder builder) {
        if (supportsAutoFocus) {
            builder.set(CaptureRequest.CONTROL_AF_MODE,
                    CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
        }
        if (supportsOIS) {
            builder.set(CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE,
                    CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE_ON);
        }
    }

    // YUV转Bitmap（优化版本）
    private Bitmap convertYUV420888ToBitmap(Image image) {
        if (image == null) return null;

        int width = image.getWidth();
        int height = image.getHeight();

        Image.Plane yPlane = image.getPlanes()[0];
        Image.Plane uPlane = image.getPlanes()[1];
        Image.Plane vPlane = image.getPlanes()[2];

        ByteBuffer yBuffer = yPlane.getBuffer();
        ByteBuffer uBuffer = uPlane.getBuffer();
        ByteBuffer vBuffer = vPlane.getBuffer();

        // 创建NV21数据
        byte[] nv21 = new byte[width * height * 3 / 2];

        // 填充Y分量
        yBuffer.get(nv21, 0, width * height);

        // 处理UV分量
        int uvWidth = width / 2;
        int uvHeight = height / 2;
        int uvPixelStride = uPlane.getPixelStride();
        int uvRowStride = uPlane.getRowStride();

        byte[] u = new byte[uvWidth * uvHeight];
        byte[] v = new byte[uvWidth * uvHeight];

        if (uvPixelStride == 1) {
            uBuffer.get(u);
            vBuffer.get(v);
        } else {
            for (int row = 0; row < uvHeight; row++) {
                for (int col = 0; col < uvWidth; col++) {
                    int offset = row * uvRowStride + col * uvPixelStride;
                    u[row * uvWidth + col] = uBuffer.get(offset);
                    v[row * uvWidth + col] = vBuffer.get(offset);
                }
            }
        }

        // 合并UV分量
        int uvOffset = width * height;
        for (int i = 0; i < u.length; i++) {
            nv21[uvOffset + i * 2] = v[i];
            nv21[uvOffset + i * 2 + 1] = u[i];
        }

        // 转换为Bitmap
        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, width, height, null);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, width, height), 100, stream);
        byte[] jpegData = stream.toByteArray();
        return BitmapFactory.decodeByteArray(jpegData, 0, jpegData.length);
    }

    // 获取最佳预览尺寸
    private Size getPreviewSize() {
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
            CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
            StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
            List<Size> sizes = Arrays.asList(map.getOutputSizes(SurfaceTexture.class));

            // 按面积选择最大支持的预览尺寸
            return Collections.max(sizes, new CompareSizesByArea());
        } catch (CameraAccessException e) {
            return new Size(1920, 1080); // 默认值
        }
    }

    // 尺寸比较器
    private static class CompareSizesByArea implements Comparator<Size> {
        @Override
        public int compare(Size lhs, Size rhs) {
            return Long.signum(
                    (long) lhs.getWidth() * lhs.getHeight() -
                            (long) rhs.getWidth() * rhs.getHeight());
        }
    }

    // 关闭相机
    private void closeCamera() {
        try {
            if (captureSession != null) {
                captureSession.close();
                captureSession = null;
            }
            if (cameraDevice != null) {
                cameraDevice.close();
                cameraDevice = null;
            }
        } finally {
            stopBackgroundThread();
        }
    }

    // 生命周期管理
    public void onResume() {
        if (!textureView.isAvailable()) {
            textureView.setSurfaceTextureListener(this);
        } else {
            openCamera();
        }
    }

    public void onPause() {
        closeCamera();
    }
}