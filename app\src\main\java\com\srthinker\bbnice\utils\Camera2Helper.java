package com.srthinker.bbnice.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.graphics.YuvImage;
import android.hardware.camera2.*;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;
import android.view.Display;
import android.view.Surface;
import android.view.TextureView;
import android.view.WindowManager;
import androidx.annotation.NonNull;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class Camera2Helper implements TextureView.SurfaceTextureListener {
    private static final String TAG = "Camera2Helper";

    private final Context context;
    private final TextureView textureView;
    private final AtomicBoolean isCapturing = new AtomicBoolean(false);

    private String cameraId;
    private CameraDevice cameraDevice;
    private CameraCaptureSession captureSession;
    private Handler backgroundHandler;
    private HandlerThread backgroundThread;

    // 相机特性参数
    private boolean supportsAutoFocus;
    private boolean supportsOIS;

    private ImageReader photoImageReader;
    private Surface previewSurface;
    private final Object sessionLock = new Object();

    // 回调接口
    public interface OnBitmapCapturedListener {
        void onBitmapCaptured(Bitmap bitmap);
    }

    // 拍照清晰度枚举
    public enum CaptureQuality {
        LOW(640, 480),      // VGA
        MEDIUM(1024, 768),  // 1K (默认)
        HIGH(1920, 1080),   // 2K
        ULTRA(4096, 3072);  // 4K

        public final int width;
        public final int height;

        CaptureQuality(int width, int height) {
            this.width = width;
            this.height = height;
        }
    }

    // 当前拍照质量设置
    private CaptureQuality captureQuality = CaptureQuality.MEDIUM;

    // 相机传感器方向
    private int sensorOrientation = 0;

    public Camera2Helper(Context context, TextureView textureView) {
        this.context = context;
        this.textureView = textureView;
        this.textureView.setSurfaceTextureListener(this);
    }

    /**
     * 设置拍照质量
     * @param quality 拍照质量
     */
    public void setCaptureQuality(CaptureQuality quality) {
        this.captureQuality = quality;
        Log.d(TAG, "Capture quality set to: " + quality.name() + " (" + quality.width + "x" + quality.height + ")");
    }

    /**
     * 获取当前拍照质量
     */
    public CaptureQuality getCaptureQuality() {
        return captureQuality;
    }

    // SurfaceTexture回调
    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
        openCamera();
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
        // 需要重新配置会话
        createCameraPreviewSession();
    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        closeCamera();
        return true;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {}

    // 启动后台线程
    private void startBackgroundThread() {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }

    // 停止后台线程
    private void stopBackgroundThread() {
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join();
                backgroundThread = null;
                backgroundHandler = null;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // 打开相机
    private void openCamera() {
        startBackgroundThread();
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);

            // 获取后置摄像头
            for (String id : manager.getCameraIdList()) {
                CameraCharacteristics characteristics = manager.getCameraCharacteristics(id);
                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
                if (facing != null && facing == CameraCharacteristics.LENS_FACING_BACK) {
                    cameraId = id;

                    // 获取传感器方向
                    Integer orientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);
                    sensorOrientation = orientation != null ? orientation : 0;
                    Log.d(TAG, "Camera sensor orientation: " + sensorOrientation);

                    // 检查自动对焦支持
                    int[] afModes = characteristics.get(CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES);
                    supportsAutoFocus = Arrays.asList(afModes).contains(
                            CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_PICTURE);

                    // 检查光学防抖支持
                    int[] stabilizationModes = characteristics.get(
                            CameraCharacteristics.CONTROL_AVAILABLE_VIDEO_STABILIZATION_MODES);
                    supportsOIS = Arrays.asList(stabilizationModes).contains(
                            CameraCharacteristics.CONTROL_VIDEO_STABILIZATION_MODE_ON);
                    break;
                }
            }

            if (cameraId == null) return;

            manager.openCamera(cameraId, stateCallback, backgroundHandler);
        } catch (CameraAccessException | SecurityException e) {
            e.printStackTrace();
        }
    }

    // 相机设备状态回调
    private final CameraDevice.StateCallback stateCallback = new CameraDevice.StateCallback() {
        @Override
        public void onOpened(@NonNull CameraDevice camera) {
            cameraDevice = camera;
            createCameraPreviewSession();
        }

        @Override
        public void onDisconnected(@NonNull CameraDevice camera) {
            closeCamera();
        }

        @Override
        public void onError(@NonNull CameraDevice camera, int error) {
            closeCamera();
        }
    };

    // 创建预览会话
    private void createCameraPreviewSession() {
        synchronized (sessionLock) {
            try {
                if (cameraDevice == null) return;

                SurfaceTexture texture = textureView.getSurfaceTexture();
                if (texture == null) return;

                texture.setDefaultBufferSize(getPreviewSize().getWidth(), getPreviewSize().getHeight());
                previewSurface = new Surface(texture);

                // 创建始终包含预览Surface的会话
                cameraDevice.createCaptureSession(
                        Collections.singletonList(previewSurface),
                        new CameraCaptureSession.StateCallback() {
                            @Override
                            public void onConfigured(@NonNull CameraCaptureSession session) {
                                synchronized (sessionLock) {
                                    captureSession = session;
                                    try {
                                        CaptureRequest.Builder previewBuilder =
                                                cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
                                        previewBuilder.addTarget(previewSurface);
                                        configureCaptureRequest(previewBuilder);

                                        session.setRepeatingRequest(
                                                previewBuilder.build(), null, backgroundHandler);
                                    } catch (CameraAccessException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                                // 处理配置失败
                            }
                        }, backgroundHandler);
            } catch (CameraAccessException e) {
                e.printStackTrace();
            }
        }
    }

    // 拍照方法 - 重写版本
    public void takePicture(OnBitmapCapturedListener listener) {
        if (!isCapturing.compareAndSet(false, true)) {
            Log.w(TAG, "Camera is already capturing");
            return;
        }

        synchronized (sessionLock) {
            try {
                if (cameraDevice == null) {
                    Log.e(TAG, "Camera device is null");
                    isCapturing.set(false);
                    return;
                }

                // 获取最佳拍照尺寸
                Size captureSize = getBestCaptureSize();
                Log.d(TAG, "Capture size: " + captureSize.getWidth() + "x" + captureSize.getHeight());

                // 创建ImageReader并设置监听器
                photoImageReader = ImageReader.newInstance(
                        captureSize.getWidth(),
                        captureSize.getHeight(),
                        ImageFormat.JPEG, 1);

                // 设置图像可用监听器
                photoImageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
                    @Override
                    public void onImageAvailable(ImageReader reader) {
                        Log.d(TAG, "Image available in ImageReader");
                        processCapturedImage(listener);
                    }
                }, backgroundHandler);

                // 创建包含预览和拍照Surface的会话
                List<Surface> surfaces = Arrays.asList(
                        previewSurface,
                        photoImageReader.getSurface()
                );

                cameraDevice.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                    @Override
                    public void onConfigured(@NonNull CameraCaptureSession session) {
                        Log.d(TAG, "Capture session configured");
                        try {
                            // 创建拍照请求
                            CaptureRequest.Builder captureBuilder =
                                    cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);
                            captureBuilder.addTarget(photoImageReader.getSurface());

                            // 配置拍照参数
                            configureCaptureRequest(captureBuilder);

                            // 设置JPEG质量
                            captureBuilder.set(CaptureRequest.JPEG_QUALITY, (byte) 95);

                            // 执行拍照
                            session.capture(captureBuilder.build(), new CameraCaptureSession.CaptureCallback() {
                                @Override
                                public void onCaptureStarted(@NonNull CameraCaptureSession session,
                                                           @NonNull CaptureRequest request,
                                                           long timestamp, long frameNumber) {
                                    Log.d(TAG, "Capture started");
                                }

                                @Override
                                public void onCaptureCompleted(@NonNull CameraCaptureSession session,
                                                               @NonNull CaptureRequest request,
                                                               @NonNull TotalCaptureResult result) {
                                    Log.d(TAG, "Capture completed");
                                    // 注意：不在这里处理图像，而是在OnImageAvailableListener中处理
                                }

                                @Override
                                public void onCaptureFailed(@NonNull CameraCaptureSession session,
                                                           @NonNull CaptureRequest request,
                                                           @NonNull CaptureFailure failure) {
                                    Log.e(TAG, "Capture failed: " + failure.getReason());
                                    cleanupAfterCapture();
                                    if (listener != null) {
                                        listener.onBitmapCaptured(null);
                                    }
                                }
                            }, backgroundHandler);
                        } catch (CameraAccessException e) {
                            Log.e(TAG, "Error creating capture request", e);
                            cleanupAfterCapture();
                            if (listener != null) {
                                listener.onBitmapCaptured(null);
                            }
                        }
                    }

                    @Override
                    public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                        Log.e(TAG, "Failed to configure capture session");
                        cleanupAfterCapture();
                        if (listener != null) {
                            listener.onBitmapCaptured(null);
                        }
                    }
                }, backgroundHandler);
            } catch (CameraAccessException e) {
                Log.e(TAG, "Error setting up capture session", e);
                cleanupAfterCapture();
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
            }
        }
    }

    // 图像处理方法
    private void processCapturedImage(OnBitmapCapturedListener listener) {
        Image image = null;
        try {
            image = photoImageReader.acquireNextImage();
            if (image == null) {
                Log.w("Camera2Helper", "Failed to acquire image from ImageReader");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            // 检查图像格式和平面
            Image.Plane[] planes = image.getPlanes();
            if (planes == null || planes.length == 0) {
                Log.w("Camera2Helper", "Image has no planes");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            ByteBuffer buffer = planes[0].getBuffer();
            if (buffer == null) {
                Log.w("Camera2Helper", "Image plane buffer is null");
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
                return;
            }

            byte[] bytes = new byte[buffer.remaining()];
            buffer.get(bytes);
            Bitmap originalBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

            if (originalBitmap != null) {
                // 修正图像方向
                Bitmap correctedBitmap = correctImageOrientation(originalBitmap);

                if (listener != null) {
                    listener.onBitmapCaptured(correctedBitmap);
                }

                // 回收原始bitmap（如果创建了新的）
                if (correctedBitmap != originalBitmap && !originalBitmap.isRecycled()) {
                    originalBitmap.recycle();
                }
            } else {
                if (listener != null) {
                    listener.onBitmapCaptured(null);
                }
            }
        } catch (Exception e) {
            Log.e("Camera2Helper", "Error processing captured image", e);
            if (listener != null) {
                listener.onBitmapCaptured(null);
            }
        } finally {
            if (image != null) {
                image.close();
            }
            // 清理资源
            cleanupAfterCapture();
        }
    }

    /**
     * 修正图像方向
     */
    private Bitmap correctImageOrientation(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }

        // 计算需要旋转的角度
        int rotationAngle = getImageRotationAngle();

        Log.d(TAG, "Image rotation angle: " + rotationAngle);

        // 如果不需要旋转，直接返回原图
        if (rotationAngle == 0) {
            return bitmap;
        }

        try {
            // 创建旋转矩阵
            Matrix matrix = new Matrix();
            matrix.postRotate(rotationAngle);

            // 应用旋转
            Bitmap rotatedBitmap = Bitmap.createBitmap(
                    bitmap, 0, 0,
                    bitmap.getWidth(), bitmap.getHeight(),
                    matrix, true);

            Log.d(TAG, "Image rotated successfully from " +
                  bitmap.getWidth() + "x" + bitmap.getHeight() + " to " +
                  rotatedBitmap.getWidth() + "x" + rotatedBitmap.getHeight());

            return rotatedBitmap;
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "Out of memory while rotating image", e);
            return bitmap; // 返回原图
        } catch (Exception e) {
            Log.e(TAG, "Error rotating image", e);
            return bitmap; // 返回原图
        }
    }

    /**
     * 计算图像需要旋转的角度
     */
    private int getImageRotationAngle() {
        // 获取设备当前方向
        int deviceRotation = getDeviceRotation();

        // 计算最终旋转角度
        // 对于后置摄像头，需要根据传感器方向和设备方向计算
        int rotationAngle;

        if (sensorOrientation == 90) {
            // 大多数设备的后置摄像头传感器方向是90度
            switch (deviceRotation) {
                case Surface.ROTATION_0:   // 竖屏
                    rotationAngle = 90;
                    break;
                case Surface.ROTATION_90:  // 横屏（左转）
                    rotationAngle = 0;
                    break;
                case Surface.ROTATION_180: // 倒立
                    rotationAngle = 270;
                    break;
                case Surface.ROTATION_270: // 横屏（右转）
                    rotationAngle = 180;
                    break;
                default:
                    rotationAngle = 90;
            }
        } else if (sensorOrientation == 270) {
            // 一些设备的传感器方向是270度
            switch (deviceRotation) {
                case Surface.ROTATION_0:
                    rotationAngle = 270;
                    break;
                case Surface.ROTATION_90:
                    rotationAngle = 180;
                    break;
                case Surface.ROTATION_180:
                    rotationAngle = 90;
                    break;
                case Surface.ROTATION_270:
                    rotationAngle = 0;
                    break;
                default:
                    rotationAngle = 270;
            }
        } else {
            // 其他情况，使用传感器方向
            rotationAngle = (sensorOrientation - deviceRotation * 90 + 360) % 360;
        }

        Log.d(TAG, "Sensor orientation: " + sensorOrientation +
              ", Device rotation: " + deviceRotation +
              ", Calculated rotation: " + rotationAngle);

        return rotationAngle;
    }

    /**
     * 获取设备当前旋转角度
     */
    private int getDeviceRotation() {
        try {
            WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            if (windowManager != null) {
                Display display = windowManager.getDefaultDisplay();
                return display.getRotation();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting device rotation", e);
        }
        return Surface.ROTATION_0; // 默认竖屏
    }

    /**
     * 获取最佳拍照尺寸 - 根据设置的质量
     */
    private Size getBestCaptureSize() {
        return getCaptureSize(captureQuality);
    }

    /**
     * 根据指定质量获取拍照尺寸
     */
    private Size getCaptureSize(CaptureQuality quality) {
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
            CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
            StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);

            // 获取JPEG格式支持的尺寸
            Size[] jpegSizes = map.getOutputSizes(ImageFormat.JPEG);
            if (jpegSizes == null || jpegSizes.length == 0) {
                Log.w(TAG, "No JPEG sizes available, using default");
                return new Size(quality.width, quality.height);
            }

            Log.d(TAG, "Target quality: " + quality.name() + " (" + quality.width + "x" + quality.height + ")");
            Log.d(TAG, "Available JPEG sizes:");
            for (Size size : jpegSizes) {
                Log.d(TAG, "  " + size.getWidth() + "x" + size.getHeight());
            }

            // 查找最接近目标质量的尺寸
            Size bestSize = findClosestSizeForQuality(jpegSizes, quality);

            Log.d(TAG, "Selected capture size: " + bestSize.getWidth() + "x" + bestSize.getHeight());
            return bestSize;

        } catch (CameraAccessException e) {
            Log.e(TAG, "Error getting capture size for quality: " + quality.name(), e);
            return new Size(quality.width, quality.height); // 默认尺寸
        }
    }

    /**
     * 为指定质量查找最接近的相机支持尺寸
     */
    private Size findClosestSizeForQuality(Size[] availableSizes, CaptureQuality quality) {
        Size targetSize = new Size(quality.width, quality.height);

        // 计算目标比例
        float targetRatio = (float) quality.width / quality.height;

        Size bestSize = null;
        int minDifference = Integer.MAX_VALUE;

        for (Size size : availableSizes) {
            int width = size.getWidth();
            int height = size.getHeight();
            float ratio = (float) width / height;

            // 根据质量设置过滤尺寸
            if (!isSuitableForQuality(size, quality)) {
                continue;
            }

            // 优先选择比例接近的尺寸
            float ratioDiff = Math.abs(ratio - targetRatio);
            if (ratioDiff > 0.3f) { // 比例差异太大，跳过
                continue;
            }

            // 计算与目标尺寸的差异
            int sizeDiff = calculateSizeDifference(size, targetSize);

            if (sizeDiff < minDifference) {
                minDifference = sizeDiff;
                bestSize = size;
            }
        }

        // 如果没找到合适的，选择一个备选尺寸
        if (bestSize == null) {
            Log.w(TAG, "No suitable size found for quality " + quality.name() + ", selecting fallback");
            bestSize = selectFallbackSize(availableSizes, quality);
        }

        return bestSize;
    }

    /**
     * 检查尺寸是否适合指定质量
     */
    private boolean isSuitableForQuality(Size size, CaptureQuality quality) {
        int width = size.getWidth();
        int height = size.getHeight();

        switch (quality) {
            case LOW:
                // VGA质量：400-800像素范围
                return width >= 400 && width <= 1024 && height >= 300 && height <= 768;
            case MEDIUM:
                // 1K质量：800-1536像素范围
                return width >= 800 && width <= 1536 && height >= 600 && height <= 1152;
            case HIGH:
                // 2K质量：1200-2560像素范围
                return width >= 1200 && width <= 2560 && height >= 900 && height <= 1920;
            case ULTRA:
                // 4K质量：2000像素以上
                return width >= 2000 || height >= 1500;
            default:
                return true;
        }
    }

    /**
     * 选择备选尺寸
     */
    private Size selectFallbackSize(Size[] availableSizes, CaptureQuality quality) {
        // 按面积排序
        List<Size> sortedSizes = Arrays.asList(availableSizes);
        sortedSizes.sort(new CompareSizesByArea());

        switch (quality) {
            case LOW:
                // 选择较小的尺寸
                return sortedSizes.get(Math.min(2, sortedSizes.size() - 1));
            case MEDIUM:
                // 选择中等尺寸
                return sortedSizes.get(sortedSizes.size() / 3);
            case HIGH:
                // 选择较大尺寸
                return sortedSizes.get(sortedSizes.size() * 2 / 3);
            case ULTRA:
                // 选择最大尺寸
                return sortedSizes.get(sortedSizes.size() - 1);
            default:
                return sortedSizes.get(sortedSizes.size() / 2);
        }
    }


    /**
     * 计算两个尺寸之间的差异
     */
    private int calculateSizeDifference(Size size1, Size size2) {
        int widthDiff = Math.abs(size1.getWidth() - size2.getWidth());
        int heightDiff = Math.abs(size1.getHeight() - size2.getHeight());
        return widthDiff + heightDiff;
    }

    /**
     * 清理拍照后的资源
     */
    private void cleanupAfterCapture() {
        try {
            if (photoImageReader != null) {
                photoImageReader.close();
                photoImageReader = null;
            }
            isCapturing.set(false);

            // 恢复预览会话
            createCameraPreviewSession();
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }

    // 公共配置方法
    private void configureCaptureRequest(CaptureRequest.Builder builder) {
        if (supportsAutoFocus) {
            builder.set(CaptureRequest.CONTROL_AF_MODE,
                    CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
        }
        if (supportsOIS) {
            builder.set(CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE,
                    CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE_ON);
        }
    }

    // 获取最佳预览尺寸
    private Size getPreviewSize() {
        try {
            CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
            CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
            StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
            List<Size> sizes = Arrays.asList(map.getOutputSizes(SurfaceTexture.class));

            // 按面积选择最大支持的预览尺寸
            return Collections.max(sizes, new CompareSizesByArea());
        } catch (CameraAccessException e) {
            return new Size(1920, 1080); // 默认值
        }
    }

    // 尺寸比较器
    private static class CompareSizesByArea implements Comparator<Size> {
        @Override
        public int compare(Size lhs, Size rhs) {
            return Long.signum(
                    (long) lhs.getWidth() * lhs.getHeight() -
                            (long) rhs.getWidth() * rhs.getHeight());
        }
    }

    // 关闭相机
    private void closeCamera() {
        try {
            if (captureSession != null) {
                captureSession.close();
                captureSession = null;
            }
            if (cameraDevice != null) {
                cameraDevice.close();
                cameraDevice = null;
            }
        } finally {
            stopBackgroundThread();
        }
    }

    // 生命周期管理
    public void onResume() {
        if (!textureView.isAvailable()) {
            textureView.setSurfaceTextureListener(this);
        } else {
            openCamera();
        }
    }

    public void onPause() {
        closeCamera();
    }
}