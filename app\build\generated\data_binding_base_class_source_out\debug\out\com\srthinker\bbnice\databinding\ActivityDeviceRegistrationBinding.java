// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDeviceRegistrationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnRefresh;

  @NonNull
  public final ImageView ivQrCode;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvDeviceId;

  @NonNull
  public final TextView tvExpireTime;

  @NonNull
  public final TextView tvInstructions;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTitle;

  private ActivityDeviceRegistrationBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnRefresh, @NonNull ImageView ivQrCode, @NonNull ProgressBar progressBar,
      @NonNull TextView tvDeviceId, @NonNull TextView tvExpireTime,
      @NonNull TextView tvInstructions, @NonNull TextView tvStatus, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnRefresh = btnRefresh;
    this.ivQrCode = ivQrCode;
    this.progressBar = progressBar;
    this.tvDeviceId = tvDeviceId;
    this.tvExpireTime = tvExpireTime;
    this.tvInstructions = tvInstructions;
    this.tvStatus = tvStatus;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDeviceRegistrationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDeviceRegistrationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_device_registration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDeviceRegistrationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnRefresh;
      Button btnRefresh = ViewBindings.findChildViewById(rootView, id);
      if (btnRefresh == null) {
        break missingId;
      }

      id = R.id.ivQrCode;
      ImageView ivQrCode = ViewBindings.findChildViewById(rootView, id);
      if (ivQrCode == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tvDeviceId;
      TextView tvDeviceId = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceId == null) {
        break missingId;
      }

      id = R.id.tvExpireTime;
      TextView tvExpireTime = ViewBindings.findChildViewById(rootView, id);
      if (tvExpireTime == null) {
        break missingId;
      }

      id = R.id.tvInstructions;
      TextView tvInstructions = ViewBindings.findChildViewById(rootView, id);
      if (tvInstructions == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityDeviceRegistrationBinding((ConstraintLayout) rootView, btnRefresh,
          ivQrCode, progressBar, tvDeviceId, tvExpireTime, tvInstructions, tvStatus, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
