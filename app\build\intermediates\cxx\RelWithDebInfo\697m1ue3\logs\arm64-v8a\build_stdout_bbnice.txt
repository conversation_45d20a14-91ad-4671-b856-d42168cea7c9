ninja: Entering directory `D:\Workspace\Projects\BBNice\AndroidClient\app\.cxx\RelWithDebInfo\697m1ue3\arm64-v8a'
[1/6] Building C object CMakeFiles/bbnice.dir/logger.c.o
[2/6] Building CXX object CMakeFiles/bbnice.dir/BBNiceNative.cpp.o
[3/6] Building CXX object CMakeFiles/bbnice.dir/BitmapUtils.cpp.o
[4/6] Building CXX object CMakeFiles/bbnice.dir/video_as_stabilizer.cpp.o
[5/6] Building CXX object CMakeFiles/bbnice.dir/video_stabilizer.cpp.o
[6/6] Linking CXX shared library D:\Workspace\Projects\BBNice\AndroidClient\app\build\intermediates\cxx\RelWithDebInfo\697m1ue3\obj\arm64-v8a\libbbnice.so
FAILED: D:/Workspace/Projects/BBNice/AndroidClient/app/build/intermediates/cxx/RelWithDebInfo/697m1ue3/obj/arm64-v8a/libbbnice.so 
cmd.exe /C "cd . && D:\Workspace\env\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android26 --sysroot=D:/Workspace/env/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -fPIC -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG  -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections -shared -Wl,-soname,libbbnice.so -o D:\Workspace\Projects\BBNice\AndroidClient\app\build\intermediates\cxx\RelWithDebInfo\697m1ue3\obj\arm64-v8a\libbbnice.so @CMakeFiles\bbnice.rsp  && cd ."
ld.lld: error: duplicate symbol: smoothTransformation(cv::Mat const&)
>>> defined at video_as_stabilizer.cpp:90 (D:/Workspace/Projects/BBNice/AndroidClient/app/src/main/cpp/video_as_stabilizer.cpp:90)
>>>            CMakeFiles/bbnice.dir/video_as_stabilizer.cpp.o:(smoothTransformation(cv::Mat const&))
>>> defined at video_stabilizer.cpp:89 (D:/Workspace/Projects/BBNice/AndroidClient/app/src/main/cpp/video_stabilizer.cpp:89)
>>>            CMakeFiles/bbnice.dir/video_stabilizer.cpp.o:(.text._Z20smoothTransformationRKN2cv3MatE+0x0)

ld.lld: error: duplicate symbol: transformations
>>> defined at video_as_stabilizer.cpp:42 (D:/Workspace/Projects/BBNice/AndroidClient/app/src/main/cpp/video_as_stabilizer.cpp:42)
>>>            CMakeFiles/bbnice.dir/video_as_stabilizer.cpp.o:(transformations)
>>> defined at video_stabilizer.cpp:25 (D:/Workspace/Projects/BBNice/AndroidClient/app/src/main/cpp/video_stabilizer.cpp:25)
>>>            CMakeFiles/bbnice.dir/video_stabilizer.cpp.o:(.bss.transformations+0x0)
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.
