package com.srthinker.bbnice.api.repository;

import android.content.Context;
import android.graphics.Bitmap;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.ImageRecognizeResponse;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCStopResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.core.ErrorHandler;
import com.srthinker.bbnice.core.Result;

/**
 * 设备相关的Repository接口
 */
public class AIRepositoryImpl implements AIRepository {

    private final BBNiceApi api;
    private final Context context;
    // 认证Token
    private String authToken;

    // 认证Token

    /**
     * 构造函数
     * @param context 上下文
     */
    public AIRepositoryImpl(Context context) {
        this.api = BBNiceApi.getInstance(context);
        this.context = context;
    }

    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        api.setAuthToken(token);
        this.authToken = token;
    }

    @Override
    public LiveData<Result<RTCTokenResponse>> aiTokens(String roomId) {
        MutableLiveData<Result<RTCTokenResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.rtcToken(roomId, new ApiCallback<RTCTokenResponse>() {
            @Override
            public void onSuccess(RTCTokenResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });
        return result;
    }

    @Override
    public LiveData<Result<RTCStartResponse>> rtcStart(RTCStartRequestData requestData) {
        MutableLiveData<Result<RTCStartResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.rtcStart(requestData, new ApiCallback<RTCStartResponse>() {
            @Override
            public void onSuccess(RTCStartResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });
        return result;
    }

    @Override
    public LiveData<Result<RTCStopResponse>> rtcStop(String taskId, String roomId) {
        MutableLiveData<Result<RTCStopResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.rtcStop(taskId, roomId, new ApiCallback<RTCStopResponse>() {
            @Override
            public void onSuccess(RTCStopResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });
        return result;
    }

    @Override
    public LiveData<Result<ImageRecognizeResponse>> imageRecognize(Bitmap img) {
        MutableLiveData<Result<ImageRecognizeResponse>> result = new MutableLiveData<>();
        result.postValue(Result.loading());
        api.imageRecognize(img, new ApiCallback<ImageRecognizeResponse>() {
            @Override
            public void onSuccess(ImageRecognizeResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });
        return result;
    }
}
