package com.srthinker.bbnice.api.repository;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;

import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.DeviceListResponse;
import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.bean.MediaResponse;
import com.srthinker.bbnice.api.bean.PetNameResponse;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.bean.WhitelistResponse;
import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.core.Repository;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.utils.HttpUtils;

import java.io.File;
import java.util.List;

/**
 * 设备相关的Repository接口
 */
public interface DeviceRepository extends Repository {

    /**
     * 设备登录
     * @return 登录结果LiveData
     */
    LiveData<Result<LoginResponse>> login();

    /**
     * 获取设备二维码
     * @return 二维码结果LiveData
     */
    LiveData<Result<QrCodeResponse>> getDeviceQrCode();

    /**
     * 解绑设备
     * @return 解绑结果LiveData
     */
    LiveData<Result<BaseResponse>> unbindDevice();

    /**
     * 获取设备列表
     * @return 设备列表结果LiveData
     */
    LiveData<Result<DeviceListResponse>> getDeviceList();

    /**
     * 更新宠物名称
     * @param deviceId 设备ID
     * @param petName 宠物名称
     * @return 更新结果LiveData
     */
    LiveData<Result<PetNameResponse>> updatePetName(String deviceId, String petName);

    /**
     * 添加白名单
     * @param deviceId 设备ID
     * @param phone 电话号码
     * @param name 姓名
     * @return 添加结果LiveData
     */
    LiveData<Result<BaseResponse>> addPhoneWhitelist(String deviceId, String phone, String name);

    /**
     * 删除白名单
     * @param deviceId 设备ID
     * @param phone 电话号码
     * @return 删除结果LiveData
     */
    LiveData<Result<BaseResponse>> deletePhoneWhitelist(String deviceId, String phone);

    /**
     * 获取白名单列表
     * @param deviceId 设备ID
     * @return 白名单列表结果LiveData
     */
    LiveData<Result<WhitelistResponse>> getPhoneWhitelist(String deviceId);

    /**
     * 上报设备状态
     * @param chatMessages 聊天记录
     * @return 上报结果LiveData
     */
    void reportChatHistory(List<ChatMessage> chatMessages, @NonNull HttpUtils.HttpCallback callback);


    /**
     * 上报设备状态
     * @param batteryLevel 电量
     * @param networkStatus 网络状态("4G"、"5G"或"wifi")
     * @param signalStrength 信号强度（百分比）
     * @param latitude 纬度
     * @param longitude 经度
     * @param firmware_version 固件版本
     * @return 上报结果LiveData
     */
    LiveData<Result<BaseResponse>> reportDeviceStatus(int batteryLevel, String networkStatus,
                                                      int signalStrength,
                                                      double latitude, double longitude,
                                                      String firmware_version);

    /**
     * 上传媒体文件
     * @param type 媒体类型("image" 或 "video")
     * @param file 文件数据
     * @return 上传结果LiveData
     */
    LiveData<Result<MediaResponse>> uploadMedia(String type, byte[] file);

    /**
     * 上传媒体文件
     * @param type 媒体类型("image" 或 "video")
     * @param files 文件数据
     * @return 上传结果LiveData
     */
    LiveData<Result<MediaResponse>> uploadMedia(String type, File[] files);

}
