// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMessageReceivedBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivOtherAvatar;

  @NonNull
  public final TextView textViewMessageReceived;

  private ItemMessageReceivedBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView ivOtherAvatar, @NonNull TextView textViewMessageReceived) {
    this.rootView = rootView;
    this.ivOtherAvatar = ivOtherAvatar;
    this.textViewMessageReceived = textViewMessageReceived;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMessageReceivedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMessageReceivedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_message_received, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMessageReceivedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_other_avatar;
      ImageView ivOtherAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivOtherAvatar == null) {
        break missingId;
      }

      id = R.id.textViewMessageReceived;
      TextView textViewMessageReceived = ViewBindings.findChildViewById(rootView, id);
      if (textViewMessageReceived == null) {
        break missingId;
      }

      return new ItemMessageReceivedBinding((LinearLayout) rootView, ivOtherAvatar,
          textViewMessageReceived);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
