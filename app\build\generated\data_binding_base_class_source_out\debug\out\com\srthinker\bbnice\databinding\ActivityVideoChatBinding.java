// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVideoChatBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FrameLayout localViewContainer;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final FrameLayout remoteViewContainer;

  private ActivityVideoChatBinding(@NonNull ConstraintLayout rootView,
      @NonNull FrameLayout localViewContainer, @NonNull ConstraintLayout main,
      @NonNull FrameLayout remoteViewContainer) {
    this.rootView = rootView;
    this.localViewContainer = localViewContainer;
    this.main = main;
    this.remoteViewContainer = remoteViewContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVideoChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVideoChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_video_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVideoChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.local_view_container;
      FrameLayout localViewContainer = ViewBindings.findChildViewById(rootView, id);
      if (localViewContainer == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.remote_view_container;
      FrameLayout remoteViewContainer = ViewBindings.findChildViewById(rootView, id);
      if (remoteViewContainer == null) {
        break missingId;
      }

      return new ActivityVideoChatBinding((ConstraintLayout) rootView, localViewContainer, main,
          remoteViewContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
