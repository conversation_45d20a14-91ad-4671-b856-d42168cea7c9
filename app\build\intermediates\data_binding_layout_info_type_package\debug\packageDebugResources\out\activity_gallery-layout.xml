<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_gallery" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_gallery.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_gallery_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="52" endOffset="39"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="76"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="40" endOffset="42"/></Target><Target id="@+id/btn_select_mode" view="ImageButton"><Expressions/><location startLine="42" startOffset="12" endLine="49" endOffset="62"/></Target><Target id="@+id/tab_layout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="54" startOffset="4" endLine="64" endOffset="55"/></Target><Target id="@+id/view_pager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="66" startOffset="4" endLine="71" endOffset="62"/></Target><Target id="@+id/bottom_action_bar" view="LinearLayout"><Expressions/><location startLine="73" startOffset="4" endLine="95" endOffset="18"/></Target><Target id="@+id/btn_select_all" view="Button"><Expressions/><location startLine="83" startOffset="8" endLine="88" endOffset="47"/></Target><Target id="@+id/btn_delete" view="Button"><Expressions/><location startLine="90" startOffset="8" endLine="94" endOffset="52"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="97" startOffset="4" endLine="105" endOffset="51"/></Target><Target id="@+id/tv_empty" view="TextView"><Expressions/><location startLine="107" startOffset="4" endLine="117" endOffset="51"/></Target></Targets></Layout>