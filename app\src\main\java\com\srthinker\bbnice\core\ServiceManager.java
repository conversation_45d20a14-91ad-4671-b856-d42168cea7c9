package com.srthinker.bbnice.core;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.srthinker.bbnice.work.ChatServiceManager;
import com.srthinker.bbnice.work.DeviceStatusManager;
import com.srthinker.bbnice.location.LocationService;
import com.srthinker.bbnice.mqtt.MqttService;

/**
 * 服务管理器，统一管理所有服务
 */
public class ServiceManager {
    private static final String TAG = "ServiceManager";

    private static ServiceManager instance;
    private final Context context;

    private final ChatServiceManager chatServiceManager;
    private final DeviceStatusManager deviceStatusManager;

    /**
     * 私有构造函数
     * @param context 上下文
     */
    private ServiceManager(Context context) {
        this.context = context.getApplicationContext();
        this.chatServiceManager = ChatServiceManager.getInstance(context);
        this.deviceStatusManager = DeviceStatusManager.getInstance(context);
    }

    /**
     * 获取实例
     * @param context 上下文
     * @return ServiceManager实例
     */
    public static synchronized ServiceManager getInstance(Context context) {
        if (instance == null) {
            instance = new ServiceManager(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * 初始化所有服务
     */
    public void initializeServices() {
        Log.d(TAG, "初始化所有服务");

        // 启动MQTT服务
        startMqttService();

        // 启动位置服务
        startLocationService();

        // 启动聊天记录上传服务
        startChatUploadService();

        // 启动设备状态上报服务
        startDeviceStatusService();
    }

    /**
     * 启动聊天记录上传服务
     */
    public void startChatUploadService() {
        try {
            chatServiceManager.startChatUploadWorker();
            Log.d(TAG, "Chat upload service started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start chat upload service", e);
        }
    }

    /**
     * 启动MQTT服务
     */
    public void startMqttService() {
        try {
            Intent intent = new Intent(context, MqttService.class);
            context.startService(intent);
            Log.d(TAG, "MQTT service started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start MQTT service", e);
        }
    }

    /**
     * 停止MQTT服务
     */
    public void stopMqttService() {
        try {
            Intent intent = new Intent(context, MqttService.class);
            context.stopService(intent);
            Log.d(TAG, "MQTT service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop MQTT service", e);
        }
    }

    /**
     * 启动位置服务
     */
    public void startLocationService() {
        try {
            LocationService.startService(context);
            Log.d(TAG, "Location service started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start location service", e);
        }
    }

    /**
     * 停止位置服务
     */
    public void stopLocationService() {
        try {
            LocationService.stopService(context);
            Log.d(TAG, "Location service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop location service", e);
        }
    }

    /**
     * 启动设备状态上报服务
     */
    public void startDeviceStatusService() {
        try {
            deviceStatusManager.startDeviceStatusWorker();
            Log.d(TAG, "Device status service started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start device status service", e);
        }
    }

    /**
     * 停止设备状态上报服务
     */
    public void stopDeviceStatusService() {
        try {
            deviceStatusManager.stopDeviceStatusWorker();
            Log.d(TAG, "Device status service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop device status service", e);
        }
    }

    /**
     * 停止所有服务
     */
    public void stopAllServices() {
        Log.d(TAG, "停止所有服务");

        // 停止MQTT服务
        stopMqttService();

        // 停止位置服务
        stopLocationService();

        // 停止设备状态上报服务
        stopDeviceStatusService();
    }
}
