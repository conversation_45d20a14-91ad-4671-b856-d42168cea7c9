package com.srthinker.bbnice.work;

import android.content.Context;
import android.util.Log;

import androidx.work.Constraints;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.NetworkType;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;

import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.db.ChatRepository;
import com.srthinker.bbnice.common.AIServerUtils;
import com.srthinker.bbnice.common.IAIServerRequestCallback;
import com.srthinker.bbnice.utils.DeviceIdUtils;

import java.util.Calendar;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 聊天服务管理器，负责调度聊天记录上传任务
 */
public class ChatServiceManager {
    private static final String TAG = "ChatServiceManager";
    private static final String UPLOAD_WORK_NAME = "chat_upload_work";
    private static final String DAILY_UPLOAD_WORK_NAME = "chat_daily_upload_work";

    private static ChatServiceManager instance;
    private final Context context;

    private ChatServiceManager(Context context) {
        this.context = context.getApplicationContext();
    }

    public static synchronized ChatServiceManager getInstance(Context context) {
        if (instance == null) {
            instance = new ChatServiceManager(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * 启动聊天记录上传服务
     */
    public void startChatUploadWorker() {
        Log.d(TAG, "Starting chat upload service");

        // 设置约束条件：需要网络连接
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        // 创建周期性工作请求，每6小时执行一次
        PeriodicWorkRequest uploadWorkRequest = new PeriodicWorkRequest.Builder(
//                ChatUploadWorker.class, 6, TimeUnit.HOURS)
                ChatUploadWorker.class, 10, TimeUnit.SECONDS)
                .setConstraints(constraints)
                .build();

        // 调度工作
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                UPLOAD_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                uploadWorkRequest
        );


        // 调度每日上传任务
        scheduleDailyUpload();
    }

    /**
     * 调度每日上传任务，在每天凌晨2点上传前一天的聊天记录
     */
    private void scheduleDailyUpload() {
        Log.d(TAG, "Scheduling daily upload task");

        // 设置约束条件：需要网络连接
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        // 计算初始延迟
        long initialDelay = calculateInitialDelayForDailyUpload();

        // 创建周期性工作请求，每24小时执行一次
        PeriodicWorkRequest dailyUploadWorkRequest = new PeriodicWorkRequest.Builder(
                DailyChatUploadWorker.class, 24, TimeUnit.HOURS)
                .setConstraints(constraints)
                .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
                .build();

        // 调度工作
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                DAILY_UPLOAD_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                dailyUploadWorkRequest
        );
    }

    /**
     * 计算到凌晨2点的延迟时间
     * @return 延迟时间（毫秒）
     */
    private long calculateInitialDelayForDailyUpload() {
        Calendar calendar = Calendar.getInstance();
        Calendar targetTime = Calendar.getInstance();

        // 设置目标时间为今天凌晨2点
        targetTime.set(Calendar.HOUR_OF_DAY, 2);
        targetTime.set(Calendar.MINUTE, 0);
        targetTime.set(Calendar.SECOND, 0);
        targetTime.set(Calendar.MILLISECOND, 0);

        // 如果当前时间已经过了今天的凌晨2点，则目标时间设为明天凌晨2点
        if (calendar.after(targetTime)) {
            targetTime.add(Calendar.DAY_OF_MONTH, 1);
        }

        return targetTime.getTimeInMillis() - calendar.getTimeInMillis();
    }

    /**
     * 立即上传昨天的聊天记录
     */
    public void uploadYesterdayChatHistory() {
        new Thread(() -> {
            Log.d(TAG, "Uploading yesterday's chat history");

            ChatRepository repository = ChatRepository.getInstance(context);
            String deviceId = DeviceIdUtils.getDeviceId(context);

            // 获取昨天的消息
            List<ChatMessage> yesterdayMessages = repository.getYesterdayMessages();

            if (yesterdayMessages.isEmpty()) {
                Log.d(TAG, "No messages from yesterday to upload");
                return;
            }

            Log.d(TAG, "Found " + yesterdayMessages.size() + " messages from yesterday");

            // 上传消息
            AIServerUtils.getInstance().uploadChatHistory(deviceId, yesterdayMessages, new IAIServerRequestCallback() {
                @Override
                public void onResult(boolean success, String result, String err) {
                    if (success) {
                        Log.d(TAG, "Yesterday's chat history upload successful: " + result);

                        // 标记消息为已上传
                        for (ChatMessage message : yesterdayMessages) {
                            repository.markMessageAsUploadedAsync(message.getMessageId());
                        }
                    } else {
                        Log.e(TAG, "Yesterday's chat history upload failed: " + err);
                    }
                }
            });
        }).start();
    }
}
