package com.srthinker.bbnice.chat;

import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.FrameLayout;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.common.GlobalRtcVideo;
import com.srthinker.bbnice.core.Result;
import com.ss.bytertc.engine.RTCRoom;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.RTCVideo;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.VideoCanvas;
import com.ss.bytertc.engine.data.RemoteStreamKey;
import com.ss.bytertc.engine.data.StreamIndex;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.handler.IRTCVideoEventHandler;
import com.ss.bytertc.engine.type.ChannelProfile;
import com.ss.bytertc.engine.type.MediaStreamType;
import com.ss.bytertc.engine.type.RTCRoomStats;
import com.ss.bytertc.engine.type.StreamRemoveReason;

import java.util.Random;

public class VideoChatActivity extends AppCompatActivity {
    public static final String TAG = "VideoChatActivity";
    private FrameLayout localViewContainer;
    private FrameLayout remoteViewContainer;
    private String roomId;
    private String localUid;
    private RTCRoom rtcRoom;
    private RTCVideo rtcVideo;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_video_chat);

        localViewContainer = findViewById(R.id.local_view_container);
        remoteViewContainer = findViewById(R.id.remote_view_container);


        rtcVideo = GlobalRtcVideo.getInstance().rtcVideo();
        rtcVideo.startVideoCapture();

        // Step3: 设置本地视频渲染窗口
        TextureView localTextureView = new TextureView(this);
        localViewContainer.removeAllViews();
        localViewContainer.addView(localTextureView);

        VideoCanvas videoCanvas = new VideoCanvas();
        videoCanvas.renderView = localTextureView;
        videoCanvas.renderMode = VideoCanvas.RENDER_MODE_HIDDEN;
        // 设置本地视频渲染视图
        rtcVideo.setLocalVideoCanvas(StreamIndex.STREAM_INDEX_MAIN, videoCanvas);

        roomId = "srt666";
        localUid = "user_" + new Random().nextInt(1000);
        rtcRoom = rtcVideo.createRTCRoom(roomId);
        rtcRoom.setRTCRoomEventHandler(rtcRoomEventHandler);
        ApiRepositoryProvider.getInstance(this).getAiRepository().aiTokens(roomId).observe(this, new Observer<Result<RTCTokenResponse>>() {
            @Override
            public void onChanged(Result<RTCTokenResponse> rtcTokenResponseResult) {
                if (rtcTokenResponseResult.isSuccess()) {
                    RTCTokenResponse data = rtcTokenResponseResult.getData();
                    String token = data.getData().getToken();
                    // 用户信息
                    UserInfo userInfo = new UserInfo(localUid, "");
                    // 设置房间配置
                    boolean isAutoPublish = true;
                    boolean isAutoSubscribeAudio = true;
                    boolean isAutoSubscribeVideo = true;
                    RTCRoomConfig roomConfig = new RTCRoomConfig(ChannelProfile.CHANNEL_PROFILE_CHAT_ROOM, isAutoPublish, isAutoSubscribeAudio, isAutoSubscribeVideo);
                    // 加入房间
                    rtcRoom.joinRoom(token, userInfo, roomConfig);
                }
            }
        });
    }


    IRTCRoomEventHandler rtcRoomEventHandler = new IRTCRoomEventHandler() {
        @Override
        public void onRoomStateChanged(String roomId, String uid, int state, String extraInfo) {
            super.onRoomStateChanged(roomId, uid, state, extraInfo);
            String info = String.format("roomId:%s, uid:%s, state:%d, extraInfo:%s", roomId, uid, state, extraInfo);
            Log.d(TAG, "onRoomStateChanged: " + info);
        }

        @Override
        public void onUserPublishStream(String uid, MediaStreamType type) {
            super.onUserPublishStream(uid, type);
            runOnUiThread(() -> {
                // 设置远端视频渲染视图
                setRemoteRenderView(uid);
            });
        }

        @Override
        public void onUserUnpublishStream(String uid, MediaStreamType type, StreamRemoveReason reason) {
            super.onUserUnpublishStream(uid, type, reason);
            runOnUiThread(() -> {
                // 解除远端视频渲染视图绑定
                removeRemoteView(uid);
            });
        }

        @Override
        public void onLeaveRoom(RTCRoomStats stats) {
            super.onLeaveRoom(stats);
            Log.d(TAG, "onLeaveRoom: " + "onLeaveRoom, stats:" + stats.toString());
        }

        @Override
        public void onTokenWillExpire() {
            super.onTokenWillExpire();
            Log.d(TAG, "onTokenWillExpire");
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (rtcRoom != null) {
            rtcRoom.destroy();
            rtcRoom = null;
        }

    }

    private void setRemoteRenderView(String uid) {
        TextureView remoteTextureView = new TextureView(this);
        remoteViewContainer.removeAllViews();
        remoteViewContainer.addView(remoteTextureView);
        VideoCanvas videoCanvas = new VideoCanvas();
        videoCanvas.renderView = remoteTextureView;
        videoCanvas.renderMode = VideoCanvas.RENDER_MODE_HIDDEN;

        RemoteStreamKey remoteStreamKey = new RemoteStreamKey(roomId, uid, StreamIndex.STREAM_INDEX_MAIN);
        // 设置远端视频渲染视图
        rtcVideo.setRemoteVideoCanvas(remoteStreamKey, videoCanvas);
    }

    private void removeRemoteView(String uid) {
        RemoteStreamKey remoteStreamKey = new RemoteStreamKey(roomId, uid, StreamIndex.STREAM_INDEX_MAIN);
        rtcVideo.setRemoteVideoCanvas(remoteStreamKey, null);
    }
}