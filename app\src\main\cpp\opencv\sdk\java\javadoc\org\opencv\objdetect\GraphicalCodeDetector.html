<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>GraphicalCodeDetector (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: GraphicalCodeDetector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class GraphicalCodeDetector" class="title">Class GraphicalCodeDetector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.GraphicalCodeDetector</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></code>, <code><a href="QRCodeDetector.html" title="class in org.opencv.objdetect">QRCodeDetector</a></code>, <code><a href="QRCodeDetectorAruco.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">GraphicalCodeDetector</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#decode(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">decode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes graphical code in image once it's found by the detect() method.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#decode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">decode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_code)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes graphical code in image once it's found by the detect() method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List)" class="member-name-link">decodeMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes graphical codes in image once it's found by the detect() method.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,java.util.List)" class="member-name-link">decodeMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_code)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes graphical codes in image once it's found by the detect() method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects graphical code in image and returns the quadrangle containing the code.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecode(org.opencv.core.Mat)" class="member-name-link">detectAndDecode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical code</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detectAndDecode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical code</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detectAndDecode</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_code)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical code</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List)" class="member-name-link">detectAndDecodeMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical codes</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)" class="member-name-link">detectAndDecodeMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical codes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)" class="member-name-link">detectAndDecodeMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_code)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Both detects and decodes graphical codes</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detectMulti(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detectMulti</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects graphical codes in image and returns the vector of the quadrangles containing the codes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Detects graphical code in image and returns the quadrangle containing the code.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing (or not) graphical code.</dd>
<dd><code>points</code> - Output vector of vertices of the minimum-area quadrangle containing the code.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>decode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">decode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_code)</span></div>
<div class="block">Decodes graphical code in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dd><code>straight_code</code> - The optional output image containing binarized code, will be empty if not found.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decode(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>decode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">decode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Decodes graphical code in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detectAndDecode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">detectAndDecode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_code)</span></div>
<div class="block">Both detects and decodes graphical code</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical code.</dd>
<dd><code>points</code> - optional output array of vertices of the found graphical code quadrangle, will be empty if not found.</dd>
<dd><code>straight_code</code> - The optional output image containing binarized code</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecode(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detectAndDecode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">detectAndDecode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Both detects and decodes graphical code</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical code.</dd>
<dd><code>points</code> - optional output array of vertices of the found graphical code quadrangle, will be empty if not found.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecode(org.opencv.core.Mat)">
<h3>detectAndDecode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">detectAndDecode</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</span></div>
<div class="block">Both detects and decodes graphical code</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical code.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectMulti(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detectMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Detects graphical codes in image and returns the vector of the quadrangles containing the codes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing (or not) graphical codes.</dd>
<dd><code>points</code> - Output vector of vector of vertices of the minimum-area quadrangle containing the codes.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List,java.util.List)">
<h3>decodeMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">decodeMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_code)</span></div>
<div class="block">Decodes graphical codes in image once it's found by the detect() method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - vector of Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dd><code>straight_code</code> - The optional output vector of images containing binarized codes</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decodeMulti(org.opencv.core.Mat,org.opencv.core.Mat,java.util.List)">
<h3>decodeMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">decodeMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info)</span></div>
<div class="block">Decodes graphical codes in image once it's found by the detect() method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - vector of Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat,java.util.List)">
<h3>detectAndDecodeMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectAndDecodeMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_code)</span></div>
<div class="block">Both detects and decodes graphical codes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - optional output vector of vertices of the found graphical code quadrangles. Will be empty if not found.</dd>
<dd><code>straight_code</code> - The optional vector of images containing binarized codes

 <ul>
   <li>
      If there are QR codes encoded with a Structured Append mode on the image and all of them detected and decoded correctly,
     method writes a full message to position corresponds to 0-th code in a sequence. The rest of QR codes from the same sequence
     have empty string.
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecodeMulti(org.opencv.core.Mat,java.util.List,org.opencv.core.Mat)">
<h3>detectAndDecodeMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectAndDecodeMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</span></div>
<div class="block">Both detects and decodes graphical codes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - optional output vector of vertices of the found graphical code quadrangles. Will be empty if not found.

 <ul>
   <li>
      If there are QR codes encoded with a Structured Append mode on the image and all of them detected and decoded correctly,
     method writes a full message to position corresponds to 0-th code in a sequence. The rest of QR codes from the same sequence
     have empty string.
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detectAndDecodeMulti(org.opencv.core.Mat,java.util.List)">
<h3>detectAndDecodeMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">detectAndDecodeMulti</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;decoded_info)</span></div>
<div class="block">Both detects and decodes graphical codes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>img</code> - grayscale or color (BGR) image containing graphical codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.

 <ul>
   <li>
      If there are QR codes encoded with a Structured Append mode on the image and all of them detected and decoded correctly,
     method writes a full message to position corresponds to 0-th code in a sequence. The rest of QR codes from the same sequence
     have empty string.
   </li>
 </ul></dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
