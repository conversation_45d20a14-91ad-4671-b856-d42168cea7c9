package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * RTC Token响应类
 * 用于表示RTC Token API的响应
 */
public class RTCTokenResponse extends BaseResponse {
    // 响应数据
    private final RTCStartResponseData data;

    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public RTCTokenResponse(ApiResponse<RTCStartResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public RTCStartResponseData getData() {
        return data;
    }

    @Override
    public String toString() {
        return "RTCTokenResponse{" +
                "data=" + data +
                '}';
    }

    /**
     * RTC Token响应数据类
     */
    public static class RTCStartResponseData {
        // 状态
        private String token;


        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }


        @Override
        public String toString() {
            return "RTCStartResponseData{" +
                    "token='" + token + '\'' +
                    '}';
        }
    }
}
