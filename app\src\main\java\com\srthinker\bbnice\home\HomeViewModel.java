package com.srthinker.bbnice.home;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * HomeActivity的ViewModel，负责处理业务逻辑
 */
public class HomeViewModel extends ViewModel {

    private final MutableLiveData<List<HomeItemType>> itemsLiveData = new MutableLiveData<>();
    private final MutableLiveData<Boolean> loadingLiveData = new MutableLiveData<>(false);
    private final MutableLiveData<String> jniMessageLiveData = new MutableLiveData<>();

    /**
     * 获取首页项目列表的LiveData
     * @return 首页项目列表LiveData
     */
    public LiveData<List<HomeItemType>> getItems() {
        return itemsLiveData;
    }

    /**
     * 获取加载状态的LiveData
     * @return 加载状态LiveData
     */
    public LiveData<Boolean> isLoading() {
        return loadingLiveData;
    }

    /**
     * 获取JNI消息的LiveData
     * @return JNI消息LiveData
     */
    public LiveData<String> getJniMessage() {
        return jniMessageLiveData;
    }

    /**
     * 设置JNI消息
     * @param message JNI消息
     */
    public void setJniMessage(String message) {
        jniMessageLiveData.setValue(message);
    }

    /**
     * 加载首页项目列表
     */
    public void loadItems() {
        loadingLiveData.setValue(true);

        // 模拟网络延迟，实际项目中可能是从网络或数据库加载
        new Thread(() -> {
            try {
                Thread.sleep(500); // 模拟加载延迟

                List<HomeItemType> items = new ArrayList<>(Arrays.asList(HomeItemType.values()));
                itemsLiveData.postValue(items);
                loadingLiveData.postValue(false);
            } catch (InterruptedException e) {
                e.printStackTrace();
                loadingLiveData.postValue(false);
            }
        }).start();
    }
}
