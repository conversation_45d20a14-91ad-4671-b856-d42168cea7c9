// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayoutCompat rootView;

  @NonNull
  public final AppCompatButton btnChangeCam;

  @NonNull
  public final AppCompatButton btnJoinRoom;

  @NonNull
  public final AppCompatButton btnLeaveRoom;

  @NonNull
  public final AppCompatButton btnOpenConfig;

  @NonNull
  public final AppCompatButton btnStartChat;

  @NonNull
  public final LinearLayoutCompat main;

  private ActivityMainBinding(@NonNull LinearLayoutCompat rootView,
      @NonNull AppCompatButton btnChangeCam, @NonNull AppCompatButton btnJoinRoom,
      @NonNull AppCompatButton btnLeaveRoom, @NonNull AppCompatButton btnOpenConfig,
      @NonNull AppCompatButton btnStartChat, @NonNull LinearLayoutCompat main) {
    this.rootView = rootView;
    this.btnChangeCam = btnChangeCam;
    this.btnJoinRoom = btnJoinRoom;
    this.btnLeaveRoom = btnLeaveRoom;
    this.btnOpenConfig = btnOpenConfig;
    this.btnStartChat = btnStartChat;
    this.main = main;
  }

  @Override
  @NonNull
  public LinearLayoutCompat getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_Change_Cam;
      AppCompatButton btnChangeCam = ViewBindings.findChildViewById(rootView, id);
      if (btnChangeCam == null) {
        break missingId;
      }

      id = R.id.btn_join_room;
      AppCompatButton btnJoinRoom = ViewBindings.findChildViewById(rootView, id);
      if (btnJoinRoom == null) {
        break missingId;
      }

      id = R.id.btn_leave_room;
      AppCompatButton btnLeaveRoom = ViewBindings.findChildViewById(rootView, id);
      if (btnLeaveRoom == null) {
        break missingId;
      }

      id = R.id.btn_open_config;
      AppCompatButton btnOpenConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnOpenConfig == null) {
        break missingId;
      }

      id = R.id.btn_start_chat;
      AppCompatButton btnStartChat = ViewBindings.findChildViewById(rootView, id);
      if (btnStartChat == null) {
        break missingId;
      }

      LinearLayoutCompat main = (LinearLayoutCompat) rootView;

      return new ActivityMainBinding((LinearLayoutCompat) rootView, btnChangeCam, btnJoinRoom,
          btnLeaveRoom, btnOpenConfig, btnStartChat, main);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
