<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:orientation="horizontal"
    android:background="?attr/selectableItemBackground"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:padding="8dp">

    <ImageView
        android:id="@+id/setting_icon"
        android:layout_marginStart="20dp"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical|start"
        android:contentDescription="@string/app_name"
        android:scaleType="fitCenter"
        android:src="@mipmap/icon_about"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/setting_title"
        android:layout_marginStart="10dp"
        android:text="fuvh"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>


</LinearLayout>
