package com.srthinker.bbnice.api.bean;

import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BaseResponse;

/**
 * 登录响应类
 * 用于表示登录API的响应
 */
public class LoginResponse extends BaseResponse {
    // 响应数据
    private final LoginResponseData data;
    
    /**
     * 构造函数
     * @param apiResponse API响应
     */
    public LoginResponse(ApiResponse<LoginResponseData> apiResponse) {
        super(apiResponse);
        this.data = apiResponse.getData();
    }
    
    /**
     * 获取响应数据
     * @return 响应数据
     */
    public LoginResponseData getData() {
        return data;
    }
    
    /**
     * 登录响应数据类
     */
    public static class LoginResponseData {
        private LoginInfo login_info;

        public LoginInfo getLoginInfo() {
            return login_info;
        }

        public void setLogin_info(LoginInfo login_info) {
            this.login_info = login_info;
        }
    }

    public static class LoginInfo {
        // 设备ID
        private String device_id;

        // 访问令牌
        private String access_token;

        /**
         * 获取设备ID
         * @return 设备ID
         */
        public String getDeviceId() {
            return device_id;
        }

        /**
         * 设置设备ID
         * @param device_id 设备ID
         */
        public void setDeviceId(String device_id) {
            this.device_id = device_id;
        }

        /**
         * 获取访问令牌
         * @return 访问令牌
         */
        public String getAccessToken() {
            return access_token;
        }

        /**
         * 设置访问令牌
         * @param access_token 访问令牌
         */
        public void setAccessToken(String access_token) {
            this.access_token = access_token;
        }

        @Override
        public String toString() {
            return "LoginResponseData{" +
                    "device_id='" + device_id + '\'' +
                    ", access_token='" + access_token + '\'' +
                    '}';
        }
    }
}
