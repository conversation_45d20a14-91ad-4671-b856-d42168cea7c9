package com.srthinker.bbnice;

import android.Manifest;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.core.ServiceManager;
import com.srthinker.bbnice.utils.LocaleHelper;
import com.srthinker.bbnice.utils.PermissionManager;

/**
 * Created by yzy on 2025/4/11.
 */
public class App extends Application {
    private static final String TAG = "BBNiceApp";
    private static App sInstance;
    private volatile Handler mainHandler;

    public static App getInstance() {
        return sInstance;
    }

    /**
     * 检查是否已授予所有必需的权限
     * @return 是否已授予所有权限
     */
    public boolean hasAllRequiredPermissions() {
        return PermissionManager.hasAllRequiredPermissions(this);
    }

    /**
     * 设置权限状态
     * @param granted 是否已授予所有权限
     */
    public void setPermissionsGranted(boolean granted) {
        // 如果权限已授予，初始化需要权限的功能
        if (granted) {
            initializeWithPermissions();
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(LocaleHelper.setLocale(base, LocaleHelper.getLanguage(base)));
    }

    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;
        mainHandler = new Handler(Looper.getMainLooper());

        // 设置全局异常处理器，处理RTC SDK的已知问题
        setupGlobalExceptionHandler();

        // 初始化RepositoryProvider
        ApiRepositoryProvider.getInstance(this);

        // 根据Android版本添加所需权限
        externPermission();

        // 检查权限
        if (hasAllRequiredPermissions()) {
            // 如果已经有所有权限，直接初始化
            setPermissionsGranted(true);
        }
        // 注意：如果没有所有权限，将在Activity中请求权限，
        // 然后通过setPermissionsGranted方法通知App类
    }

    private static void externPermission() {
        int sdkVersion = Build.VERSION.SDK_INT;
        if (sdkVersion <= Build.VERSION_CODES.Q) {  // Android 10及以下
            // 存储权限 (Android 10及以下需要)
            PermissionManager.addPermission(Manifest.permission.READ_EXTERNAL_STORAGE);
            PermissionManager.addPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }

        if (sdkVersion >= Build.VERSION_CODES.S) { // Android 12及以上
            // 蓝牙权限 (Android 12及以上需要)
            PermissionManager.addPermission(Manifest.permission.BLUETOOTH_SCAN);
            PermissionManager.addPermission(Manifest.permission.BLUETOOTH_CONNECT);
        }

        if (sdkVersion >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14 (API 34)
            // 前台服务位置权限 (Android 14及以上需要)
            PermissionManager.addPermission(Manifest.permission.FOREGROUND_SERVICE_LOCATION);
            // 前台服务数据同步权限 (Android 14及以上需要)
            PermissionManager.addPermission(Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC);
        }
    }

    /**
     * 初始化需要权限的功能
     */
    private void initializeWithPermissions() {
        Log.d(TAG, "初始化需要权限的功能");

        // 使用ServiceManager初始化所有服务
        ServiceManager.getInstance(this).initializeServices();
    }

    /**
     * 设置全局异常处理器，处理RTC SDK的已知问题
     */
    private void setupGlobalExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            private final Thread.UncaughtExceptionHandler defaultHandler =
                    Thread.getDefaultUncaughtExceptionHandler();

            @Override
            public void uncaughtException(Thread thread, Throwable throwable) {
                // 检查是否是RTC SDK的SmartModeStatus异常
                if (isRtcSdkKnownIssue(throwable)) {
                    Log.w(TAG, "Caught known RTC SDK issue: " + throwable.getMessage());
                    // 忽略这个异常，不让应用崩溃
                    return;
                }

                // 对于其他异常，使用默认处理器
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, throwable);
                }
            }
        });
    }

    /**
     * 检查是否是RTC SDK的已知问题
     */
    private boolean isRtcSdkKnownIssue(Throwable throwable) {
        if (throwable == null) return false;

        String message = throwable.getMessage();
        String stackTrace = Log.getStackTraceString(throwable);

        // 检查SmartModeStatus异常
        if (throwable instanceof android.provider.Settings.SettingNotFoundException) {
            return message != null && message.contains("SmartModeStatus");
        }

        // 检查是否来自RTC SDK的电源监控模块
        if (stackTrace.contains("RXPowerMonitorAndroid.getBatterySaveStatus") ||
            stackTrace.contains("com.bytedance.realx.base.RXPowerMonitorAndroid")) {
            return true;
        }

        return false;
    }

    public void runOnUiThread(Runnable runnable) {
        mainHandler.post(runnable);
    }

    @Override
    public void onTerminate() {
        // 停止所有服务
        ServiceManager.getInstance(this).stopAllServices();

        super.onTerminate();
    }
}
