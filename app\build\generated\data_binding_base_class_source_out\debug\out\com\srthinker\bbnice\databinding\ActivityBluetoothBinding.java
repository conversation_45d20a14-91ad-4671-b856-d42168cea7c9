// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBluetoothBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout bluetoothStatusContainer;

  @NonNull
  public final View divider;

  @NonNull
  public final View divider2;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvAvailableDevices;

  @NonNull
  public final RecyclerView rvPairedDevices;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final SwitchCompat switchBluetooth;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAvailableDevices;

  @NonNull
  public final TextView tvBluetoothStatusLabel;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvPairedDevices;

  private ActivityBluetoothBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout bluetoothStatusContainer, @NonNull View divider,
      @NonNull View divider2, @NonNull ConstraintLayout main, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView rvAvailableDevices, @NonNull RecyclerView rvPairedDevices,
      @NonNull SwipeRefreshLayout swipeRefresh, @NonNull SwitchCompat switchBluetooth,
      @NonNull Toolbar toolbar, @NonNull TextView tvAvailableDevices,
      @NonNull TextView tvBluetoothStatusLabel, @NonNull TextView tvEmptyState,
      @NonNull TextView tvPairedDevices) {
    this.rootView = rootView;
    this.bluetoothStatusContainer = bluetoothStatusContainer;
    this.divider = divider;
    this.divider2 = divider2;
    this.main = main;
    this.progressBar = progressBar;
    this.rvAvailableDevices = rvAvailableDevices;
    this.rvPairedDevices = rvPairedDevices;
    this.swipeRefresh = swipeRefresh;
    this.switchBluetooth = switchBluetooth;
    this.toolbar = toolbar;
    this.tvAvailableDevices = tvAvailableDevices;
    this.tvBluetoothStatusLabel = tvBluetoothStatusLabel;
    this.tvEmptyState = tvEmptyState;
    this.tvPairedDevices = tvPairedDevices;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBluetoothBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBluetoothBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_bluetooth, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBluetoothBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bluetooth_status_container;
      ConstraintLayout bluetoothStatusContainer = ViewBindings.findChildViewById(rootView, id);
      if (bluetoothStatusContainer == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.divider2;
      View divider2 = ViewBindings.findChildViewById(rootView, id);
      if (divider2 == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_available_devices;
      RecyclerView rvAvailableDevices = ViewBindings.findChildViewById(rootView, id);
      if (rvAvailableDevices == null) {
        break missingId;
      }

      id = R.id.rv_paired_devices;
      RecyclerView rvPairedDevices = ViewBindings.findChildViewById(rootView, id);
      if (rvPairedDevices == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.switch_bluetooth;
      SwitchCompat switchBluetooth = ViewBindings.findChildViewById(rootView, id);
      if (switchBluetooth == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_available_devices;
      TextView tvAvailableDevices = ViewBindings.findChildViewById(rootView, id);
      if (tvAvailableDevices == null) {
        break missingId;
      }

      id = R.id.tv_bluetooth_status_label;
      TextView tvBluetoothStatusLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvBluetoothStatusLabel == null) {
        break missingId;
      }

      id = R.id.tv_empty_state;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_paired_devices;
      TextView tvPairedDevices = ViewBindings.findChildViewById(rootView, id);
      if (tvPairedDevices == null) {
        break missingId;
      }

      return new ActivityBluetoothBinding((ConstraintLayout) rootView, bluetoothStatusContainer,
          divider, divider2, main, progressBar, rvAvailableDevices, rvPairedDevices, swipeRefresh,
          switchBluetooth, toolbar, tvAvailableDevices, tvBluetoothStatusLabel, tvEmptyState,
          tvPairedDevices);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
