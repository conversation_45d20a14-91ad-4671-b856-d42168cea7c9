package com.srthinker.bbnice.chat;

import android.annotation.SuppressLint;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ChatReportMessage {
    @SuppressLint("ConstantLocale")
    private static final SimpleDateFormat stdDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

    public String chat_type;
    public long start_time;
    public long end_time;

    public long duration;
    public List<ContentData> chat_content;

    public ChatReportMessage(String chat_type, List<ContentData> chat_content) {
        this.chat_type = chat_type;
        this.chat_content = chat_content;
    }

    public static class ContentData {
        public String role;
        public String content;
        public String time;

        public ContentData(String role, String content, long timestamp) {
            this.role = role;
            this.content = content;
            this.time = stdDateFormat.format(new Date(timestamp));
        }
    }
}
