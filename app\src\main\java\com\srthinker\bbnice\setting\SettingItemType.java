package com.srthinker.bbnice.setting;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.call.CallActivity;
import com.srthinker.bbnice.capture.CaptureActivity;
import com.srthinker.bbnice.chat.ChatActivity;
import com.srthinker.bbnice.learn.LearnActivity;
import com.srthinker.bbnice.recognition.RecognitionActivity;
import com.srthinker.bbnice.registration.DeviceRegistrationActivity;

public enum SettingItemType {

    WIFI(R.mipmap.iocn_wifi, "Wi-Fi"),

    Net(R.mipmap.icon_4g, "移动网络"),
    Bluetooth(R.mipmap.icon_bluetooth, "蓝牙"),

    Location(R.mipmap.icon_location, "定位"),
    Display(R.mipmap.icon_location, "显示"),
    Voice(R.mipmap.icon_voice, "声音"),
    Shutdown(R.mipmap.icon_shutdwon, "关机"),
    Bind(R.mipmap.icon_bind, "绑定设备"),
    About(R.mipmap.icon_about, "关于本机"),
    Private(R.mipmap.icon_private, "隐私协议"),
    Resume(R.mipmap.icon_resume, "恢复出厂"),
    ;

    private final int iconResId;
    private final String title;

    SettingItemType(int iconResId,String title) {
        this.iconResId = iconResId;
        this.title = title;
    }

    public int getIconResId() {
        return iconResId;
    }

    public String getTitle() {
        return title;
    }
}
