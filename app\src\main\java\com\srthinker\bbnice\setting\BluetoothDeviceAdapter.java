package com.srthinker.bbnice.setting;

import android.bluetooth.BluetoothDevice;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;

import java.util.ArrayList;
import java.util.List;

public class BluetoothDeviceAdapter extends RecyclerView.Adapter<BluetoothDeviceAdapter.DeviceViewHolder> {

    private List<BluetoothDevice> deviceList = new ArrayList<>();
    private List<BluetoothDevice> connectedDevices = new ArrayList<>();
    private OnDeviceClickListener listener;
    private boolean isPairedList;

    public interface OnDeviceClickListener {
        void onDeviceClick(BluetoothDevice device);
    }

    public BluetoothDeviceAdapter(OnDeviceClickListener listener, boolean isPairedList) {
        this.listener = listener;
        this.isPairedList = isPairedList;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_bluetooth, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        BluetoothDevice device = deviceList.get(position);
        holder.bind(device);
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    public void updateDeviceList(List<BluetoothDevice> deviceList, List<BluetoothDevice> connectedDevices) {
        this.deviceList = new ArrayList<>(deviceList);
        this.connectedDevices = new ArrayList<>(connectedDevices);
        notifyDataSetChanged();
    }

    public void addDevice(BluetoothDevice device) {
        if (!deviceList.contains(device)) {
            deviceList.add(device);
            notifyItemInserted(deviceList.size() - 1);
        }
    }

    public void clearDevices() {
        deviceList.clear();
        notifyDataSetChanged();
    }

    class DeviceViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivBluetoothIcon;
        private final TextView tvDeviceName;
        private final TextView tvDeviceAddress;
        private final TextView tvDeviceStatus;

        public DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            ivBluetoothIcon = itemView.findViewById(R.id.iv_bluetooth_icon);
            tvDeviceName = itemView.findViewById(R.id.tv_device_name);
            tvDeviceAddress = itemView.findViewById(R.id.tv_device_address);
            tvDeviceStatus = itemView.findViewById(R.id.tv_device_status);

            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onDeviceClick(deviceList.get(position));
                }
            });
        }

        public void bind(BluetoothDevice device) {
            // 设置设备名称
            String deviceName = device.getName();
            if (deviceName == null || deviceName.isEmpty()) {
                deviceName = device.getAddress();
            }
            tvDeviceName.setText(deviceName);
            
            // 设置设备地址
            tvDeviceAddress.setText(device.getAddress());
            
            // 设置设备状态
            boolean isConnected = connectedDevices.contains(device);
            
            if (isPairedList) {
                if (isConnected) {
                    tvDeviceStatus.setText(itemView.getContext().getString(R.string.bluetooth_connected));
                    tvDeviceStatus.setTextColor(itemView.getContext().getResources().getColor(android.R.color.holo_green_dark));
                } else {
                    tvDeviceStatus.setText(itemView.getContext().getString(R.string.bluetooth_paired));
                    tvDeviceStatus.setTextColor(itemView.getContext().getResources().getColor(android.R.color.darker_gray));
                }
            } else {
                tvDeviceStatus.setText(itemView.getContext().getString(R.string.bluetooth_disconnected));
                tvDeviceStatus.setTextColor(itemView.getContext().getResources().getColor(android.R.color.darker_gray));
            }
            
            // 设置图标
            if (device.getBluetoothClass() != null) {
                int deviceClass = device.getBluetoothClass().getMajorDeviceClass();
                // 根据设备类型设置不同图标
                switch (deviceClass) {
                    case android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO:
                        ivBluetoothIcon.setImageResource(android.R.drawable.stat_sys_data_bluetooth);
                        break;
                    case android.bluetooth.BluetoothClass.Device.Major.COMPUTER:
                        ivBluetoothIcon.setImageResource(android.R.drawable.stat_sys_data_bluetooth);
                        break;
                    case android.bluetooth.BluetoothClass.Device.Major.PHONE:
                        ivBluetoothIcon.setImageResource(android.R.drawable.stat_sys_data_bluetooth);
                        break;
                    default:
                        ivBluetoothIcon.setImageResource(android.R.drawable.stat_sys_data_bluetooth);
                        break;
                }
            } else {
                ivBluetoothIcon.setImageResource(android.R.drawable.stat_sys_data_bluetooth);
            }
        }
    }
}
