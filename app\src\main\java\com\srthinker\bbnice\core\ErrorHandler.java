package com.srthinker.bbnice.core;

import android.content.Context;
import android.widget.Toast;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.ApiError;

/**
 * 统一的错误处理工具类
 */
public class ErrorHandler {
    
    /**
     * 处理API错误并转换为Result
     * @param error API错误
     * @param <T> 结果数据类型
     * @return 包含错误的Result
     */
    public static <T> Result<T> handleApiError(ApiError error) {
        return Result.error(new AppException(error.getCode(), error.getMessage()));
    }
    
    /**
     * 处理网络错误并转换为Result
     * @param errorMessage 错误消息
     * @param <T> 结果数据类型
     * @return 包含错误的Result
     */
    public static <T> Result<T> handleNetworkError(String errorMessage) {
        return Result.error(new NetworkException(errorMessage));
    }
    
    /**
     * 处理通用异常并转换为Result
     * @param throwable 异常
     * @param <T> 结果数据类型
     * @return 包含错误的Result
     */
    public static <T> Result<T> handleException(Throwable throwable) {
        if (throwable instanceof AppException || throwable instanceof NetworkException) {
            return Result.error(throwable);
        } else {
            return Result.error(new UnknownException(throwable.getMessage(), throwable));
        }
    }
    
    /**
     * 向用户显示错误消息
     * @param context 上下文
     * @param throwable 异常
     */
    public static void showError(Context context, Throwable throwable) {
        String message;
        
        if (throwable instanceof AppException) {
            message = ((AppException) throwable).getUserMessage();
        } else if (throwable instanceof NetworkException) {
            message = context.getString(R.string.error_network);
        } else {
            message = context.getString(R.string.error_unknown);
        }
        
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 应用异常基类
     */
    public static class AppException extends Exception {
        private final int code;
        
        public AppException(int code, String message) {
            super(message);
            this.code = code;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getUserMessage() {
            return getMessage();
        }
    }
    
    /**
     * 网络异常
     */
    public static class NetworkException extends Exception {
        public NetworkException(String message) {
            super(message);
        }
    }
    
    /**
     * 未知异常
     */
    public static class UnknownException extends Exception {
        public UnknownException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
