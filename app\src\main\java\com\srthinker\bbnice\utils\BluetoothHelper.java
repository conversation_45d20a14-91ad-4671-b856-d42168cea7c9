package com.srthinker.bbnice.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelUuid;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.srthinker.bbnice.R;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 蓝牙辅助类，封装蓝牙相关操作
 */
@SuppressLint("MissingPermission")
public class BluetoothHelper {
    private static final String TAG = "BluetoothHelper";
    private static final int REQUEST_ENABLE_BT = 1;
    private static final int PERMISSIONS_REQUEST_CODE = 200;

    private final Context context;
    private final BluetoothManager bluetoothManager;
    private final BluetoothAdapter bluetoothAdapter;
    private final Handler handler;

    // 蓝牙配置文件
    private BluetoothA2dp bluetoothA2dp;
    private BluetoothHeadset bluetoothHeadset;
    private BluetoothGatt bluetoothGatt;
    private BluetoothSocket bluetoothSocket;
    private BluetoothProfile.ServiceListener profileListener;

    // 设备列表
    private final Set<BluetoothDevice> pairedDevices = new HashSet<>();
    private final Set<BluetoothDevice> availableDevices = new HashSet<>();
    private final Set<BluetoothDevice> connectedDevices = new HashSet<>();

    // 状态标志
    private boolean isScanning = false;
    private boolean isConnecting = false;

    // 广播接收器
    private BroadcastReceiver bluetoothReceiver;

    // 回调接口
    private BluetoothCallback callback;

    /**
     * 蓝牙状态和事件回调接口
     */
    public interface BluetoothCallback {
        void onBluetoothStateChanged(int state);
        void onDeviceDiscovered(BluetoothDevice device);
        void onDeviceConnected(BluetoothDevice device);
        void onDeviceDisconnected(BluetoothDevice device);
        void onDevicePaired(BluetoothDevice device);
        void onDeviceUnpaired(BluetoothDevice device);
        void onScanStarted();
        void onScanFinished();
        void onError(int errorCode, String message);
    }

    /**
     * 错误代码
     */
    public static final int ERROR_BLUETOOTH_NOT_SUPPORTED = 1;
    public static final int ERROR_BLUETOOTH_DISABLED = 2;
    public static final int ERROR_PERMISSION_DENIED = 3;
    public static final int ERROR_SCAN_FAILED = 4;
    public static final int ERROR_CONNECTION_FAILED = 5;
    public static final int ERROR_PAIRING_FAILED = 6;
    public static final int ERROR_UNPAIRING_FAILED = 7;

    /**
     * 构造函数
     * @param context 上下文
     */
    public BluetoothHelper(Context context) {
        this.context = context;
        this.handler = new Handler(Looper.getMainLooper());

        // 获取蓝牙管理器和适配器
        bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager != null ? bluetoothManager.getAdapter() : null;

        // 初始化广播接收器
        initBluetoothReceiver();

        // 初始化配置文件监听器
        initProfileListener();
    }

    /**
     * 设置回调
     * @param callback 回调接口
     */
    public void setCallback(BluetoothCallback callback) {
        this.callback = callback;
    }

    /**
     * 初始化蓝牙广播接收器
     */
    private void initBluetoothReceiver() {
        bluetoothReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action == null) return;

                switch (action) {
                    case BluetoothAdapter.ACTION_STATE_CHANGED:
                        int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                        if (callback != null) {
                            callback.onBluetoothStateChanged(state);
                        }
                        break;

                    case BluetoothDevice.ACTION_FOUND:
                        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                        if (device != null && device.getName() != null && !pairedDevices.contains(device)) {
                            availableDevices.add(device);
                            if (callback != null) {
                                callback.onDeviceDiscovered(device);
                            }
                        }
                        break;

                    case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                        isScanning = true;
                        availableDevices.clear();
                        if (callback != null) {
                            callback.onScanStarted();
                        }
                        break;

                    case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                        isScanning = false;
                        if (callback != null) {
                            callback.onScanFinished();
                        }
                        break;

                    case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                        int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
                        BluetoothDevice bondDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                        if (bondDevice != null) {
                            if (bondState == BluetoothDevice.BOND_BONDED) {
                                pairedDevices.add(bondDevice);
                                if (callback != null) {
                                    callback.onDevicePaired(bondDevice);
                                }
                            } else if (bondState == BluetoothDevice.BOND_NONE) {
                                pairedDevices.remove(bondDevice);
                                if (callback != null) {
                                    callback.onDeviceUnpaired(bondDevice);
                                }
                            }
                        }
                        break;

                    // 监听连接状态变化
                    case BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED:
                    case BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED:
                        int connectionState = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED);
                        BluetoothDevice profileDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

                        if (profileDevice != null) {
                            if (connectionState == BluetoothProfile.STATE_CONNECTED) {
                                connectedDevices.add(profileDevice);
                                if (callback != null) {
                                    callback.onDeviceConnected(profileDevice);
                                }
                            } else if (connectionState == BluetoothProfile.STATE_DISCONNECTED) {
                                connectedDevices.remove(profileDevice);
                                if (callback != null) {
                                    callback.onDeviceDisconnected(profileDevice);
                                }
                            }
                        }
                        break;
                }
            }
        };
    }

    /**
     * 初始化蓝牙配置文件监听器
     */
    private void initProfileListener() {
        profileListener = new BluetoothProfile.ServiceListener() {
            @Override
            public void onServiceConnected(int profile, BluetoothProfile proxy) {
                if (profile == BluetoothProfile.A2DP) {
                    bluetoothA2dp = (BluetoothA2dp) proxy;
                    Log.d(TAG, "A2DP profile connected");
                } else if (profile == BluetoothProfile.HEADSET) {
                    bluetoothHeadset = (BluetoothHeadset) proxy;
                    Log.d(TAG, "HEADSET profile connected");
                }
            }

            @Override
            public void onServiceDisconnected(int profile) {
                if (profile == BluetoothProfile.A2DP) {
                    bluetoothA2dp = null;
                    Log.d(TAG, "A2DP profile disconnected");
                } else if (profile == BluetoothProfile.HEADSET) {
                    bluetoothHeadset = null;
                    Log.d(TAG, "HEADSET profile disconnected");
                }
            }
        };

        // 获取A2DP和HEADSET配置文件代理
        if (bluetoothAdapter != null) {
            try {
                bluetoothAdapter.getProfileProxy(context, profileListener, BluetoothProfile.A2DP);
                bluetoothAdapter.getProfileProxy(context, profileListener, BluetoothProfile.HEADSET);
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_profile_proxy, e.getMessage()));
            }
        }
    }

    /**
     * 注册蓝牙广播接收器
     */
    public void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        filter.addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED);
        filter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
        context.registerReceiver(bluetoothReceiver, filter);

        // 更新设备列表
        updateDeviceLists();
    }

    /**
     * 注销蓝牙广播接收器
     */
    public void unregisterReceiver() {
        try {
            context.unregisterReceiver(bluetoothReceiver);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "Receiver not registered: " + e.getMessage());
        }

        // 停止扫描
        if (isScanning && bluetoothAdapter != null) {
            bluetoothAdapter.cancelDiscovery();
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        // 关闭GATT连接
        if (bluetoothGatt != null) {
            bluetoothGatt.close();
            bluetoothGatt = null;
        }

        // 关闭Socket连接
        if (bluetoothSocket != null) {
            try {
                bluetoothSocket.close();
            } catch (IOException e) {
                Log.e(TAG, "关闭Socket失败: " + e.getMessage());
            }
            bluetoothSocket = null;
        }

        // 关闭配置文件代理
        if (bluetoothAdapter != null) {
            if (bluetoothA2dp != null) {
                try {
                    bluetoothAdapter.closeProfileProxy(BluetoothProfile.A2DP, bluetoothA2dp);
                } catch (Exception e) {
                    Log.e(TAG, context.getString(R.string.bluetooth_error_close_a2dp_proxy, e.getMessage()));
                }
                bluetoothA2dp = null;
            }

            if (bluetoothHeadset != null) {
                try {
                    bluetoothAdapter.closeProfileProxy(BluetoothProfile.HEADSET, bluetoothHeadset);
                } catch (Exception e) {
                    Log.e(TAG, context.getString(R.string.bluetooth_error_close_headset_proxy, e.getMessage()));
                }
                bluetoothHeadset = null;
            }
        }
    }

    /**
     * 检查蓝牙是否可用
     * @return 蓝牙是否可用
     */
    public boolean isBluetoothAvailable() {
        return bluetoothAdapter != null;
    }

    /**
     * 检查蓝牙是否已启用
     * @return 蓝牙是否已启用
     */
    public boolean isBluetoothEnabled() {
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled();
    }

    /**
     * 请求启用蓝牙
     * @param activity 活动
     */
    public void requestEnableBluetooth(Activity activity) {
        if (bluetoothAdapter != null && !bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            activity.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
        }
    }

    /**
     * 禁用蓝牙
     */
    public void disableBluetooth() {
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            bluetoothAdapter.disable();
        }
    }

    /**
     * 检查蓝牙权限
     * @return 是否已授予所有必要的权限
     */
    public boolean hasBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED &&
                   ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED;
        }
        return true;
    }

    /**
     * 请求蓝牙权限
     * @param activity 活动
     */
    public void requestBluetoothPermissions(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.requestPermissions(activity, new String[]{
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
            }, PERMISSIONS_REQUEST_CODE);
        }
    }

    /**
     * 开始扫描蓝牙设备
     * @return 是否成功开始扫描
     */
    public boolean startScan() {
        if (!isBluetoothEnabled()) {
            if (callback != null) {
                callback.onError(ERROR_BLUETOOTH_DISABLED, context.getString(R.string.bluetooth_disabled));
            }
            return false;
        }

        if (!hasBluetoothPermissions()) {
            if (callback != null) {
                callback.onError(ERROR_PERMISSION_DENIED, context.getString(R.string.bluetooth_permissions_not_granted));
            }
            return false;
        }

        // 如果正在扫描，先停止
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }

        // 开始扫描
        boolean success = bluetoothAdapter.startDiscovery();
        if (!success && callback != null) {
            callback.onError(ERROR_SCAN_FAILED, context.getString(R.string.bluetooth_scan_start_failed));
        }

        return success;
    }

    /**
     * 停止扫描蓝牙设备
     */
    public void stopScan() {
        if (bluetoothAdapter != null && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
    }

    /**
     * 更新设备列表
     */
    public void updateDeviceLists() {
        if (!isBluetoothEnabled() || !hasBluetoothPermissions()) {
            return;
        }

        // 获取已配对设备
        pairedDevices.clear();
        Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
        if (bondedDevices != null) {
            pairedDevices.addAll(bondedDevices);
        }

        // 更新已连接设备
        updateConnectedDevices();
    }

    /**
     * 更新已连接设备列表
     */
    private void updateConnectedDevices() {
        connectedDevices.clear();

        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled() || !hasBluetoothPermissions()) {
            return;
        }

        // 检查A2DP连接的设备
        if (bluetoothA2dp != null) {
            try {
                List<BluetoothDevice> a2dpDevices = bluetoothA2dp.getConnectedDevices();
                if (a2dpDevices != null) {
                    connectedDevices.addAll(a2dpDevices);
                }
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_a2dp_devices, e.getMessage()));
            }
        }

        // 检查HEADSET连接的设备
        if (bluetoothHeadset != null) {
            try {
                List<BluetoothDevice> headsetDevices = bluetoothHeadset.getConnectedDevices();
                if (headsetDevices != null) {
                    connectedDevices.addAll(headsetDevices);
                }
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_headset_devices, e.getMessage()));
            }
        }
    }

    /**
     * 获取已配对设备列表
     * @return 已配对设备列表
     */
    public List<BluetoothDevice> getPairedDevices() {
        return new ArrayList<>(pairedDevices);
    }

    /**
     * 获取可用设备列表
     * @return 可用设备列表
     */
    public List<BluetoothDevice> getAvailableDevices() {
        return new ArrayList<>(availableDevices);
    }

    /**
     * 获取已连接设备列表
     * @return 已连接设备列表
     */
    public List<BluetoothDevice> getConnectedDevices() {
        return new ArrayList<>(connectedDevices);
    }

    /**
     * 检查设备是否已连接
     * @param device 设备
     * @return 是否已连接
     */
    public boolean isDeviceConnected(BluetoothDevice device) {
        return connectedDevices.contains(device);
    }

    /**
     * 配对设备
     * @param device 要配对的设备
     * @return 是否成功发起配对
     */
    public boolean pairDevice(BluetoothDevice device) {
        if (!hasBluetoothPermissions()) {
            if (callback != null) {
                callback.onError(ERROR_PERMISSION_DENIED, context.getString(R.string.bluetooth_permissions_not_granted));
            }
            return false;
        }

        if (device.getBondState() == BluetoothDevice.BOND_BONDED) {
            // 设备已配对
            return true;
        }

        try {
            // 停止扫描以提高配对性能
            stopScan();

            // 开始配对
            boolean success = device.createBond();
            if (!success && callback != null) {
                callback.onError(ERROR_PAIRING_FAILED, context.getString(R.string.bluetooth_initiate_pairing_failed));
            }
            return success;
        } catch (Exception e) {
            Log.e(TAG, context.getString(R.string.bluetooth_error_pairing, e.getMessage()));
            if (callback != null) {
                callback.onError(ERROR_PAIRING_FAILED, context.getString(R.string.bluetooth_error_pairing, e.getMessage()));
            }
            return false;
        }
    }

    /**
     * 取消配对设备
     * @param device 要取消配对的设备
     * @return 是否成功发起取消配对
     */
    public boolean unpairDevice(BluetoothDevice device) {
        if (!hasBluetoothPermissions()) {
            if (callback != null) {
                callback.onError(ERROR_PERMISSION_DENIED, context.getString(R.string.bluetooth_permissions_not_granted));
            }
            return false;
        }

        if (device.getBondState() != BluetoothDevice.BOND_BONDED) {
            // 设备未配对
            return true;
        }

        try {
            // 使用标准方法取消配对
            boolean success = false;

            try {
                // 尝试使用标准方法
                success = (boolean) device.getClass().getMethod("removeBond").invoke(device);
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_unpairing, e.getMessage()));
            }

            if (!success && callback != null) {
                callback.onError(ERROR_UNPAIRING_FAILED, context.getString(R.string.bluetooth_unpair_failed));
            }
            return success;
        } catch (Exception e) {
            Log.e(TAG, context.getString(R.string.bluetooth_error_unpairing, e.getMessage()));
            if (callback != null) {
                callback.onError(ERROR_UNPAIRING_FAILED, context.getString(R.string.bluetooth_error_unpairing, e.getMessage()));
            }
            return false;
        }
    }

    /**
     * 连接设备
     * @param device 要连接的设备
     * @return 是否成功发起连接
     */
    public boolean connectDevice(BluetoothDevice device) {
        if (isConnecting) {
            return false;
        }

        if (!hasBluetoothPermissions()) {
            if (callback != null) {
                callback.onError(ERROR_PERMISSION_DENIED, context.getString(R.string.bluetooth_permissions_not_granted));
            }
            return false;
        }

        // 停止扫描以提高连接性能
        stopScan();

        isConnecting = true;

        // 根据设备类型选择连接方式
        int deviceClass = device.getBluetoothClass().getMajorDeviceClass();

        // 尝试使用GATT连接（适用于BLE设备）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            try {
                return connectWithGatt(device);
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
            }
        }

        // 尝试使用A2DP连接（适用于音频设备）
        if (deviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO) {
            try {
                return connectWithA2dp(device);
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
            }
        }

        // 尝试使用HEADSET连接（适用于耳机设备）
        if (deviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO ||
            deviceClass == android.bluetooth.BluetoothClass.Device.Major.PHONE) {
            try {
                return connectWithHeadset(device);
            } catch (Exception e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
            }
        }

        // 尝试使用Socket连接（通用方法）
        try {
            return connectWithSocket(device);
        } catch (Exception e) {
            Log.e(TAG, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
            isConnecting = false;
            if (callback != null) {
                callback.onError(ERROR_CONNECTION_FAILED, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
            }
            return false;
        }
    }

    /**
     * 使用GATT连接BLE设备
     * @param device 要连接的蓝牙设备
     * @return 是否成功发起连接
     */
    private boolean connectWithGatt(BluetoothDevice device) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            bluetoothGatt = device.connectGatt(context, false, new BluetoothGattCallback() {
                @Override
                public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
                    super.onConnectionStateChange(gatt, status, newState);
                    final BluetoothDevice gattDevice = gatt.getDevice();

                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        Log.d(TAG, "GATT连接成功: " + gattDevice.getName());
                        handler.post(() -> {
                            isConnecting = false;
                            connectedDevices.add(gattDevice);
                            if (callback != null) {
                                callback.onDeviceConnected(gattDevice);
                            }
                        });
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        Log.d(TAG, "GATT连接断开: " + gattDevice.getName());
                        handler.post(() -> {
                            isConnecting = false;
                            connectedDevices.remove(gattDevice);
                            if (callback != null) {
                                if (status != 0) {
                                    callback.onError(ERROR_CONNECTION_FAILED, "Failed to connect to device");
                                }
                                callback.onDeviceDisconnected(gattDevice);
                            }
                        });
                    }
                }
            });
            return true;
        } else {
            throw new UnsupportedOperationException(context.getString(R.string.bluetooth_gatt_not_supported));
        }
    }

    /**
     * 使用A2DP连接音频设备
     * @param device 要连接的蓝牙设备
     * @return 是否成功发起连接
     */
    private boolean connectWithA2dp(BluetoothDevice device) {
        if (bluetoothA2dp == null) {
            throw new IllegalStateException(context.getString(R.string.bluetooth_a2dp_profile_unavailable));
        }

        // 检查当前连接状态
        int connectionState = bluetoothA2dp.getConnectionState(device);
        if (connectionState == BluetoothProfile.STATE_CONNECTED) {
            // 已经连接
            isConnecting = false;
            return true;
        }

        // 设置连接状态监听
        // 注意：实际连接是通过系统UI或特定的制造商API发起的
        // 这里我们只是通知用户需要在系统UI中完成连接
        handler.post(() -> {
            isConnecting = false;
            if (callback != null) {
                callback.onError(ERROR_CONNECTION_FAILED,
                        context.getString(R.string.bluetooth_a2dp_connect_system_settings));
            }
        });

        return false;
    }

    /**
     * 使用HEADSET连接耳机设备
     * @param device 要连接的蓝牙设备
     * @return 是否成功发起连接
     */
    private boolean connectWithHeadset(BluetoothDevice device) {
        if (bluetoothHeadset == null) {
            throw new IllegalStateException(context.getString(R.string.bluetooth_headset_profile_unavailable));
        }

        // 检查当前连接状态
        int connectionState = bluetoothHeadset.getConnectionState(device);
        if (connectionState == BluetoothProfile.STATE_CONNECTED) {
            // 已经连接
            isConnecting = false;
            return true;
        }

        // 设置连接状态监听
        // 注意：实际连接是通过系统UI或特定的制造商API发起的
        // 这里我们只是通知用户需要在系统UI中完成连接
        handler.post(() -> {
            isConnecting = false;
            if (callback != null) {
                callback.onError(ERROR_CONNECTION_FAILED,
                        context.getString(R.string.bluetooth_headset_connect_system_settings));
            }
        });

        return false;
    }

    /**
     * 使用Socket连接通用蓝牙设备
     * @param device 要连接的蓝牙设备
     * @return 是否成功发起连接
     */
    private boolean connectWithSocket(BluetoothDevice device) throws IOException {
        // 获取设备的UUID
        UUID uuid = null;
        try {
            ParcelUuid[] uuids = device.getUuids();
            if (uuids != null && uuids.length > 0) {
                uuid = uuids[0].getUuid();
            }
        } catch (Exception e) {
            Log.e(TAG, context.getString(R.string.bluetooth_error_get_device_uuid, e.getMessage()));
        }

        // 如果无法获取设备UUID，使用通用串行端口UUID
        if (uuid == null) {
            uuid = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"); // 串行端口服务UUID
        }

        // 创建并连接Socket
        try {
            bluetoothSocket = device.createRfcommSocketToServiceRecord(uuid);

            // 在后台线程中连接，避免阻塞UI
            new Thread(() -> {
                try {
                    bluetoothSocket.connect();

                    handler.post(() -> {
                        isConnecting = false;
                        connectedDevices.add(device);
                        if (callback != null) {
                            callback.onDeviceConnected(device);
                        }
                    });
                } catch (IOException e) {
                    try {
                        bluetoothSocket.close();
                    } catch (IOException closeException) {
                        Log.e(TAG, context.getString(R.string.bluetooth_error_socket_close, closeException.getMessage()));
                    }

                    handler.post(() -> {
                        isConnecting = false;
                        if (callback != null) {
                            callback.onError(ERROR_CONNECTION_FAILED, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
                        }
                    });

                    Log.e(TAG, context.getString(R.string.bluetooth_error_connecting, e.getMessage()));
                }
            }).start();

            return true;
        } catch (IOException e) {
            isConnecting = false;
            throw e;
        }
    }

    /**
     * 断开设备连接
     * @param device 要断开连接的设备
     * @return 是否成功发起断开连接
     */
    public boolean disconnectDevice(BluetoothDevice device) {
        if (!hasBluetoothPermissions()) {
            if (callback != null) {
                callback.onError(ERROR_PERMISSION_DENIED, context.getString(R.string.bluetooth_permissions_not_granted));
            }
            return false;
        }

        // 尝试断开GATT连接
        if (bluetoothGatt != null && bluetoothGatt.getDevice().equals(device)) {
            bluetoothGatt.disconnect();
            bluetoothGatt.close();
            bluetoothGatt = null;
            connectedDevices.remove(device);
            if (callback != null) {
                callback.onDeviceDisconnected(device);
            }
            return true;
        }

        // 检查A2DP连接状态
        if (bluetoothA2dp != null && bluetoothA2dp.getConnectionState(device) == BluetoothProfile.STATE_CONNECTED) {
            // A2DP断开连接需要在系统UI中完成
            if (callback != null) {
                callback.onError(ERROR_CONNECTION_FAILED,
                        context.getString(R.string.bluetooth_a2dp_disconnect_system_settings));
            }
            return false;
        }

        // 检查HEADSET连接状态
        if (bluetoothHeadset != null && bluetoothHeadset.getConnectionState(device) == BluetoothProfile.STATE_CONNECTED) {
            // HEADSET断开连接需要在系统UI中完成
            if (callback != null) {
                callback.onError(ERROR_CONNECTION_FAILED,
                        context.getString(R.string.bluetooth_headset_disconnect_system_settings));
            }
            return false;
        }

        // 尝试断开Socket连接
        if (bluetoothSocket != null && bluetoothSocket.getRemoteDevice().equals(device)) {
            try {
                bluetoothSocket.close();
                bluetoothSocket = null;
                connectedDevices.remove(device);
                if (callback != null) {
                    callback.onDeviceDisconnected(device);
                }
                return true;
            } catch (IOException e) {
                Log.e(TAG, context.getString(R.string.bluetooth_error_disconnecting, e.getMessage()));
                if (callback != null) {
                    callback.onError(ERROR_CONNECTION_FAILED, context.getString(R.string.bluetooth_error_disconnecting, e.getMessage()));
                }
                return false;
            }
        }

        return false;
    }
}
