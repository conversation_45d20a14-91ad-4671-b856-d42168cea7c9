package com.srthinker.bbnice.chat.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.db.ChatRepository;
import com.srthinker.bbnice.db.RoomType;
import com.srthinker.bbnice.work.ChatServiceManager;
import com.srthinker.bbnice.core.Event;
import com.srthinker.bbnice.home.HomeItemType;

import java.util.ArrayList;
import java.util.List;

/**
 * 聊天界面的ViewModel
 */
public class ChatViewModel extends AndroidViewModel {
    
    private final ChatRepository chatRepository;

    // 聊天消息列表
    private final MutableLiveData<List<ChatMessage>> _chatMessages = new MutableLiveData<>(new ArrayList<>());
    public final LiveData<List<ChatMessage>> chatMessages = _chatMessages;
    
    // 加载状态
    private final MutableLiveData<Boolean> _isLoading = new MutableLiveData<>(false);
    public final LiveData<Boolean> isLoading = _isLoading;
    
    // 错误状态
    private final MutableLiveData<Event<Throwable>> _error = new MutableLiveData<>();
    public final LiveData<Event<Throwable>> error = _error;
    
    // 是否正在加载更多
    private final MutableLiveData<Boolean> _isLoadingMore = new MutableLiveData<>(false);
    public final LiveData<Boolean> isLoadingMore = _isLoadingMore;
    
    // 当前页码
    private int currentHistoryPage = 0;
    
    // 房间类型
    private RoomType roomType = RoomType.Chinese; // 默认中文聊天
    
    /**
     * 构造函数
     * @param application 应用
     */
    public ChatViewModel(@NonNull Application application) {
        super(application);
        chatRepository = ChatRepository.getInstance(application);
    }
    
    /**
     * 初始化ViewModel
     * @param systemMessage 系统消息
     */
    public void init(String systemMessage) {
        // 确定房间类型
        determineRoomType(systemMessage);
        
        // 加载历史聊天记录
        loadChatHistory();

    }
    
    /**
     * 确定当前房间类型
     * @param systemMessage 系统消息
     */
    private void determineRoomType(String systemMessage) {
        if (systemMessage != null) {
            if (systemMessage.equals(HomeItemType.Liao_CN.getSystemMessage())) {
                roomType = RoomType.Chinese;
            } else if (systemMessage.equals(HomeItemType.Liao_EN.getSystemMessage())) {
                roomType = RoomType.English;
            } else {
                // 默认中文聊天
                roomType = RoomType.Chinese;
            }
        } else {
            // 默认中文聊天
            roomType = RoomType.Chinese;
        }
    }
    
    /**
     * 加载历史聊天记录
     */
    public void loadChatHistory() {
        _isLoading.setValue(true);
        
        // 重置页码
        currentHistoryPage = 0;
        
        // 在后台线程中加载历史记录
        new Thread(() -> {
            try {
                // 加载第一页历史记录
                List<ChatMessage> historyMessages = chatRepository.getLatestMessages(roomType, 50);
                
                // 更新UI
                _chatMessages.postValue(new ArrayList<>(historyMessages));
                _isLoading.postValue(false);
            } catch (Exception e) {
                _error.postValue(new Event<>(e));
                _isLoading.postValue(false);
            }
        }).start();
    }
    
    /**
     * 加载更多历史聊天记录
     */
    public void loadMoreChatHistory() {
        if (_isLoadingMore.getValue() != null && _isLoadingMore.getValue()) {
            return;
        }
        
        _isLoadingMore.setValue(true);
        currentHistoryPage++;
        
        // 在后台线程中加载更多历史记录
        new Thread(() -> {
            try {
                // 加载下一页历史记录
                List<ChatMessage> moreHistoryMessages = chatRepository.getMessagesByPage(roomType, currentHistoryPage);
                
                if (!moreHistoryMessages.isEmpty()) {
                    // 获取当前消息列表
                    List<ChatMessage> currentMessages = _chatMessages.getValue();
                    if (currentMessages == null) {
                        currentMessages = new ArrayList<>();
                    }
                    
                    // 添加历史消息到列表开头
                    List<ChatMessage> newMessages = new ArrayList<>(moreHistoryMessages);
                    newMessages.addAll(currentMessages);
                    
                    // 更新UI
                    _chatMessages.postValue(newMessages);
                } else {
                    // 没有更多历史记录，恢复页码
                    currentHistoryPage--;
                }
                
                _isLoadingMore.postValue(false);
            } catch (Exception e) {
                _error.postValue(new Event<>(e));
                _isLoadingMore.postValue(false);
                currentHistoryPage--; // 恢复页码
            }
        }).start();
    }
    
    /**
     * 保存聊天消息
     * @param message 聊天消息
     */
    public void saveMessage(ChatMessage message) {
        if (message.getRoomType() == null) {
            message.setRoomType(roomType.getValue());
        }
        
        // 使用Room的异步方法保存消息
        chatRepository.saveMessageAsync(message);
        
        // 更新UI
        List<ChatMessage> currentMessages = _chatMessages.getValue();
        if (currentMessages == null) {
            currentMessages = new ArrayList<>();
        }
        
        // 添加消息到列表
        List<ChatMessage> newMessages = new ArrayList<>(currentMessages);
        newMessages.add(message);
        _chatMessages.setValue(newMessages);
    }
    
    /**
     * 更新消息内容
     * @param messageId 消息ID
     * @param content 新内容
     */
    public void updateMessageContent(String messageId, String content) {
        // 使用Room的异步方法更新消息
        chatRepository.updateMessageContentAsync(messageId, content);
        
        // 更新UI
        List<ChatMessage> currentMessages = _chatMessages.getValue();
        if (currentMessages != null) {
            for (int i = 0; i < currentMessages.size(); i++) {
                ChatMessage message = currentMessages.get(i);
                if (message.getMessageId().equals(messageId)) {
                    message.setContent(content);
                    _chatMessages.setValue(currentMessages);
                    break;
                }
            }
        }
    }
    

    /**
     * 清理资源
     */
    @Override
    protected void onCleared() {
        super.onCleared();
        
        // 确保所有消息都已保存
        List<ChatMessage> messages = _chatMessages.getValue();
        if (messages != null) {
            for (ChatMessage message : messages) {
                if (message.getRoomType() == null) {
                    message.setRoomType(roomType.getValue());
                    chatRepository.saveMessageAsync(message);
                }
            }
        }
    }
}
