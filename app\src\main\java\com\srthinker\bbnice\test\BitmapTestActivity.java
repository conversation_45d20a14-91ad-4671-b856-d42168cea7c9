package com.srthinker.bbnice.test;

import android.app.Activity;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.utils.BitmapCompressionTest;
import com.srthinker.bbnice.utils.BitmapUtils;

/**
 * Bitmap压缩测试Activity
 */
public class BitmapTestActivity extends Activity {
    private static final String TAG = "BitmapTestActivity";
    
    private TextView resultText;
    private Button testButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建简单的布局
        setContentView(createLayout());
        
        testButton.setOnClickListener(v -> runCompressionTest());
    }

    private int createLayout() {
        // 这里应该创建一个简单的布局，但为了简化，我们返回一个默认布局ID
        // 在实际使用中，你需要创建对应的布局文件
        return android.R.layout.activity_list_item;
    }

    private void runCompressionTest() {
        resultText.setText("Running compression test...");
        
        // 在后台线程运行测试
        new Thread(() -> {
            try {
                // 创建测试bitmap
                Bitmap testBitmap = BitmapCompressionTest.createTestBitmap(2048, 2048);
                Log.d(TAG, "Created test bitmap: 2048x2048");
                
                // 测试不同的压缩目标
                StringBuilder results = new StringBuilder();
                results.append("Compression Test Results:\n\n");
                
                int[] targetSizes = {100, 200, 500, 1000}; // KB
                
                for (int targetSize : targetSizes) {
                    long startTime = System.currentTimeMillis();
                    byte[] compressed = BitmapUtils.compressBitmap(testBitmap, targetSize);
                    long endTime = System.currentTimeMillis();
                    
                    int actualSizeKB = compressed.length / 1024;
                    boolean success = actualSizeKB <= targetSize;
                    
                    results.append("Target: ").append(targetSize).append("KB\n");
                    results.append("Actual: ").append(actualSizeKB).append("KB\n");
                    results.append("Success: ").append(success ? "✅" : "❌").append("\n");
                    results.append("Time: ").append(endTime - startTime).append("ms\n\n");
                    
                    Log.d(TAG, "Target: " + targetSize + "KB, Actual: " + actualSizeKB + "KB, Success: " + success);
                }
                
                // 回收测试bitmap
                if (!testBitmap.isRecycled()) {
                    testBitmap.recycle();
                }
                
                // 更新UI
                runOnUiThread(() -> resultText.setText(results.toString()));
                
                // 运行完整测试套件
                BitmapCompressionTest.runAllTests();
                
            } catch (Exception e) {
                Log.e(TAG, "Error during compression test", e);
                runOnUiThread(() -> resultText.setText("Test failed: " + e.getMessage()));
            }
        }).start();
    }
}
